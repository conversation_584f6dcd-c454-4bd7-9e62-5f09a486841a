package com.cpvsn.rm.clientportal.auth.clientportaluser

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.model.CapAuthException
import com.cpvsn.core.model.ResponseStatus
import com.cpvsn.core.svc.spring.Env
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.clientportal.auth.clientportaluser.api.RegisterStep2Request
import com.cpvsn.rm.core.features.auth.DeviceType
import com.cpvsn.rm.core.features.auth.GeneralUserRole
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.client_user.ClientPortalUser
import com.cpvsn.rm.core.features.portal.client_user.ClientPortalUserService
import com.cpvsn.rm.core.features.portal.client_user.ClientPortalUserSession
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.portal.common.PortalService
import com.cpvsn.rm.core.features.portal.common.payload.ClientUserRegisterPayload
import com.cpvsn.rm.core.features.portal.common.payload.GeneralUserPortalPayload
import com.cpvsn.rm.core.util.PasswordEncoders
import com.cpvsn.rm.core.util.RandomUtil
import com.cpvsn.rm.core.util.biz_error
import com.cpvsn.web.auth.AuthContext
import com.cpvsn.web.auth.TokenBasedAuthService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.security.SecureRandom

@Service
class ClientPortalUserAuthService : TokenBasedAuthService {

    private val secureRandom = SecureRandom()

    @Autowired
    private lateinit var portalService: PortalService

    @Autowired
    private lateinit var clientPortalUserService: ClientPortalUserService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    override fun authenticate(token: String): ClientPortalUser? {
        val session = ClientPortalUserSession.firstOrNull(
            ClientPortalUserSession.Query(
                session_id = token
            ), include = Includes.setOf(
                ClientPortalUserSession::client_portal_user
                    .dot(ClientPortalUser::client_contact),
                ClientPortalUserSession::client_portal_user
                    .dot(ClientPortalUser::client)
            )
        ) ?: return null
        val user = session.client_portal_user ?: return null

        if (user.is_blocked)
            throw CapAuthException(ResponseStatus.INVALID_ACCOUNT)

        user.client_contact?.let { contact ->
            if (contact.is_blocked) {
                throw CapAuthException(
                    ResponseStatus.INVALID_ACCOUNT,
                    "Client portal platform hasn't been enabled for you"
                )
            }
        }
        user.client?.let { client ->
            if (!client.enable_client_portal) {
                throw CapAuthException(
                    ResponseStatus.INVALID_ACCOUNT,
                    "Client portal platform hasn't been enabled yet for your organization"
                )
            }
        }

        return user
    }

    @Transactional
    fun register_step1(
        email: String,
        redirect_url: String?,
    ): ClientPortalUser {
        val user = create_portal_user_and_send_email(email, redirect_url)
        AuthContext.setAuthentication(user)
        return user
    }

    private fun create_portal_user_and_send_email(
        email_address: String,
        redirect_url: String?,
    ): ClientPortalUser {
        val user = clientPortalUserService.register(
            ClientPortalUser {
                this.email = email_address
            }
        )

        // create portal
        val portal = portalService.create(
            Portal {
                type = PortalType.CLIENT_USER_REGISTER
                payload_obj = ClientUserRegisterPayload(
                    id = user.id,
                    role = GeneralUserRole.CLIENT_PORTAL_USER,
                    redirect_uri = redirect_url,
                )
            }
        )

        // generate email
        val email = EmailRequestPojo.from_template_result(
            placeholderBasedEmailTemplateService.process_first_by_data(
                EmailTemplate.Query(
                    is_draft = false,
                    content_type = EmailContentType.PORTAL_CLIENT_USER_REGISTER,
                    is_system_template = true
                ),
                PlaceholderBasedModel(
                    client_portal_user = user,
                    general_user = user,
                ).apply {
                    context_params = mapOf(
                        ClientUserRegisterPayload::redirect_uri.name to redirect_url
                    )
                }
            )
        )

        val mail = portalService.get_email(portal, email)
        // send email
        mail.send_and_persist()

        return user
    }

    /**
     * step 2: sign up user when email in db
     */
    @Transactional
    fun register_step2(
        portal: Portal,
        request: RegisterStep2Request,
    ): ClientPortalUser {
        val payload = portal.get_payload<GeneralUserPortalPayload>()
        val user_id = payload.id
        val user = ClientPortalUser.get(user_id)
        val password = PasswordEncoders.BCryptEncoder.encode(request.password!!)
        Patch.fromMutator(user) {
            username = request.username
            this.password = password
            this.register_finished = true
        }.patch()
        user.idToken = create_token(user)
        AuthContext.setAuthentication(user)
        return user
    }

    @Transactional
    fun login(
        account: String,
        password: String,
    ): ClientPortalUser {
        val user = ClientPortalUser.firstOrNull(
            ClientPortalUser.Query(
                email = account
            ), include = Includes.setOf(
                ClientPortalUser::client_contact,
                ClientPortalUser::client
            )
        ) ?: throw BusinessException(ResponseStatus.ACCOUNT_DOES_NOT_EXIST)

        val encoded_pwd = user.password
            ?: throw BusinessException(ResponseStatus.INVALID_ACCOUNT)

        if (!PasswordEncoders.BCryptEncoder.matches(password, encoded_pwd))
            throw BusinessException(ResponseStatus.BAD_CREDENTIALS)

        if (user.is_blocked) throw BusinessException(ResponseStatus.INVALID_ACCOUNT)

        user.client_contact?.let { contact ->
            if (contact.is_blocked) {
                throw BusinessException(
                    ResponseStatus.INVALID_ACCOUNT,
                    "Client portal platform hasn't been enabled for you"
                )
            }
        }
        user.client?.let { client ->
            if (!client.enable_client_portal) {
                throw BusinessException(
                    ResponseStatus.INVALID_ACCOUNT,
                    "Client portal platform hasn't been enabled yet for your organization"
                )
            }
        }

        clientPortalUserService.bind_client_contact(user)
        user.idToken = create_token(user)

        AuthContext.setAuthentication(user)
        return user
    }

    private fun create_token(
        user: ClientPortalUser,
        deviceType: DeviceType = DeviceType.PC_WEB
    ): String {
        require(!user.is_new)

        // we enable SSO only in prod environment
        if (Env.current() == Env.PRODUCTION) {
            // SSO, invalidate issued token
            ClientPortalUserSession.findAll(
                ClientPortalUserSession.Query(
                    client_portal_user_id = user.id,
                    device_type = deviceType,
                )
            ).forEach { it.delete() }
        }
        val session = ClientPortalUserSession {
            session_id = RandomUtil.random_alpha_numeric(
                targetLength = 12,
                random = secureRandom
            )
            client_portal_user_id = user.id
            this.device_type = deviceType
        }.apply {
            this.save()
        }
        return session.session_id
    }

    @Transactional
    fun reset_pwd(
        id: Int,
        old_pwd: String,
        pwd: String
    ) {
        val user = ClientPortalUser.get(id)
        val encoded_pwd = user.password ?: biz_error("inactive account")
        if (!PasswordEncoders.BCryptEncoder.matches(old_pwd, encoded_pwd))
            throw BusinessException("bad credentials")
        reset_pwd(user.id, pwd)
    }

    @Transactional
    fun reset_pwd_via_portal(
        portal: Portal,
        pwd: String
    ) {
        val payload = portal.get_payload<GeneralUserPortalPayload>()
        if (payload.role != GeneralUserRole.CLIENT_PORTAL_USER) {
            throw BusinessException(ResponseStatus.FORBIDDEN)
        }
        reset_pwd(payload.id, pwd)
        portal.consume()
    }

    private fun reset_pwd(
        id: Int,
        pwd: String
    ) {
        val user = ClientPortalUser.get(id)
        validate_pwd(pwd)
        val encoded = PasswordEncoders.BCryptEncoder.encode(pwd)
        Patch.fromMutator(user) {
            password = encoded
        }.patch()
    }

    private fun validate_pwd(pwd: String) {
        // validate pwd strength?
        // for now, we leave this job to frontend
    }
}
