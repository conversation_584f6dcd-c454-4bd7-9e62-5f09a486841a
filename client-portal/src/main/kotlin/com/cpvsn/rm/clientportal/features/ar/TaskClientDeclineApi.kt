package com.cpvsn.rm.clientportal.features.ar

import com.cpvsn.rm.core.features.task.client_decline.TaskClientDecline
import com.cpvsn.rm.core.features.task.client_decline.TaskClientDeclineService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/tasks/client_decline"], produces = [(MediaType.APPLICATION_JSON_VALUE)])
class TaskClientDeclineApi {
    @Autowired
    private lateinit var service: TaskClientDeclineService

    @PatchMapping
    fun decline(
            @RequestBody decline: TaskClientDecline,
    ): TaskClientDecline {
        return service.decline(decline, send_notification = true)
    }

    @PatchMapping("/invalidate/{id}")
    fun invalidate_decline(
            @PathVariable id: Int,
    ) {
        return service.invalidate_decline(id)
    }
}