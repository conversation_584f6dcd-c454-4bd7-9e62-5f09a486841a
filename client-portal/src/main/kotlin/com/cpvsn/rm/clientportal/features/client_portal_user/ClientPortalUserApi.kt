package com.cpvsn.rm.clientportal.features.client_portal_user

import com.cpvsn.crud.model.Includes
import com.cpvsn.rm.clientportal.auth.clientportaluser.ClientPortalUserAuthService
import com.cpvsn.rm.core.base.pojo.ClientPortalJsonView
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.auth.portal.getPortal
import com.cpvsn.rm.core.features.portal.client_user.ClientPortalUser
import com.cpvsn.web.auth.AuthContext
import com.fasterxml.jackson.annotation.JsonView
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import javax.validation.constraints.NotBlank
import javax.validation.constraints.NotNull

@RestController
@RequestMapping(value = ["/client_portal/users"], produces = [(MediaType.APPLICATION_JSON_VALUE)])
class ClientPortalUserApi {

    private val logger = LoggerFactory.getLogger(this::class.java)

    //region @
    @Autowired
    private lateinit var clientPortalUserAuthService: ClientPortalUserAuthService
    //endregion

    @GetMapping("/me")
    @JsonView(ClientPortalJsonView::class)
    fun me(
            extra: Includes? = null
    ): ClientPortalUser {
        val user = AuthContext.get<ClientPortalUser>()
        ClientPortalUser.join(
                user,
                extra.orEmpty() + Includes.setOf(ClientPortalUser::client_contact)
        )
        return user
    }

    data class ResetPwdRequest(
            @NotNull
            @NotBlank
            val old_password: String? = null,
            @NotNull
            @NotBlank
            val password: String? = null,
    )

    /**
     * reset pwd of a logged in user
     */
    @PatchMapping("/reset_pwd")
    fun reset_pwd(
            @Validated @RequestBody request: ResetPwdRequest
    ) {
        val user = AuthContext.get<ClientPortalUser>()
        clientPortalUserAuthService.reset_pwd(
                user.id,
                request.old_password.orEmpty(),
                request.password.orEmpty(),
        )
    }

    data class ResetPwdByPortalRequest(
            @NotNull
            @NotBlank
            val password: String? = null,
    )

    @PatchMapping("/reset_pwd_via_portal")
    fun reset_pwd_via_portal(
            @Validated @RequestBody request: ResetPwdByPortalRequest,
    ) {
        clientPortalUserAuthService.reset_pwd_via_portal(
                AuthContext.getPortal(PortalType.RESET_PWD),
                request.password!!
        )
    }

}
