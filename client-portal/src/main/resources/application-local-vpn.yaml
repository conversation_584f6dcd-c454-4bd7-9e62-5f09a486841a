spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************
    username: qa
    password: qa@2024!
    hikari:
      type: com.zaxxer.hikari.HikariDataSource
      maximum-pool-size: 3
  #      max-life-time: 28740000
  #      idle-timeout: 28740000
#  rabbitmq:
#    host: rabbitmq.default.svc.cluster.local
#    password: mUu8al5f
#    username: admin
  rabbitmq:
    host: ************
    port: 30674
    password: mq@2021!
    username: mq
  redis:
    host: ************
    port: 31380
  flyway:
    enabled: false

es:
  host: ************
  port: 31210
  cluster_name: elasticsearch
  fingerprint: 17:9F:E7:E9:6F:8F:64:4F:63:B7:4B:B9:5D:87:CB:40:09:C2:8F:19:35:C2:06:4E:1F:FE:EB:CC:F1:01:3E:4C
  user_name: elastic
  password: ApMEKSgUvwOqJK6eZbN

logging:
  level:
    com.cpvsn.rm.core.features: debug
    org.springframework.jdbc.core:
      JdbcTemplate: DEBUG
      StatementCreatorUtils: TRACE

# ---------------------------------------------------------------------------------------------------------------------
cap.svc:
  rabbitmq:
    email:
      exchange: 'mail_us.direct'
      routing-key: 'mail_us'
    outreach-email:
      exchange: 'mail_outreach_us.direct'
      routing-key: 'mail_outreach_us'
    bulk_outreach-email:
      exchange: 'mail_batch_us.direct'
      routing-key: 'mail_batch_us'
  env:
    module: client-portal
  hashid:
    salt: 'rm-server-us'
    min-hash-length: 6

cap.bridge:
  us:
    base-url: https://qa-udb2.capvision.com/api/bridge-server-preview/api/
  cn:
    base-url: https://qa-udb2.capvision.com/api/bridge-server-preview/api/
  sea:
    base-url: https://qa-udb2.capvision.com/api/bridge-server-preview/api/

oauth2-client:
  linkedin:
    client-id: 863ecc7ahyz9a5
    client-secret: ukwwisrJL8dfc5xH
# ---------------------------------------------------------------------------------------------------------------------
com:
  cpvsn:
    portal:
      client-user-register-url: https://qa-compliance2.capvision.com/client-portal/#/register
      reset-pwd-url: https://qa-udb2.capvision.com/client-portal/#/reset-pwd
      client-portal-aes-secret-key: gTRpAzm6Pn3xbrrKG+QqNg==
    conf:
      db_domain: https://qa-udb2.capvision.com
      w9_service_base_url: http://qa.api.capvision.com/py-track1099
      locations_version: 4
      cors_origin: "*"
    es:
      indice:
        - index_enum: ADVISOR
          index_name: advisor
        - index_enum: PROJECT
          index_name: usdb_project
        - index_enum: COMPANY
          index_name: usdb_company

slack:
  enable: true
  apps:
    # test (workspace:Capvision Platform)
    - app_enum: EXPERT_ACTIVITY_BOT
      id: A03CDBR3LMQ
      slack_bot_token: ********************************************************
      slack_signing_secret: 053fcaaf2e6c947b9b8f64c8d7064246
      client_secret: 22df79cf754e6d6412efa8d2dc62d9d1

# sub account (dev)
twilio:
  enable: true
  account_email: '<EMAIL>'
  account_sid: '**********************************'
  account_auth_token: '104269e57e19f0ec42aa796ec0317bfe'
  client_dial_app_sid: 'APb318363f1b2ed7f202861ea423975032'
  client_dial_api_key: '**********************************'
  client_dial_api_secret: 'UbFiXfLWV8eYlQiAw7iWg6IXw5HxvxB1'

# <EMAIL> - Test DB
zoom:
  client-id: yxFTBjQHRmef4v4OSkCgJA
  client-secret: WjAgThTJzKBwCSDiFCJgMxK9w5GTg0vL
  account-id: NiI6koipQqCezK4k1rVhiA
  secret-token: zHDzAW-UStudJ-2h5HIukA

contact-out:
  token: 'zNUMoYOfxc7FJMQVRd5C18uE'
  url: 'https://api.contactout.com/v1'

rocket_reach:
  token: '175eb89k4d55864800cc5ba8f5316868dd0a1821'
  url: 'https://api.rocketreach.co/api/v2'

exchange-rate:
  key: ************************