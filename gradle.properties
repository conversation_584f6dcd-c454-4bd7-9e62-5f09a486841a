# https://stackoverflow.com/questions/57670510/how-to-get-rid-of-incremental-annotation-processing-requested-warning
kapt.incremental.apt=true

#nexusUrl=http://nexus.capvision.com/repository/ks_server
#nexusUserName=admin
#nexusPassword=admin123
#nexusSnapshotUrl=http://nexus.capvision.com/repository/maven-snapshots/
usNexusUrl=http://nexus.capvision.us:32080/repository/us_server
usNexusUserName=admin
usNexusPassword=nexus@2021!
usNexusSnapshotUrl=http://nexus.capvision.us:32080/repository/maven-snapshots
usLocalNexusUrl=https://nexus-us.capvision.com/repository/us_server
usLocalNexusSnapshotUrl=https://nexus-us.capvision.com/repository/maven-snapshots
usVpnNexusUrl=http://nexus.capvision.us/repository/us_server
usVpnNexusSnapshotUrl=http://nexus.capvision.us/repository/maven-snapshots

org.gradle.jvmargs=-Xmx4g -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8