server:
  port: 80

management:
  # temporarily fix heath down problem(due to elasticsearch issues)
  # https://docs.spring.io/spring-boot/docs/2.2.0.BUILD-SNAPSHOT/reference/html/production-ready-features.html#auto-configured-healthindicators
  health:
    defaults.enabled: false
  # debug actuator status
  endpoint:
    health:
      show-details: always

spring:
  profiles:
    active: ${ENV}

logging:
  level:
    serviceStatsLog: warn
  path: /data/logs/rm_server

com:
  cpvsn:
    env:
      module: PORTAL

log-aspect:
  enabled: false
