spring:
  rabbitmq:
    host: rabbitmq
    password: mq@2021!
    username: mq
  redis:
    host: redis-master
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: jdbc:mysql://************:3306/us_cn_bridge?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
    username: udb_aws
    password: DrP9RT03l6tNm5GfBoE=
    hikari:
      type: com.zaxxer.hikari.HikariDataSource
      leak-detection-threshold: 3000
      maximum-pool-size: 3
#      max-life-time: 28740000
#      idle-timeout: 28740000
  flyway:
    enabled: true

es:
  host: elasticsearch.es8-17.svc.cluster.local
  port: 9200
  cluster_name: elasticsearch
  fingerprint: 09:48:D8:8C:F0:45:29:53:CC:FF:C2:EA:8A:A9:81:18:0C:B7:43:23:73:F0:8F:50:97:64:59:0A:8B:33:B3:65
  user_name: elastic
  password: YjNDLTgZxzRaSZ2pUaM

# ---------------------------------------------------------------------------------------------------------------------

com:
  cpvsn:
    portal:
      advisorPreCallUrl: https://compliance2.capvision.com/portal/#/b
      advisorPostCallUrl: https://compliance2.capvision.com/portal/#/after-task
      contactPreCallUrl: https://compliance2.capvision.com/portal/#/pick
      contactPostCallUrl: https://compliance2.capvision.com/portal/#/afeedback
      clientComplianceLoginUrl: https://compliance2.capvision.com/portal
      clientComplianceResetPwdUrl: https://compliance2.capvision.com/portal/#/reset-pwd
    conf:
      db_domain: https://db2.capvision.com
      locations_version: 4
      cors_origin: https://db2.capvision.com
    es:
      indice:
        - index_enum: ADVISOR
          index_name: advisor
        - index_enum: PROJECT
          index_name: usdb_project
        - index_enum: COMPANY
          index_name: usdb_company
springdoc:
  api-docs:
    enabled: false

py-uploader:
  host: 'https://api-db2.capvision.com/uploader'

log-aspect:
  enabled: true
  module: bridge
  es:
    url: 'elasticsearch-master.log:9200'
    index-template-name-prefix: rm-server
    index-template-version: 0.0.1

contact-out:
  token: 'zNUMoYOfxc7FJMQVRd5C18uE'
  url: 'https://api.contactout.com/v1'

rocket_reach:
  token: '175eb89k4d55864800cc5ba8f5316868dd0a1821'
  url: 'https://api.rocketreach.co/api/v2'
exchange-rate:
  key: ************************