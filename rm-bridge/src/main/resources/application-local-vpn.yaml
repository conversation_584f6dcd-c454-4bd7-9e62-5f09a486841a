spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: qa@2024!
    url: jdbc:mysql://************:3306/us_cn_bridge_qa?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
    username: qa
    hikari:
      type: com.zaxxer.hikari.HikariDataSource
      leak-detection-threshold: 3000
  #      max-life-time: 28740000
  #      idle-timeout: 28740000
  rabbitmq:
    host: ************
    port: 30674
    password: mq@2021!
    username: mq
  redis:
    host: ************
    port: 31380

es:
  host: ************
  port: 31210
  cluster_name: elasticsearch
  fingerprint: 17:9F:E7:E9:6F:8F:64:4F:63:B7:4B:B9:5D:87:CB:40:09:C2:8F:19:35:C2:06:4E:1F:FE:EB:CC:F1:01:3E:4C
  user_name: elastic
  password: ApMEKSgUvwOqJK6eZbN

logging:
  level:
    com.cpvsn.rm.ndb.features: debug
# ---------------------------------------------------------------------------------------------------------------------

auth:
  jwt:
    iss: us-db
    secret: JrvSR/LoyA9jzc7rE82wuWc6YPw7u/a34ctAAPbG3dQ=

oauth2-client:
  facebook:
    client-id: 602030593862496
    client-secret: fbacc45af4b5c524ad939221e1afdc95
    proxy:
      host: proxy.capvision.com
      port: 8080
  google:
    client-id: 68925922578-tf33991pd4lm86nvla1vcr36eg9ubk87.apps.googleusercontent.com
    client-secret: eGlG-nIqKVP0FJbph89425ru
    proxy:
      host: proxy.capvision.com
      port: 8080
  linkedin:
    client-id: 863ecc7ahyz9a5
    client-secret: ukwwisrJL8dfc5xH
# ---------------------------------------------------------------------------------------------------------------------
com:
  cpvsn:
    portal:
      advisorPreCallUrl: https://qa-udb2.capvision.com/portal/#/b
      advisorPostCallUrl: https://qa-udb2.capvision.com/portal/#/after-task
      contactPreCallUrl: https://qa-udb2.capvision.com/portal/#/pick
      contactPostCallUrl: https://qa-udb2.capvision.com/portal/#/afeedback
    conf:
      db_domain: https://qa-udb2.capvision.com
      locations_version: 4
      cors_origin: "*"
    es:
      indice:
        - index_enum: ADVISOR
          index_name: advisor
        - index_enum: PROJECT
          index_name: usdb_project
        - index_enum: COMPANY
          index_name: usdb_company
contact-out:
  token: 'zNUMoYOfxc7FJMQVRd5C18uE'
  url: 'https://api.contactout.com/v1'

rocket_reach:
  token: '175eb89k4d55864800cc5ba8f5316868dd0a1821'
  url: 'https://api.rocketreach.co/api/v2'
exchange-rate:
  key: ************************