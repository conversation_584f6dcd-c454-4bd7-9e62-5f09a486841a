package com.cpvsn.rm.core.annotation

import com.cpvsn.rm.core.features.search.EsIndex

/**
 * Ensure the corresponding id consumer is added in `com.cpvsn.rm.web.aspect.EsSyncAdvice` before using.
 *
 * Useful when id can be acquired from method parameter after method executed.
 *
 * By default , if first parameter is Int, it will take it as id,
 * otherwise it will take `id` property from first parameter.(if it is collection, take id from each element of collection)
 *
 *
 * @param es_index must provide.
 * @param which_arg FIRST/ SECOND/ THIRD. Its type should be Int/ model/ Collection<>.
 * @param id_field field name in this model corresponding to es doc id.
 *
 * <AUTHOR>
 */
@MustBeDocumented
@Target(AnnotationTarget.FUNCTION)
@Retention(AnnotationRetention.RUNTIME)
annotation class RequireEsSync(
        val es_index: EsIndex,
        val which_arg: Int = 0,
        val id_field: String = "id"
)
