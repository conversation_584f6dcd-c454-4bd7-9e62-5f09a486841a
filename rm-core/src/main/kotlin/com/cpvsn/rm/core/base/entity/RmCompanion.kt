package com.cpvsn.rm.core.base.entity

import com.cpvsn.core.base.entity.BaseCompanion
import com.cpvsn.core.base.entity.BaseEntity
import com.cpvsn.rm.core.annotation.PatchSpec
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.memberProperties

@Suppress("DeprecatedCallableAddReplaceWith")
abstract class RmCompanion<T : BaseEntity> : BaseCompanion<T>() {
    open val clientUpdatableProps: Set<String>
        get() = entityClass.memberProperties.filter {
            val spec = it.findAnnotation<PatchSpec>() ?: return@filter true
            spec.clientUpdatable
        }.map { it.name }.toSet().intersect(updatableFields)
}
