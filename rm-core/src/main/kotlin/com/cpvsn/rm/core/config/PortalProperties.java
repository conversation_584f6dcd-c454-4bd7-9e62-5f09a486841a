package com.cpvsn.rm.core.config;

import com.cpvsn.rm.core.features.portal.PortalType;
import org.hibernate.validator.constraints.URL;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * we use java here because spring annotation processor is more friendly to java
 */
@Component
@Validated
@ConfigurationProperties(prefix = "com.cpvsn.portal")
public class PortalProperties {

    @NotNull
    @NotBlank
    @URL
    private String advisorSurveyUrl = "https://qa-udb2.capvision.com/portal/#/expert_survey/";

    /**
     * https://www.notion.so/Survey-MVP-DB-Decipher-a86813cccdc34d37ab81f06da36a26b2
     */
    @NotNull
    @NotBlank
    @URL
    private String advisor3rdPartySurveyUrl = "https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party/";

    @NotNull
    @NotBlank
    @URL
    private String advisorBankAccountUrl = "https://qa-udb2.capvision.com/portal/#/advisor/payment_info/";

    @NotNull
    @NotBlank
    @URL
    private String advisorUnsubscribeOutreachUrl = "https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe/";

    @NotNull
    @NotBlank
    @URL
    private String advisorUnsubscribeOutreachClickOnceUrl = "https://qa-udb2.capvision.com/api/rm-portal-preview/advisor_portal/unsubscribe/outreach/";

    @NotNull
    @NotBlank
    @URL
    private String advisorDeclineUrl = "https://qa-udb2.capvision.com/portal/#/advisor/decline/";

    @NotNull
    @NotBlank
    @URL
    private String advisorPaymentConfirmUrl = "https://qa-udb2.capvision.com/portal/#/advisor/payment_confirm/";

    @NotNull
    @NotBlank
    @URL
    private String advisorPreCallUrl = "https://qa-udb2.capvision.com/portal/#/b/";

    @NotNull
    @NotBlank
    @URL
    private String advisorPostCallUrl = "https://qa-udb2.capvision.com/portal/#/after-task/";

    @NotNull
    @NotBlank
    @URL
    private String contactPreCallUrl = "https://qa-udb2.capvision.com/portal/#/pick/";

    @NotNull
    @NotBlank
    @URL
    private String contactPostCallUrl = "https://qa-udb2.capvision.com/portal/#/afeedback/";

    @NotNull
    @NotBlank
    @URL
    private String resetPwdUrl = "https://qa-udb2.capvision.com/portal/#/reset-pwd/";

    @NotNull
    @NotBlank
    @URL
    private String clientComplianceLoginUrl = "https://qa-compliance2.capvision.com";

    @NotNull
    @NotBlank
    @URL
    private String clientUserRegisterUrl = "https://qa-udb2.capvision.com/client-portal/#/register/";

    @NotNull
    @NotBlank
    @URL
    private String clientPortalAdvisorRecommendationUrl = "https://qa-udb2.capvision.com/client-portal/#/recommendation";

    //region twilio
    @NotNull
    @NotBlank
    @URL
    private String conferenceDialUrl = "https://qa-udb2.capvision.com/portal/#/conf-dial/";

    @NotNull
    @NotBlank
    @URL
    private String clientPortalConferenceDialUrl = "https://qa-compliance2.capvision.com/client-portal/#/conf-dial";

    @NotNull
    @NotBlank
    @URL
    private String clientPortalConferenceAssetUrl = "https://qa-compliance2.capvision.com/client-portal/#/conf-asset";

    @NotNull
    @NotBlank
    @URL
    private String advisorRecordingConsentUrl = "https://qa-udb2.capvision.com/portal/#/record-consent";
    //endregion

    @NotNull
    @NotBlank
    @URL
    private String clientPortalAdvisorRecommendationInternalUrl = "https://qa-udb2.capvision.com/client-portal/#/recommendation";

    @NotNull
    @NotBlank
    @URL
    private String advisorW9FormCollectUrl = "https://qa-udb2.capvision.com/portal/#/w9-form-collect/";

    @NotNull
    @NotBlank
    private String clientPortalAESSecretKey = "gTRpAzm6Pn3xbrrKG+QqNg==";

    @Min(4)
    @NotNull
    private Integer contactPortalTokenLength = 9;

    @Min(6)
    @NotNull
    private Integer advisorPortalTokenLength = 10;

    /**
     * Because our design is not fine-grained enough,
     * some token's duration cannot be configured using this value.
     * especially,
     * 'ADVISOR_PRE_CALL',
     * 'ADVISOR_POST_CALL',
     * 'ADVISOR_PAYMENT_CONFIRM',
     * 'ADVISOR_UNSUBSCRIBE_OUTREACH',
     * 'ADVISOR_DECLINE',
     * token's durations are NOT controlled by this value.
     *
     * @see PortalType#get_default_token_duration(com.cpvsn.rm.core.config.PortalProperties)
     * unit: minute
     */
    @NotNull
    private Long advisorPortalTokenDuration = 60 * 24 * 7L;
    /**
     * unit: minute
     */
    @NotNull
    private Long contactPortalTokenDuration = 60 * 24 * 7L;
    /**
     * unit: minute
     */
    @NotNull
    private Long resetPwdTokenDuration = 60 * 5L;

    /**
     * unit: minute
     */
    @NotNull
    private Long clientUserRegisterTokenDuration = 60 * 5L;

    @NotNull
    private Long advisorBankAccountTokenDuration = 60 * 24 * 7L;

    public String getAdvisorSurveyUrl() {
        return advisorSurveyUrl;
    }

    public void setAdvisorSurveyUrl(String advisorSurveyUrl) {
        this.advisorSurveyUrl = advisorSurveyUrl;
    }

    public String getAdvisorBankAccountUrl() {
        return advisorBankAccountUrl;
    }

    public void setAdvisorBankAccountUrl(String advisorBankAccountUrl) {
        this.advisorBankAccountUrl = advisorBankAccountUrl;
    }

    public String getAdvisorPreCallUrl() {
        return advisorPreCallUrl;
    }

    public void setAdvisorPreCallUrl(String advisorPreCallUrl) {
        this.advisorPreCallUrl = advisorPreCallUrl;
    }

    public String getContactPreCallUrl() {
        return contactPreCallUrl;
    }

    public void setContactPreCallUrl(String contactPreCallUrl) {
        this.contactPreCallUrl = contactPreCallUrl;
    }

    public String getAdvisorPostCallUrl() {
        return advisorPostCallUrl;
    }

    public void setAdvisorPostCallUrl(String advisorPostCallUrl) {
        this.advisorPostCallUrl = advisorPostCallUrl;
    }

    public String getContactPostCallUrl() {
        return contactPostCallUrl;
    }

    public void setContactPostCallUrl(String contactPostCallUrl) {
        this.contactPostCallUrl = contactPostCallUrl;
    }

    public Integer getContactPortalTokenLength() {
        return contactPortalTokenLength;
    }

    public void setContactPortalTokenLength(Integer contactPortalTokenLength) {
        this.contactPortalTokenLength = contactPortalTokenLength;
    }

    public Long getAdvisorPortalTokenDuration() {
        return advisorPortalTokenDuration;
    }

    public void setAdvisorPortalTokenDuration(Long advisorPortalTokenDuration) {
        this.advisorPortalTokenDuration = advisorPortalTokenDuration;
    }

    public Long getContactPortalTokenDuration() {
        return contactPortalTokenDuration;
    }

    public void setContactPortalTokenDuration(Long contactPortalTokenDuration) {
        this.contactPortalTokenDuration = contactPortalTokenDuration;
    }

    public Integer getAdvisorPortalTokenLength() {
        return advisorPortalTokenLength;
    }

    public void setAdvisorPortalTokenLength(Integer advisorPortalTokenLength) {
        this.advisorPortalTokenLength = advisorPortalTokenLength;
    }

    public Long getResetPwdTokenDuration() {
        return resetPwdTokenDuration;
    }

    public void setResetPwdTokenDuration(Long resetPwdTokenDuration) {
        this.resetPwdTokenDuration = resetPwdTokenDuration;
    }

    public String getResetPwdUrl() {
        return resetPwdUrl;
    }

    public void setResetPwdUrl(String resetPwdUrl) {
        this.resetPwdUrl = resetPwdUrl;
    }

    public String getClientComplianceLoginUrl() {
        return clientComplianceLoginUrl;
    }

    public void setClientComplianceLoginUrl(String clientComplianceLoginUrl) {
        this.clientComplianceLoginUrl = clientComplianceLoginUrl;
    }

    public Long getClientUserRegisterTokenDuration() {
        return clientUserRegisterTokenDuration;
    }

    public void setClientUserRegisterTokenDuration(Long clientUserRegisterTokenDuration) {
        this.clientUserRegisterTokenDuration = clientUserRegisterTokenDuration;
    }

    public String getClientUserRegisterUrl() {
        return clientUserRegisterUrl;
    }

    public void setClientUserRegisterUrl(String clientUserRegisterUrl) {
        this.clientUserRegisterUrl = clientUserRegisterUrl;
    }

    public Long getAdvisorBankAccountTokenDuration() {
        return advisorBankAccountTokenDuration;
    }

    public void setAdvisorBankAccountTokenDuration(Long advisorBankAccountTokenDuration) {
        this.advisorBankAccountTokenDuration = advisorBankAccountTokenDuration;
    }

    public String getAdvisorUnsubscribeOutreachUrl() {
        return advisorUnsubscribeOutreachUrl;
    }

    public void setAdvisorUnsubscribeOutreachUrl(String advisorUnsubscribeOutreachUrl) {
        this.advisorUnsubscribeOutreachUrl = advisorUnsubscribeOutreachUrl;
    }

    public String getAdvisorUnsubscribeOutreachClickOnceUrl() {
        return advisorUnsubscribeOutreachClickOnceUrl;
    }

    public String setAdvisorUnsubscribeOutreachClickOnceUrl(String advisorUnsubscribeOutreachClickOnceUrl) {
        return this.advisorUnsubscribeOutreachClickOnceUrl = advisorUnsubscribeOutreachClickOnceUrl;
    }

    public String getClientPortalAdvisorRecommendationUrl() {
        return clientPortalAdvisorRecommendationUrl;
    }

    public void setClientPortalAdvisorRecommendationUrl(String clientPortalAdvisorRecommendationUrl) {
        this.clientPortalAdvisorRecommendationUrl = clientPortalAdvisorRecommendationUrl;
    }

    public String getConferenceDialUrl() {
        return conferenceDialUrl;
    }

    public void setConferenceDialUrl(String conferenceDialUrl) {
        this.conferenceDialUrl = conferenceDialUrl;
    }

    public String getClientPortalConferenceDialUrl() {
        return clientPortalConferenceDialUrl;
    }

    public void setClientPortalConferenceDialUrl(String clientPortalConferenceDialUrl) {
        this.clientPortalConferenceDialUrl = clientPortalConferenceDialUrl;
    }

    public String getClientPortalConferenceAssetUrl() {
        return clientPortalConferenceAssetUrl;
    }

    public void setClientPortalConferenceAssetUrl(String clientPortalConferenceAssetUrl) {
        this.clientPortalConferenceAssetUrl = clientPortalConferenceAssetUrl;
    }

    public String getAdvisorRecordingConsentUrl() {
        return advisorRecordingConsentUrl;
    }

    public void setAdvisorRecordingConsentUrl(String advisorRecordingConsentUrl) {
        this.advisorRecordingConsentUrl = advisorRecordingConsentUrl;
    }

    public String getClientPortalAESSecretKey() {
        return clientPortalAESSecretKey;
    }

    public void setClientPortalAESSecretKey(String clientPortalAESSecretKey) {
        this.clientPortalAESSecretKey = clientPortalAESSecretKey;
    }

    public String getAdvisor3rdPartySurveyUrl() {
        return advisor3rdPartySurveyUrl;
    }

    public void setAdvisor3rdPartySurveyUrl(String advisor3rdPartySurveyUrl) {
        this.advisor3rdPartySurveyUrl = advisor3rdPartySurveyUrl;
    }

    public String getClientPortalAdvisorRecommendationInternalUrl() {
        return clientPortalAdvisorRecommendationInternalUrl;
    }

    public void setClientPortalAdvisorRecommendationInternalUrl(String clientPortalAdvisorRecommendationInternalUrl) {
        this.clientPortalAdvisorRecommendationInternalUrl = clientPortalAdvisorRecommendationInternalUrl;
    }

    public String getAdvisorW9FormCollectUrl() {
        return advisorW9FormCollectUrl;
    }

    public void setAdvisorW9FormCollectUrl(String advisorW9FormCollectUrl) {
        this.advisorW9FormCollectUrl = advisorW9FormCollectUrl;
    }


    public String getAdvisorDeclineUrl() {
        return advisorDeclineUrl;
    }

    public void setAdvisorDeclineUrl(String advisorDeclineUrl) {
        this.advisorDeclineUrl = advisorDeclineUrl;
    }

    public String getAdvisorPaymentConfirmUrl() {
        return advisorPaymentConfirmUrl;
    }

    public void setAdvisorPaymentConfirmUrl(String advisorPaymentConfirmUrl) {
        this.advisorPaymentConfirmUrl = advisorPaymentConfirmUrl;
    }

}
