package com.cpvsn.rm.core.config

import com.cpvsn.rm.core.extensions.getBean

interface Webpage {

    val db_domain: String
        get() = getBean<CpvsnConfig>().mainConfig().db_domain!!

    val url: String

    data class AdvisorProfile(
            val advisor_id: Int
    ) : Webpage {
        override val url = "$db_domain/#/consultant/$advisor_id/detail"
    }

    data class ClientProfile(
            val client_id: Int
    ) : Webpage {
        override val url = "$db_domain/#/client/$client_id/detail"
    }

    data class ProjectDetail(
            val project_id: Int
    ) : Webpage {
        override val url = "$db_domain/#/project/consultation/$project_id/client-tasks"
    }

    data class ProjectAngle(
            val project_id: Int,
            val angle_ids: String,
    ) : Webpage {
        override val url = "$db_domain/#/project/consultation/$project_id/client-tasks?angle_ids=$angle_ids"
    }

    data class ProjectProfile(
        val project_id: Int
    ) : Webpage {
        override val url = "$db_domain/#/project/consultation/$project_id/information"
    }

    // duplicate of TASK_PROJECT_PAGE_URL (Task::db_page_url_jit)
    data class TaskAnchor(
            val task_id: Int,
            val project_id: Int,
    ) : Webpage {
        override val url: String = "$db_domain/#/project/consultation/$project_id/tasks?task_id=$task_id"
    }

    data class ContractDetail(
            val contract_id: Int
    ) : Webpage {
        override val url = "$db_domain/#/client/contract/$contract_id/detail"
    }

    data class ContractApproval(
            val contract_id: Int
    ) : Webpage {
        override val url = "$db_domain/#/compliance/contract_approval?id=$contract_id"
    }

    object ConsultationApprovalPage : Webpage {
        override val url = "$db_domain/#/compliance/consultation_approval"
    }

    data class TcDetail(
            val id: Int
    ) : Webpage {
        override val url = "$db_domain/#/compliance/tnc/$id/detail"
    }

    data class ClientContactProfile(
            val client_contact_id: Int,
    ) : Webpage {
        override val url = "$db_domain/#/client_contact/$client_contact_id/detail"
    }

}
