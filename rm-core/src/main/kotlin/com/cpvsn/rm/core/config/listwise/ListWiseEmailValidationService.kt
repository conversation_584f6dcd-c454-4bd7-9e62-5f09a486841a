package com.cpvsn.rm.core.config.listwise

import com.cpvsn.core.svc.rpc.http.unirest.UnirestUtil.configureJsonContentTypeHeader
import com.cpvsn.core.svc.rpc.http.unirest.UnirestUtil.configureLogger
import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.rm.core.features.thirdparty.ThirdPartyException
import com.cpvsn.rm.core.features.thirdparty.ThirdPartyVendor
import kong.unirest.Unirest
import kong.unirest.UnirestException
import org.slf4j.LoggerFactory

/**
 * @see https://listwisehq.com/docs/listwise-api/
 */
class ListWiseEmailValidationService(
    private val properties: ListWiseProperties,
) {

    companion object {
        val unpassed_listwise_status = setOf(
            "invalid",
            "no-reply",
            "spam-trap",
            "bad-mx",
            "bounced",
            "suspicious",
            "unknown",
            "disposable",
            "rejected",
            "deferred",
        )
    }

    private val logger = LoggerFactory.getLogger(this::class.java)

    private val unirestInstance = Unirest.spawnInstance()
        .configureJsonContentTypeHeader()
        .configureLogger(logger)

    fun validate_email(
        request: ListWiseEmailValidationRequest,
    ): String {
        val res = try {
            unirestInstance.get(properties.endpoint)
                .queryString(
                    mapOf(
                        "email" to request.email,
                        "api_key" to properties.apiKey
                    )
                )
                .asString()
                .body
        } catch (e: UnirestException) {
            throw ThirdPartyException(
                vendor = ThirdPartyVendor.LIST_WISE,
                cause = e,
            )
        }

        return res
    }

    data class ListwiseResponse(
        val email_status: String,
    )

    fun validate_with_parsed_rsp(
        request: ListWiseEmailValidationRequest,
    ): ListwiseResponse {
        return CoreJsonUtil.parse(validate_email(request), ListwiseResponse::class)
    }

    fun validate_email(
        requests: List<ListWiseEmailValidationRequest>,
    ): String {
        val res = requests.map {
            validate_email(it)
        }
        return "[${res.joinToString(",")}]"
    }

}
