package com.cpvsn.rm.core.config.mq

import org.springframework.amqp.core.*
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@EnableConfigurationProperties(RabbitMqProjectRealTimeNotifyProperties::class)
class RabbitMqProjectRealTimeNotifyConfig {

    @Autowired
    private lateinit var rabbitMqProjectRealTimeNotifyProperties: RabbitMqProjectRealTimeNotifyProperties

    @Bean
    fun projectRealTimeQueue(): Queue {
        return AnonymousQueue()
    }

    @Bean
    fun projectRealTimeFanoutExchange(): FanoutExchange {
        return FanoutExchange(rabbitMqProjectRealTimeNotifyProperties.exchange, true, false)
    }

    @Bean
    fun binding(projectRealTimeQueue: Queue, projectRealTimeFanoutExchange: FanoutExchange): Binding {
        return BindingBuilder.bind(projectRealTimeQueue).to(projectRealTimeFanoutExchange)
    }

}