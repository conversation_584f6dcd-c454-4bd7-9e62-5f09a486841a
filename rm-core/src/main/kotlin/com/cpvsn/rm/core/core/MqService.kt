package com.cpvsn.rm.core.core

import com.cpvsn.rm.core.util.JsonUtil
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component

@Component
class MqService {
    @Autowired
    private lateinit var rabbitTemplate: RabbitTemplate

    fun sendMail(o: Any?) {
        rabbitTemplate.convertAndSend("mail_k8s.direct", "mail_k8s", JsonUtil.stringify(o))
    }

    fun sendSms(o: Any?) {
        rabbitTemplate.convertAndSend("sms.direct", "sms", JsonUtil.stringify(o))
    }
}
