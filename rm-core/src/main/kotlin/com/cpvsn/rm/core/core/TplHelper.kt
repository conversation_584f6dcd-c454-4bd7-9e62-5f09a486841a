package com.cpvsn.rm.core.core

import com.cpvsn.core.util.experimental.BeanUtil
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.thymeleaf.TemplateEngine
import org.thymeleaf.context.Context

@Component
class TplHelper {
    @Autowired
    private lateinit var templateEngine: TemplateEngine

    fun process(
            tpl_name: String?,
            pojo: Any?
    ): String {
        val context = Context()
        pojo?.let { context.setVariables(BeanUtil.to_map(it)) }
        return templateEngine.process(tpl_name, context)
    }

    fun process(
            tpl_name: String?,
            params: Map<String?, Any?>?
    ): String {
        val context = Context()
        context.setVariables(params)
        return templateEngine.process(tpl_name, context)
    }
}
