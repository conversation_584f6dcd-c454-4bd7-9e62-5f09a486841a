package com.cpvsn.rm.core.extensions

import com.cpvsn.core.base.entity.BaseEntity
import com.cpvsn.core.model.BusinessException

fun <T : Any> T?.assert_exist(message: String? = null): T {
    return this ?: throw IllegalStateException(message)
}

/**
 * if message is present use message
 * else if id_hint is present use "resource with id $id_hint not found"
 * else use "resource not found"
 */
fun <T : BaseEntity> T?.assert_model_exist(
        id_hint: Int? = null,
        message: String? = null
): T {
    return this ?: throw IllegalStateException(
            message ?: id_hint?.let {
                "resource with id $id_hint not found"
            } ?: "resource not found"
    )
}

fun <T : Any> List<T?>.ensure_at_most_one(message: String = "illegal status: ${this::class.simpleName} found more than one."): T? {
    if (this.size > 1)
        throw BusinessException(message)
    else
        return this.firstOrNull()
}
