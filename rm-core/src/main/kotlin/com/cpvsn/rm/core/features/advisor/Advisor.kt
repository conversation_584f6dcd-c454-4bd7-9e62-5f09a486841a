package com.cpvsn.rm.core.features.advisor

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.orm.annotation.*
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.crud.tsid.GeneratedTsId
import com.cpvsn.rm.core.annotation.DocConstant
import com.cpvsn.rm.core.base.entity.ColumnDefinitions
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.model.*
import com.cpvsn.rm.core.features.advisor.activity.AdvisorActivity
import com.cpvsn.rm.core.features.advisor.bank_account.AdvisorBankAccount
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.advisor.job.AdvisorNpiRegistry
import com.cpvsn.rm.core.features.advisor.payment_form.AdvisorPaymentForm
import com.cpvsn.rm.core.features.advisor.w9.AdvisorW9Form
import com.cpvsn.rm.core.features.auth.GeneralUser
import com.cpvsn.rm.core.features.auth.GeneralUserRole
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.client.blacklist.ClientBlacklist
import com.cpvsn.rm.core.features.compliance.tc.Tc
import com.cpvsn.rm.core.features.compliance.tc.advisor.AdvisorTc
import com.cpvsn.rm.core.features.email.EmailAddressScore
import com.cpvsn.rm.core.features.email.validation.EmailValidationRecord
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.features.misc.excel_import.model.LinkedinIndustryCode
import com.cpvsn.rm.core.features.misc.location.Location
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.features.user.cart.ICartRef
import com.cpvsn.rm.core.features.user.tag.UserAdvisorTagMap
import com.cpvsn.rm.core.util.TextMosaicUtils
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import java.math.BigDecimal
import java.time.Instant
import javax.validation.constraints.Email

@TableDefinition(
    indices = [
        TableDefinition.IndexDefinition(
            columns = ["ndb_id"],
            type = TableDefinition.IndexType.UNIQUE
        ),
        TableDefinition.IndexDefinition(
            columns = ["tsid"],
            type = TableDefinition.IndexType.UNIQUE
        ),
    ]
)
class Advisor : RmEntity(),
    Zoned,
    HasLocale,
    HasEmail,
    HasTsId,
    UsOrCn,
    GeneralUser,
    UserAuditing,
    ICartRef,
    SoftDeletable {

    companion object : RmCompanion<Advisor>()

    @Column
    var ext_id: String = ""

    @Column
    @GeneratedTsId
    @ColumnDefinition(type = ColumnDefinitions.TSID)
    override var tsid: String? = null

    // hide id no
    @Column
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    var id_no: String? = null

    val id_no_with_mosaic: String?
        get() = id_no?.let {
            TextMosaicUtils.process_id_card(it)
        }

    @Column
    @ColumnDefinition(type = Region.COLUMN_DEFINITION)
    var origin: String = Region.current.name

    @get:JsonIgnore
    var origin_enum: Region? by PropDelegates.enum_nullable(this::origin)

    @Column
    var type: Type = Type.LEAD

    @JsonIgnore
    @Column
    var spoken_language: String? = null

    @PatchFields(["spoken_language"])
    var spoken_language_list: List<String> by PropDelegates.comma_list(this::spoken_language) { it }

    @Column
    var npi: String? = null

    @Column
    var npi_status: NpiStatus = NpiStatus.INITIAL

    @Column
    var status: String? = null

    @get: JsonIgnore
    @PatchFields(["status"])
    var status_enum: Status? by PropDelegates.enum_nullable(this::status)

    /**
     * DO NOT use this property to decide is a user signed tc
     *
     * it is set to 'SENT' whenever we send a sign tc request to the advisor.
     */
    @Column
    var tc_status: TcStatus = TcStatus.INITIAL

    /**
     * consultation_tc_expire_at
     */
    @Column
    var tc_expire_at: Instant? = null

    /**
     * 对应 Tc.Term.CONSULTATION
     * 当切仅当值为 false 的时候, advisor 可以参加访谈项目
     * 如果值为 null 或者 true, 专家需要先签署带有 CONSULTATION Term 的 TC
     *
     * it is called tc_expired for historical reason
     * a better name should be consultation_tc_expired
     *
     * this property is exposed for historical reason
     */
    val tc_expired: Boolean?
        get() {
            return tc_expire_at?.let {
                Instant.now() > it
            }
        }

    /**
     * we don't expose this property to client app
     * client app can use "tc_term_valid" instead
     */
    @get: JsonIgnore
    val consultation_tc_valid: Boolean
        get() = (tc_expired == false)

    @Column
    var survey_tc_expire_at: Instant? = null

    /**
     * 对应 Tc.Term.SURVEY
     * 当切仅当值为 false 的时候, advisor 可以参加调查项目
     * 如果值为 null 或者 true, 专家需要先签署带有 SURVEY Term 的 TC
     *
     * we don't expose this property to client app
     * client app can use "tc_term_valid" instead
     */
    @get: JsonIgnore
    val survey_tc_expired: Boolean?
        get() {
            return survey_tc_expire_at?.let {
                Instant.now() > it
            }
        }

    /**
     * we don't expose this property to client app
     * client app can use "tc_term_valid" instead
     */
    @get: JsonIgnore
    val survey_tc_valid: Boolean
        get() = (survey_tc_expired == false)

    val tc_term_valid: Map<Tc.Term, Boolean>
        get() = mapOf(
            Tc.Term.CONSULTATION to consultation_tc_valid,
            Tc.Term.SURVEY to survey_tc_valid,
        )

    @Column
    var firstname: String? = null

    @Column
    var lastname: String? = null

    @Column
    var name_prefix: String? = null

    @Column
    val full_name: String
        get() = listOfNotNull(
            firstname,
            lastname
        ).filter { it.isNotBlank() }.joinToString(" ")

    @get:JsonIgnore
    override val name: String
        get() = full_name

    @Column
    var background: String? = null

    @Column
    var location_id: Int? = null

    /**
     * when we send tc to advisor, we may use this value as default rate value
     */
    @Column
    var default_rate: Int? = null

    @Column
    var rate: Int? = null

    @Column
    var rate_currency: BuiltInCurrency? = null

    @Column
    @Email
    // hide contact info
    @JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    override var email: String? = null

    val email_with_mosaic: String?
        get() = email?.let { TextMosaicUtils.process_email(it) }

    @Column
    override var locale: String? = null

    @Column
    override var zone_id_string: String? = null

    @Column
    override var ndb_id: Int? = null

    @Column
    override var is_cn: Boolean = false

    /**
     * https://www.notion.so/capvision/Add-30-Minute-Minimum-to-expert-c7895133145b4d1684cb81f36cff8644?d=bc06653c20cf40558e576cd9a0e89ab8
     */
    @Column
    var time_minimum: TimeMinimumType = TimeMinimumType.NO_MINIMUM

    val is_one_hour_minimum: Boolean
        get() {
            return time_minimum == TimeMinimumType.ONE_HOUR
        }

    @Column
    var notes: String? = null

    /**
     * This is whoever sent the TC to the advisor and the advisor accepted TC
     */
    @Column
    var sourced_by_id: Int = 0

    /**
     * Date they accepted TC
     */
    @Column
    var sourced_at: Instant? = null

    @Column
    var sourced_project_id: Int? = null

    /**
     * of consultations completed
     */
    @Column
    var completed_consultation_count: Int = 0

    @Column
    var put_blacklist_at: Instant? = null

    @Column
    var put_blacklist_by_id: Int? = null

    @Column
    var unsubscribe_type: UnsubscribeType? = null

    @Column
    var latest_signed_advisor_tc_id: Int? = null

    @Column
    var latest_signed_survey_advisor_tc_id: Int? = null

    @Column
    var latest_signed_default_advisor_tc_id: Int? = null

    @Column
    var latest_manually_update_at: Instant? = null

    /**
     * https://www.notion.so/capvision/GDPR-Delete-Advisor-Lead-but-retain-library-of-unique-ID-66b713edc58d457eb244cd621a81583e
     */
    @Column
    var gdpr_delete_at: Instant? = null

    @Column
    var gdpr_delete_by_id: Int? = null

    @Column
    override var create_by_id: Int = 0

    @Column
    override var update_by_id: Int = 0

    @Column(updatable = false, insertable = false)
    override var delete_at: Instant? = null

    @Column
    var linkedin_industry_code_id: Int? = null

    @Column
    var linkedin_profile_summary: String? = null

    @Column
    var linkedin_profile_id: String? = null

    @Column
    var is_linkedin_premium: Boolean = false

    @Column
    var is_outsource_statistic_advisor: Boolean = false

    @Column
    var red_herring_sq_count: Int? = 0

    @get: JsonIgnore
    override val general_user_role: GeneralUserRole
        get() = GeneralUserRole.ADVISOR

    @get: JsonIgnore
    override val is_blocked: Boolean
        get() = false

    //region +
    @Relation
    var sourced_by: User? = null

    @Relation
    var linkedin_industry_code: LinkedinIndustryCode? = null

    @Relation
    var create_by: User? = null

    @Relation
    var update_by: User? = null

    @Relation
    var gdpr_delete_by: User? = null

    /**
     * compute via avg feedback score(`(communication_rate + expertise_rate + professionalism_rate) / 3 * size`)
     * it is computed just in time, in future, we may compute it periodically
     */
    var feedback_rating_jit: BigDecimal? = null

    var contact_count_jit: Int? = null

    @Relation
    var jobs: List<AdvisorJob>? = null

    @Deprecated("this becomes meaningless as now an advisor may have multiple current jobs, use current_jobs instead")
    var current_job: AdvisorJob? = null
    var current_jobs: List<AdvisorJob>? = null

    // generated background based on advisor jobs
    var auto_background: String? = null

    @Relation(reference = "email", backReference = "email")
    var email_address_score: EmailAddressScore? = null

    /**
     * we may limit result size in future
     * e.g. fetch latest 20 records
     */
    @Relation(reference = "email", backReference = "email")
    var email_validation_records: List<EmailValidationRecord>? = null

    var email_contact_info: ContactInfo? = null

    var contact_infos: List<ContactInfo>? = null

    var contact_infos_without_mosaic: List<ContactInfo>? = null

    @Relation
    var location: Location? = null

    var country: Location? = null

    var projects: List<Project>? = null

    @Relation(backReference = "advisor_id")
    var tasks: List<Task>? = null

    var latest_task: Task? = null

    var location_id_path: String? = null

    // consider add @JsonIgnore in future
    // client should use payment form related API instead.
    @Relation(backReference = "advisor_id")
    var bank_account: AdvisorBankAccount? = null

    /**
     * https://www.notion.so/capvision/Payment-Form-Changes-to-support-physical-check-and-amazon-gift-cards-f0476935868946249634f9e8d8454093
     *
     * we add @JsonIgnore, cuz I want client to access it via standalone API
     */
    @Relation
    @JsonIgnore
    var payment_forms: List<AdvisorPaymentForm>? = null

    @Relation
    var tc_list: List<AdvisorTc>? = null

    // means latest_signed_advisor_tc
    // keep it for backward compatibility
    @Relation(reference = "latest_signed_advisor_tc_id")
    var latest_signed_tc: AdvisorTc? = null

    @Relation
    var latest_signed_default_advisor_tc: AdvisorTc? = null

    @Relation
    var latest_signed_survey_advisor_tc: AdvisorTc? = null

    @Relation
    var client_blacklist_advisor: List<ClientBlacklist>? = null

    @Relation(reference = "npi", backReference = "npi_number")
    var advisor_npi_registry: AdvisorNpiRegistry? = null

    var latest_sent_tc: AdvisorTc? = null

    @Relation
    var status_change_logs: List<AdvisorStatusChangeLog>? = null
    //endregion

    //region $
    // hide contact info
    @JsonIgnore
    var mobile_jit: String? = null

    val mobile_with_mosaic_jit: String?
        get() = mobile_jit?.let { TextMosaicUtils.process_mobile(it) }

    @JsonIgnore
    var linkedin_url_jit: String? = null

    val linkedin_url_with_mosaic_jit: String?
        get() = linkedin_url_jit?.let { TextMosaicUtils.process_linkedin_url(it) }

    /**
     * this value can be used to determine whether we should send W9 form
     */
    var paid_usd_in_this_year_jit: BigDecimal? = null

    var w9_form: AdvisorW9Form? = null

    var db_page_url_jit: String? = null

    //endregion
    @Relation(backReference = "advisor_id")
    var activity: AdvisorActivity? = null

    @Relation
    var user_advisor_tag_maps: List<UserAdvisorTagMap>? = null

    @Relation(backReference = "advisor_id")
    var schedule: Schedule? = null

    @Relation(reference = "sourced_project_id")
    var sourced_project: Project? = null

    @JsonIgnore
    var trolley_recipient_id: String? = null

    @JsonIgnore
    var is_trolley_recipient_incomplete: Boolean? = null

    enum class Status(val description: String = "") {
        NOT_CONTACTED,
        LINKEDIN_MESSAGE,
        EMAIL,
        LEFT_VM,
        PRE_ADVISOR,
        ADVISOR,
        NOT_INTERESTED,
        DO_NOT_CONTACT,
        BLACKLIST
    }

    /**
     * Note that advisor tc status can be changed from 'SIGNED' to 'SENT'
     */
    @DocConstant(displayNameFieldName = "value")
    enum class TcStatus {
        INITIAL,
        SENT,
        SIGNED,
    }

    @DocConstant(displayNameFieldName = "value")
    enum class Type(val value: String) {
        LEAD("Lead"),
        ADVISOR("Advisor")
    }

    enum class UnsubscribeType {
        EMAIL,
        UNSUBSCRIBE_HEADER,
        MANUALLY_IN_DB,
    }

    enum class NpiStatus {
        INITIAL,
        VALID,
        DUPLICATE,
    }

    enum class TimeMinimumType {
        THIRTY_MINUTES,
        ONE_HOUR,
        NO_MINIMUM
    }

    enum class OutsourceStatisticAdvisor(val id: Int) {
        CN(5489245)
    }

    data class Query(
        @Criteria.Eq
        var id: Int? = null,
        @Criteria.IdsIn
        var ids: Set<Int>? = null,

        @Criteria.Gte
        val id_gte: Int? = null,
        @Criteria.Lte
        val id_lte: Int? = null,

        @Criteria.Eq
        val tsid: String? = null,
        @Criteria.Ne
        val tsid_ne: String? = null,
        @Criteria.In
        val tsid_in: Set<String>? = null,
        @Criteria.Expr("({this}.tsid is null or {this}.tsid = '') = #{value}")
        val tsid_is_null_or_empty: Boolean? = null,

        @Criteria.Eq
        val ext_id: String? = null,
        @Criteria.Ne
        val ext_id_ne: String? = null,
        @Criteria.In
        val ext_ids: Set<String>? = null,

        @Criteria.Eq
        val ndb_id: Int? = null,
        @Criteria.IdsIn
        val ndb_ids: Set<Int>? = null,
        @Criteria.Eq
        val npi: String? = null,
        @Criteria.Eq
        val npi_status: NpiStatus? = null,
        @Criteria.Eq
        val is_cn: Boolean? = null,

        @Criteria.Eq
        val type: Type? = null,
        @Criteria.In
        val type_in: Set<Type>? = null,
        @Criteria.Eq
        val tc_status: TcStatus? = null,

        @Criteria.Expr("({this}.tc_expire_at is not null and {this}.tc_expire_at > NOW())=#{value}")
        val consultation_tc_valid: Boolean? = null,
        @Criteria.Expr("({this}.survey_tc_expire_at is not null and {this}.survey_tc_expire_at > NOW())=#{value}")
        val survey_tc_valid: Boolean? = null,

        @Criteria.Eq
        val status: Status? = null,
        @Criteria.In
        val status_in: Set<Status>? = null,
        @Criteria.NotIn
        val status_not_in: Set<Status>? = null,
        @Criteria.IsNull
        val status_is_null: Boolean? = null,

        @Criteria.Eq
        val full_name: String? = null,
        @Criteria.Contains
        val full_name_contains: String? = null,

        @Criteria.Eq
        val email: String? = null,
        @Criteria.Contains
        val email_contains: String? = null,

        @Criteria.Eq
        val is_linkedin_premium: Boolean? = null,

        @Criteria.IsNull
        val gdpr_delete_at_is_null: Boolean? = null,

        @Criteria.Eq
        val location_id: Int? = null,
        @Criteria.IdsIn
        val location_ids: Set<Int>? = null,
        @Criteria.Join
        val location: Location.Query? = null,

        @Criteria.Lt
        val create_at_lt: Instant? = null,
        @Criteria.Gte
        val create_at_gte: Instant? = null,

        @Criteria.Eq
        val time_minimum: TimeMinimumType? = null,
        @Criteria.In
        val time_minimums: Set<TimeMinimumType>? = null,

        @Criteria.Lt
        val sourced_at_lt: Instant? = null,
        @Criteria.Gte
        val sourced_at_gte: Instant? = null,

        @Criteria.Eq
        val is_outsource_statistic_advisor: Boolean? = null,

        @Criteria.Eq
        val linkedin_profile_id: String? = null,
        @Criteria.Eq
        val linkedin_industry_code_id: Int? = null,
        @Criteria.IdsIn
        val linkedin_industry_code_ids: Set<Int>? = null,

        @Criteria.Join(on = "{this}.id = {that}.owner_id and {that}.owner_type = 'ADVISOR'")
        val contact_infos: ContactInfo.Query? = null,

        // means latest_signed_advisor_tc
        // keep it for backward compatibility
        @Criteria.Join
        val latest_signed_tc: AdvisorTc.Query? = null,
        @Criteria.Join
        val latest_signed_default_advisor_tc: AdvisorTc.Query? = null,
        @Criteria.Join
        val latest_signed_survey_advisor_tc: AdvisorTc.Query? = null,
        @Criteria.Join(on = "{this}.email={that}.email")
        var email_validation_records: EmailValidationRecord.Query? = null,

        @Criteria.Join
        val task: Task.Query? = null,
        @Criteria.Join
        val activity: AdvisorActivity.Query? = null,
        // jobs
        // https://www.notion.so/capvision/AM-Dashboard-Formers-filter-for-employment-dates-e66a8ef354af44619f2faec13c5a6502
        @Criteria.Join(on = "{this}.id = {that}.advisor_id and {that}.is_current")
        val current_jobs: AdvisorJob.Query? = null,
        @Criteria.Join(
            on = """
            {that}.id = (
                select id from advisor_job 
                where advisor_id = {this}.id 
                and not is_current 
                and delete_at = 0
                order by end_date desc limit 1
            )
        """
        )
        val latest_former_job: AdvisorJob.Query? = null,
        @Criteria.Expr("({this.current_jobs}.company_id = {this.latest_former_job}.company_id) = #{value}")
        val same_current_and_former_employer: Boolean? = null,
        @Criteria.Join
        val user_advisor_tag_maps: UserAdvisorTagMap.Query? = null,
        @Criteria.Or
        val or: List<Query>? = null,
        override val includeSoftDeletedRecords: Boolean = false,
    ) : BaseQuery<Advisor>()
}
