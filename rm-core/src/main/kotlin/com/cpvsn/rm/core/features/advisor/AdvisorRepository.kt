package com.cpvsn.rm.core.features.advisor

import com.cpvsn.core.util.extension.BD
import com.cpvsn.core.util.extension.add_prefix
import com.cpvsn.core.util.extension.remove_prefix
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.model.CrudOptions
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.mybatis.sqlprovider.MethodNameSqlProvider
import com.cpvsn.crud.spring.util.ext.explicitFields
import com.cpvsn.crud.util.*
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.config.oversea.OverSeaDbRouter
import com.cpvsn.rm.core.extensions.contains_any
import com.cpvsn.rm.core.extensions.legacy_ids
import com.cpvsn.rm.core.extensions.sumBdBy
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.advisor.w9.AdvisorW9FormService
import com.cpvsn.rm.core.features.compliance.tc.advisor.AdvisorTc
import com.cpvsn.rm.core.features.finance.advisor_payment.AdvisorPayment
import com.cpvsn.rm.core.features.finance.advisor_payment.local.LocalAdvisorPayment
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfoUtils
import com.cpvsn.rm.core.features.misc.location.LocationRepo
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.feedback.TaskContactFeedback
import com.cpvsn.rm.core.features.thirdparty.trolley.TrolleyService
import com.cpvsn.rm.core.features.thirdparty.trolley.entity.TrolleyRecipient
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.SelectProvider
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import java.math.RoundingMode
import java.time.Instant
import java.time.ZonedDateTime


@Mapper
@Component
interface AdvisorMapper {
    @SelectProvider(MethodNameSqlProvider::class)
    fun one_by_contact_info(query: AdvisorJoinContactInfoQuery): Advisor?
}

@Repository
class AdvisorRepository : RmBaseRepository<Advisor>(),
    JdbcEntityBatchRepo<Int, Advisor> {

    override val batchDao: JdbcEntityBatchDao<Int, Advisor> by lazy {
        JdbcEntityBatchDao(Advisor::class, dataSource)
    }

    @Autowired
    private lateinit var mapper: AdvisorMapper

    @Autowired
    private lateinit var locationRepo: LocationRepo

    @Autowired
    private lateinit var advisorW9FormService: AdvisorW9FormService

    @Autowired
    private lateinit var trolleyService: TrolleyService

    fun one_by_contact_info(
        query: AdvisorJoinContactInfoQuery,
        include: Set<String>
    ): Advisor? {
        val res = mapper.one_by_contact_info(query) ?: return null
        join(listOf(res), include)
        return res
    }

    override fun handleJoin(
        list: List<Advisor>,
        include: Set<String>
    ): List<Advisor> {
        val res = super.handleJoin(list, include)

        if (include.contains_any(
                Advisor::contact_infos.name,
                Advisor::email_contact_info.name,
            )
        ) {
            val inc = include
                .remove_prefix(Advisor::contact_infos.name)
                .plus(include.remove_prefix(Advisor::email_contact_info.name))
            val items = ContactInfo.findAll(
                ContactInfo.Query(
                    owner_ids = res.ids(),
                    owner_type = ContactInfo.OwnerType.ADVISOR,
                ), include = inc
            )
            // hide contact info
            items.forEach { it.add_mosaic() }
            val map = items.groupBy { it.owner_id }
            res.forEach { advisor ->
                advisor.contact_infos = map[advisor.id].orEmpty()
                advisor.email_contact_info =
                    advisor.contact_infos.orEmpty().firstOrNull {
                        it.value == advisor.email
                                && it.type == ContactInfo.Type.EMAIL
                    }
            }
        }

        if (include.contains(Advisor::contact_infos_without_mosaic.name)) {
            val items = ContactInfo.findAll(
                ContactInfo.Query(
                    owner_ids = res.ids(),
                    owner_type = ContactInfo.OwnerType.ADVISOR,
                ),
                include = include.remove_prefix(Advisor::contact_infos_without_mosaic.name)
            )
            val map = items.groupBy { it.owner_id }
            res.forEach {
                it.contact_infos_without_mosaic = map[it.id].orEmpty()
            }
        }

        if (include.contains_any(
                Advisor::mobile_jit.name,
                Advisor::mobile_with_mosaic_jit.name,
                Advisor::linkedin_url_jit.name,
                Advisor::linkedin_url_with_mosaic_jit.name,
            )
        ) {
            val map = ContactInfo.findAll(
                ContactInfo.Query(
                    owner_ids = res.ids(),
                    owner_type = ContactInfo.OwnerType.ADVISOR,
                )
            ).groupBy { it.owner_id }
            res.forEach {
                val l = map[it.id]
                it.mobile_jit = ContactInfoUtils.find_mobile(l)?.value.orEmpty()
                it.linkedin_url_jit =
                    ContactInfoUtils.find_linkedin_url(l)?.value.orEmpty()
            }
        }

        @Suppress("DEPRECATION")
        if (include.contains(Advisor::current_job.name)) {
            val map = AdvisorJob.findAll(
                AdvisorJob.Query(
                    advisor_ids = res.ids(),
                    is_current = true,
                ), include = include.remove_prefix(Advisor::current_job.name)
            )
                .associateBy { it.advisor_id }
            res.forEach { advisor ->
                advisor.current_job = map[advisor.id]
            }
        }

        if (include.contains(Advisor::current_jobs.name)) {
            val map = AdvisorJob.findAll(
                AdvisorJob.Query(
                    advisor_ids = res.ids(),
                    is_current = true,
                ), include = include.remove_prefix(Advisor::current_jobs.name)
            )
                .groupBy { it.advisor_id }
            res.forEach { advisor ->
                advisor.current_jobs = map[advisor.id].orEmpty()
            }
        }

        if (include.contains(Advisor::auto_background.name)) {
            val map = AdvisorJob.findAll(
                AdvisorJob.Query(
                    advisor_ids = res.ids(),
                ), include = Includes.setOf(AdvisorJob::company)
            )
                .groupBy { it.advisor_id }
            res.forEach {
                val jobs = map[it.id]
                it.auto_background = AdvisorUtil.compute_auto_bg(it, jobs.orEmpty())
            }
        }

        if (include.contains(Advisor::projects.name)) {
            val query = Task.Query(advisor_ids = res.legacy_ids())
            val map = Task.findAll(
                query, include = setOf(
                    Task::project.name,
                    *include.remove_prefix(Advisor::projects.name)
                        .add_prefix(Task::project.name)
                )
            ).groupBy { it.advisor_id }
                .mapValues {
                    it.value.mapNotNull { task -> task.project }
                        .distinctBy { project -> project.id }
                }
            res.forEach { it.projects = map[it.id] ?: emptyList() }
        }

        if (include.contains(Advisor::latest_task.name)) {
            val query = Task.Query(advisor_ids = res.legacy_ids())
            val map = Task.findAll(
                query, include = include.remove_prefix(Advisor::latest_task.name)
            ).groupBy { it.advisor_id }.mapValues { entry ->
                entry.value.sortedByDescending { it.create_at }.firstOrNull()
            }
            res.forEach { it.latest_task = map[it.id] }
        }

        if (include.contains(Advisor::location_id_path.name)) {
            res.forEach {
                it.location_id_path =
                    locationRepo.location_id_path_map[it.location_id]
            }
        }

        if (include.contains(Advisor::latest_sent_tc.name)) {
            val items = AdvisorTc.findAll(
                AdvisorTc.Query(
                    advisor_ids = res.ids(),
                ), include = include.remove_prefix(Advisor::latest_sent_tc.name)
            )

            val map = items.groupBy { it.advisor_id }
                .mapValues {
                    it.value.maxByOrNull { o -> o.create_time ?: Instant.MIN }
                }

            res.forEach {
                it.latest_sent_tc = map[it.id]
            }
        }

        if (include.contains(Advisor::feedback_rating_jit.name)) {
            val map = TaskContactFeedback.findAll(
                TaskContactFeedback.Query(
                    advisor_ids = res.ids()
                )
            ).groupBy { it.advisor_id }
            res.forEach { advisor ->
                val feed_backs = map[advisor.id].orEmpty()
                advisor.feedback_rating_jit =
                    feed_backs.takeIf { it.isNotEmpty() }?.let {
                        val total =
                            feed_backs.sumBy { it.communication_rate + it.expertise_rate + it.professionalism_rate }
                                .toBigDecimal()
                        total.divide(
                            (3 * feed_backs.size).BD,
                            1,
                            RoundingMode.CEILING
                        )
                    }
            }
        }

        if (include.contains(Advisor::contact_count_jit.name)) {
            val map = CommunicationRecord.findAll(
                CommunicationRecord.Query(
                    advisor_ids = res.ids()
                )
            ).groupBy { it.advisor_id }
            res.forEach { advisor ->
                advisor.contact_count_jit = map[advisor.id].orEmpty().size
            }
        }

        if (include.contains(Advisor::paid_usd_in_this_year_jit.name)) {
            val current_year = ZonedDateTime.now().year
            val map = LocalAdvisorPayment.findAll(
                LocalAdvisorPayment.Query(
                    advisor_ids = res.ids(),
                    status = AdvisorPayment.Status.PAID,
                    currency = BuiltInCurrency.USD,
                    paid_at_year = current_year,
                )
            ).groupBy { it.advisor_id }
            res.forEach { advisor ->
                advisor.paid_usd_in_this_year_jit =
                    map[advisor.id].orEmpty().sumBdBy { it.amount }
            }
        }

//        if (include.contains(Advisor::w9_form.name)) {
//            res.forEach { advisor ->
//                advisor.w9_form =
//                    advisorW9FormService.find_w9_form(advisor_id = advisor.id)
//            }
//        }

        if (Advisor::db_page_url_jit.name in include) {
            res.forEach {
                it.db_page_url_jit = OverSeaDbRouter.ADVISOR.getUrl(
                    mapOf(
                        Advisor::id.name to it.id
                    )
                )
            }
        }

        if (Advisor::country.name in include) {

        }

        if (Advisor::trolley_recipient_id.name in include) {
            val map = TrolleyRecipient.findAll(
                TrolleyRecipient.Query(
                    advisor_ids = res.ids()
                )
            ).associateBy { it.advisor_id }
            res.forEach {
                it.trolley_recipient_id = map[it.id]?.id
            }
        }

        if (Advisor::is_trolley_recipient_incomplete.name in include) {
            res.forEach {
                it.is_trolley_recipient_incomplete = trolleyService.isRecipientInactive(it.id)
            }
        }

        return res
    }

    @Transactional
    override fun save(
        entity: Advisor,
        option: CrudOptions.SaveOption<Advisor>
    ): Advisor {
        entity.email = ContactInfoUtils.find_email(entity.contact_infos)?.value
        return super.save(entity, option)
    }

    @Transactional
    override fun patch(
        patch: Patch<Advisor>,
        option: CrudOptions.PatchOption<Advisor>
    ) {
        var p = patch
        if (patch.fields.contains_any(
                Advisor::firstname.name,
                Advisor::lastname.name
            )
        ) {
            if (!patch.fields.containsAll(
                    setOf(
                        Advisor::firstname.name, Advisor::lastname.name
                    )
                )
            ) {
                val existed = Advisor.get(patch.entity.id)
                if (Advisor::firstname.name !in patch.fields) {
                    patch.entity.firstname = existed.firstname
                }
                if (Advisor::lastname.name !in patch.fields) {
                    patch.entity.lastname = existed.lastname
                }
            }
            p = p.explicitFields(p.fields + Advisor::full_name.name)
        }

        if (Advisor::contact_infos.name in patch.fields
            && option.cascades.any { it.property == Advisor::contact_infos }
        ) {
            p = ContactInfoUtils.sync_contact_info2email(
                patch,
                Advisor::contact_infos,
                Advisor::email
            )
        }

        super.patch(p, option)

        // sync to es
        InvokeUtil.trigger(Event.ADVISOR_DOC_CHANGED(advisor_id = patch.entity.id))
    }

    // we recommend using patch instead
    override fun update(
        entity: Advisor,
        option: CrudOptions.UpdateOption<Advisor>
    ): Advisor {
        val res = super.update(entity, option)
        InvokeUtil.trigger(Event.ADVISOR_DOC_CHANGED(advisor_id = res.id))
        return res
    }

}
