package com.cpvsn.rm.core.features.advisor.activity

import com.cpvsn.rm.core.base.AbstractJob
import com.cpvsn.rm.core.config.schedule.CronJobWeb
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled

@CronJobWeb
class AdvisorActivityJob : AbstractJob() {

    @Autowired
    private lateinit var service: AdvisorActivityService

    /**
     * At 01:20:00am every day
     *
     * https://www.freeformatter.com/cron-expression-generator-quartz.html
     * https://stackoverflow.com/questions/30887822/spring-cron-vs-normal-cron
     */
    @Scheduled(cron = "0 0 1 ? * *")
    @SchedulerLock(name = "AdvisorActivityJob.reevaluate")
    fun reevaluate() {
        // since the last evaluation
        service.reevaluate(since = null)
    }
}
