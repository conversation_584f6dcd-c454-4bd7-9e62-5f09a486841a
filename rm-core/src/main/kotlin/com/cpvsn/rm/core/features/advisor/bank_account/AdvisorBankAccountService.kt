package com.cpvsn.rm.core.features.advisor.bank_account

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.model.ResponseStatus
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.config.CpvsnConfig
import com.cpvsn.rm.core.extensions.assert_exist
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.DuplicateBankAccountModel
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.portal.common.payload.GeneralUserPortalPayload
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.biz_error
import com.cpvsn.web.auth.AuthContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Service
class AdvisorBankAccountService {

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var advisorBankAccountMapper: AdvisorBankAccountMapper

    @Autowired
    private lateinit var cpvsnConfig: CpvsnConfig

    @Transactional
    @Deprecated("use com.cpvsn.rm.core.features.advisor.payment_account.AdvisorPaymentFormService#update_via_portal")
    fun update_via_portal(
        portal: Portal,
        bankAccount: AdvisorBankAccount
    ) {
        if (portal.consumed) throw BusinessException(ResponseStatus.FORBIDDEN)
        save_or_update(bankAccount)
        portal.consume()
        send_bank_account_portal_consumed_notification(portal)
    }

    internal fun send_bank_account_portal_consumed_notification(portal: Portal) {
        val model = PlaceholderBasedModel(
            user = User.find(portal.create_by_id),
            advisor = Advisor.get(portal.get_payload<GeneralUserPortalPayload>().id)
        )
        val mail = EmailRequestPojo.from_template_result(
            placeholderBasedEmailTemplateService.process_first_by_data(
                EmailTemplate.Query(
                    is_draft = false,
                    content_type = EmailContentType.NOTIFY_KM_ADVISOR_BANK_ACCOUNT_RESPONDED,
                    is_system_template = true,
                ),
                model
            )
        )
        mail.send()
    }

    @Transactional
    fun save_or_update(model: AdvisorBankAccount): AdvisorBankAccount {
        val existed = one_by_advisor_id(model.advisor_id)
        // trim account name and bank name when persist
        model.account2?.let {
            model.account2 = model.account2?.copy(
                account_name = it.account_name.trim(),
                bank_name = it.bank_name.trim(),
            )
        }
        val res = if (existed == null) {
            AdvisorBankAccount.save(model)
            model
        } else {
            model.id = existed.id
            AdvisorBankAccount.update(model)
        }
        if (existed == null || (model.is_valid && !model.account_match(existed))) {
            detect_duplication(res)
        }
        return res
    }

    @Transactional
    fun save_or_patch(
        advisor_id: Int,
        patch: Patch<AdvisorBankAccount>
    ): AdvisorBankAccount {
        val existed = one_by_advisor_id(advisor_id)
        val res = if (existed == null) {
            val model = patch.entity
            model.advisor_id = advisor_id
            AdvisorBankAccount.save(model)
        } else {
            patch.entity.id = existed.id
            AdvisorBankAccount.patchThenGet(patch)
        }
        if (existed == null || (patch.entity.is_valid && !patch.entity.account_match(existed))) {
            detect_duplication(res)
        }
        return res
    }

    fun detect_duplication(account: AdvisorBankAccount) {
        val duplicate = find_duplicate(account)
        duplicate?.let {
            send_duplicate_bank_account_notification(account, duplicate)
        }
    }

    /**
     * return a duplicate bank account or null
     */
    fun find_duplicate(account: AdvisorBankAccount): AdvisorBankAccount? {
        if (!account.is_valid) return null
        return advisorBankAccountMapper.get_duplicate_accounts(
            advisor_id_ne = account.advisor_id,
            bank_name = account.bank_name,
            account_name = account.account_name,
            account_number = account.account2_number,
            secret_key = cpvsnConfig.mainConfig().bank_account_secret_key
                ?: biz_error("bank account secret key not found")
        ).firstOrNull()
    }

    private fun send_duplicate_bank_account_notification(
        account: AdvisorBankAccount,
        duplicate: AdvisorBankAccount,
    ) {
        AdvisorBankAccount.join(
            listOf(account, duplicate), Includes.setOf(
                AdvisorBankAccount::advisor dot Advisor::db_page_url_jit,
            )
        )
        val advisor = account.advisor.assert_exist()
        val model = PlaceholderBasedModel(
            user = AuthContext.fetch(), // should use fetch here
            advisor = advisor,
        ).apply {
            thymeleaf_model = DuplicateBankAccountModel(
                bank_account = account,
                another_bank_account = duplicate,
                advisor = advisor,
                another_advisor = duplicate.advisor.assert_exist()
            )
        }
        val mail = EmailRequestPojo.from_template_result(
            placeholderBasedEmailTemplateService.process_first_by_data(
                EmailTemplate.Query(
                    is_draft = false,
                    content_type = EmailContentType.NOTIFY_DUPLICATE_ADVISOR_BANK_ACCOUNT,
                    is_system_template = true,
                ),
                model
            )
        )
        mail.send()
    }

    @Transactional
    fun one_by_advisor_id(advisor_id: Int, extra: Set<String> = emptySet()): AdvisorBankAccount? {
        val query = AdvisorBankAccount.Query(
            advisor_id = advisor_id,
        )
        return AdvisorBankAccount.firstOrNull(query, include = extra)
    }
}
