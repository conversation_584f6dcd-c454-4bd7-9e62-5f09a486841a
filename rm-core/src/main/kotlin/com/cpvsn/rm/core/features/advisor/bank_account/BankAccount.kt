package com.cpvsn.rm.core.features.advisor.bank_account

import kotlin.reflect.KClass

interface BankAccount {
    val bank_name: String
    val bank_area: String
    val account_name: String
    val account_number: String
    val receiver_type: ReceiverType?
    val bank_branch: String?
    val bank_location: String?
    val account_type: BankAccountType?
    val routing_number: String?
    val transit_number: String?
    val swift_code: String?
    val sort_code: String?
    val cpf_code: String?
    val ifsc_code: String?
    val cmnd_code: String?

    /**
     * it says "cn db use these two fields to store the varies 'code' information"
     */
    val tax_id_code: String?
    val tax_id_code_type: String?
    val address1: String?
    val address2: String?

    enum class ReceiverType(val description: String) {
        ONESELF("专家本人"),
        ANOTHER_PERSON("他人"),
        CORPORATION("机构, 公司"),
        ;
    }

    companion object {
        fun find_class_by_version(version: Int): KClass<out BankAccount> {
            // here we only have one version
            return BankAccountV1::class
        }
    }

}
