package com.cpvsn.rm.core.features.advisor.job

import com.cpvsn.rm.core.features.misc.company.Company
import com.cpvsn.rm.core.features.misc.company.CompanyAlias

interface AdvisorJobLegalValidateResult {
    val valid: Boolean
    val error_message: String?
    val warn_companies: List<CompanyAlias>
    val banned_company: Company?

    private data class SimpleAdvisorJobLegalValidateResult(
        override val valid: Boolean,
        override val error_message: String?,
        override val warn_companies: List<CompanyAlias>,
        override val banned_company: Company?,
    ) : AdvisorJobLegalValidateResult

    companion object {
        fun of(
            valid: Boolean,
            error_message: String? = null,
            warn_companies: List<CompanyAlias> = emptyList(),
            banned_company: Company? = null,
        ): AdvisorJobLegalValidateResult {
            return SimpleAdvisorJobLegalValidateResult(
                valid, error_message, warn_companies, banned_company,
            )
        }
    }
}
