package com.cpvsn.rm.core.features.advisor.job

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.user.User

class AdvisorPhysicianBoardCertification : RmEntity() {
    companion object : RmCompanion<AdvisorPhysicianBoardCertification>()

    @Column
    var advisor_id: Int = 0

    @Column
    var is_board_certification_confirmed: Boolean = false

    @Column
    var note: String = ""

    @Column
    var project_id: Int = 0

    @Column
    var user_id: Int = 0

    @Relation
    var project: Project? = null

    @Relation
    var user: User? = null

    data class Query(
        @Criteria.Eq
        val advisor_id: Int? = null,
        @Criteria.IdsIn
        val advisor_ids: Set<Int>? = null,

        @Criteria.Eq
        val project_id: Int? = null,

        @Criteria.Eq
        val user_id: Int? = null,
    ) : BaseQuery<AdvisorPhysicianBoardCertification>()
}