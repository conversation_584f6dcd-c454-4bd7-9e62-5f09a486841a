package com.cpvsn.rm.core.features.advisor.payment_form

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.model.ResponseStatus
import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.extension.biz_require
import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.spring.util.ext.explicitFields
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.bank_account.AdvisorBankAccountService
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.project.Project
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate

@Service
class AdvisorPaymentFormService {

    @Autowired
    private lateinit var advisorBankAccountService: AdvisorBankAccountService

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Transactional
    fun update_via_portal(
        portal: Portal,
        form: AdvisorPaymentForm,
        advisor_patch: Patch<Advisor>
    ) {
        if (portal.consumed) throw BusinessException(ResponseStatus.FORBIDDEN)
        save_or_update(form, form.advisor_id)
        portal.consume()
        advisorBankAccountService
            .send_bank_account_portal_consumed_notification(portal)
        update_advisor_profile(
            advisor_patch, form.advisor_id,
        )
    }

    fun save_or_update(
        form: AdvisorPaymentForm,
        advisor_id: Int,
    ): AdvisorPaymentForm {
        form.advisor_id = advisor_id
        form.bank_account?.let {
            it.advisor_id = advisor_id
        }

        validate(form)

        transactionTemplate.extExecute {
            form.bank_account?.let {
                advisorBankAccountService.save_or_update(it)
                form.bank_account_id = it.id
            }

            internal_save_or_update(form)
        }
        return form
    }

    /**
     * either unknown pay method, or unknown schema version
     * is considered valid
     * I think this way, we don't need to update backend code immediately
     * when a new pay method is added.
     * Anyway, let the front end to decide the json format might not be a good idea.
     */
    private fun validate(form: AdvisorPaymentForm) {
        with(form) {
            val method = this.pay_method ?: return

            if (method == AdvisorPayMethod.DIRECT_DEPOSIT) {
                biz_require(
                    this.bank_account != null
                            || this.bank_account_id.is_valid_id
                ) {
                    "invalid bank account"
                }
            } else {
                val json = this.payment_detail_json_str ?: return
                // db maximum size 65535
                // I roughly limit it to 10000 as I don't want to do the encoding math.
                biz_require(json.length < 10000) {
                    "data too long"
                }
                biz_require(
                    PaymentDetail.validate(
                        json,
                        method,
                        this.payment_detail_version
                    )
                ) {
                    "invalid json schema"
                }
            }
        }
    }

    private fun internal_save_or_update(form: AdvisorPaymentForm) {
        val existed = if (form.id.is_valid_id) {
            AdvisorPaymentForm.get(form.id)
        } else {
            one_by_advisor_id(
                form.advisor_id,
                form.project_sub_type,
            )
        }
        if (existed == null) {
            AdvisorPaymentForm.save(form)
        } else {
            form.id = existed.id
            AdvisorPaymentForm.update(form)
        }
    }

    private fun one_by_advisor_id(
        advisor_id: Int,
        project_sub_type: Project.SubType,
    ): AdvisorPaymentForm? {
        return AdvisorPaymentForm.firstOrNull(
            AdvisorPaymentForm.Query(
                advisor_id = advisor_id,
                project_sub_type = project_sub_type,
            )
        )
    }

    /**
     * - Physical Country (dropdown with type-ahead of all countries alphabetically from A-Z with the United States at the top)
     * - This should default to the country we have on file but the user can change it
     * - If they change it, it will update their profile
     */
    internal fun update_advisor_profile(
        patch: Patch<Advisor>,
        advisor_id: Int,
    ) {
        patch.entity.id = advisor_id
        val allow_update_fields = Includes.setOf(Advisor::location_id)
        val p = patch
            .explicitFields(patch.fields.intersect(allow_update_fields))
        Advisor.patch(p)
    }

}
