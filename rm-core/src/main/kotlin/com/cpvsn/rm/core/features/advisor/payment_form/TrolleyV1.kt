package com.cpvsn.rm.core.features.advisor.payment_form

data class TrolleyV1(
    val account_holder_name: String?,
    val account_number: String?,
    val bank_address: String?,
    val bank_city: String?,
    val bank_id: String?,
    val bank_name: String?,
    val bank_postal_code: String?,
    val bank_region_code: String?,
    val branch_id: String?,
    val country: String?,
    val currency: String?,
    val iban: String?,
    val route_type: String?,
    val swift_bic: String?,
) : PaymentDetail {
    override val version: Int
        get() = 1
}
