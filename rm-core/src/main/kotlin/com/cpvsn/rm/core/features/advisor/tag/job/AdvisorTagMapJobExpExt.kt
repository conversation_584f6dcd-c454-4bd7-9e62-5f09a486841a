package com.cpvsn.rm.core.features.advisor.tag.job

import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.search.doctype.AdvisorDoc
import com.cpvsn.rm.core.features.search.doctype.AdvisorJobPart

fun AdvisorDoc.latest_former_company_name(): String? {
    val (
        @Suppress("UNUSED_VARIABLE")
        current_jobs,
        former_jobs
    ) = this.jobs.orEmpty().partition { it.is_current }

    return former_jobs.sortedWith(
        compareByDescending<AdvisorJobPart> { it.local_end_date }
            .thenByDescending { it.end_year }
    ).firstOrNull()?.company
}

fun Advisor.latest_former_company_name(): String? {
    val (
        @Suppress("UNUSED_VARIABLE")
        current_jobs,
        former_jobs
    ) = this.jobs.orEmpty().partition { it.is_current }

    return former_jobs.sortedWith(
        compareByDescending<AdvisorJob> { it.local_end_date }
            .thenByDescending { it.end_year }
    ).firstOrNull()?.company?.name
}
