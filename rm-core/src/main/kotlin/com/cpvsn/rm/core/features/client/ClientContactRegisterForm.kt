package com.cpvsn.rm.core.features.client

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.annotation.TableDefinition
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.pojo.ClientPortalJsonView
import com.cpvsn.rm.core.features.portal.client_user.ClientPortalUser
import com.fasterxml.jackson.annotation.JsonView
import java.time.Instant

@TableDefinition(indices = [
    TableDefinition.IndexDefinition(columns = ["client_portal_user_id"], type = TableDefinition.IndexType.UNIQUE, uniqueValidation = true),
])
class ClientContactRegisterForm : RmEntity() {

    companion object : RmCompanion<ClientContactRegisterForm>()

    @Column
    var client_portal_user_id: Int = 0

    @Column
    @JsonView(ClientPortalJsonView::class)
    var client_name: String = ""

    @Column
    @JsonView(ClientPortalJsonView::class)
    var client_type: Client.Type? = null

    @Column
    @JsonView(ClientPortalJsonView::class)
    var client_location_id: Int? = null

    @Column
    @JsonView(ClientPortalJsonView::class)
    var given_name: String = ""

    @Column
    @JsonView(ClientPortalJsonView::class)
    var family_name: String = ""

    @Column
    @JsonView(ClientPortalJsonView::class)
    var contact_location_id: Int? = null

    @Column
    @JsonView(ClientPortalJsonView::class)
    var status: Status = Status.INITIAL

    @Column
    var review_user_id: Int? = null

    @Column
    @JsonView(ClientPortalJsonView::class)
    var comment: String? = null

    @Column
    @JsonView(ClientPortalJsonView::class)
    var remark: String? = null

    @Column
    override var create_at: Instant? = null

    @Column
    override var update_at: Instant? = null

    @Relation
    @JsonView(ClientPortalJsonView::class)
    var client_portal_user: ClientPortalUser? = null

    enum class Status {
        INITIAL,
        ;
    }

    data class Query(
            @Criteria.Eq
            val id: Int? = null,
            @Criteria.IdsIn
            val ids: Set<Int>? = null,
            @Criteria.Eq
            val client_portal_user_id: Int? = null,
    ) : BaseQuery<ClientContactRegisterForm>()
}
