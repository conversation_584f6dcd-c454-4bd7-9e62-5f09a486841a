package com.cpvsn.rm.core.features.client

import com.cpvsn.rm.core.annotation.OpenApiDefaultViewExclusion
import com.cpvsn.rm.core.base.pojo.BaseDtoCompanion
import com.cpvsn.rm.core.base.pojo.BaseEntityDto
import com.cpvsn.rm.core.base.pojo.ToolsApiView
import com.cpvsn.rm.core.base.pojo.ToolsEmailValidationApiView
import com.fasterxml.jackson.annotation.JsonView
import java.math.BigDecimal
import java.time.Instant

@OpenApiDefaultViewExclusion
class ClientDto : BaseEntityDto<Client>() {
    companion object : BaseDtoCompanion<ClientDto, Client>()

    @get:JsonView(
        ToolsEmailValidationApiView::class,
        ToolsApiView::class
    )
    var name: String? = null

    @get:JsonView(
        ToolsEmailValidationApiView::class,
        ToolsApiView::class
    )
    var type: Client.Type? = null

    @get:JsonView(
        ToolsEmailValidationApiView::class,
        ToolsApiView::class
    )
    var compliance_status: Client.ComplianceStatus = Client.ComplianceStatus.NOT_YET

    @get:JsonView(
        ToolsEmailValidationApiView::class,
        ToolsApiView::class
    )
    var status: Client.Status = Client.Status.PROSPECT

    var registered_address: String? = null

    var business_address: String? = null

    @get:JsonView(ToolsEmailValidationApiView::class)
    var am_team_id: Int = 0

    var require_project_code: Boolean = false

    var require_pre_call_ca: Boolean = false

    var require_post_call_ca: Boolean = false

    var require_list_only_ca: Boolean = false

    var ndb_id: Int? = null

    var location_id: Int? = null

    var show_nominal_client_charges: Boolean = false

    var potential: Client.Potential? = null

    var deal_potential: BigDecimal? = null

    var close_time: Instant? = null

    var enable_client_portal: Boolean = false

    var client_portal_require_registration: Boolean = false

    var disallow_complete_task: Boolean = false

    var cik: Int? = null

    var whale_wisdom_filer_id: Int? = null

}
