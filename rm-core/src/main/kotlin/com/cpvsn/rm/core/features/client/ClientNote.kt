package com.cpvsn.rm.core.features.client

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.annotation.Table
import com.cpvsn.crud.orm.annotation.TableDefinition
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.user.User
import java.time.Instant

@Table("client_note")
@TableDefinition(indices = [
    TableDefinition.IndexDefinition(columns = ["client_id"]),
    TableDefinition.IndexDefinition(columns = ["client_contact_id"]),
    TableDefinition.IndexDefinition(columns = ["create_by_id"]),
])
class ClientNote : RmEntity(), UserAuditing, SoftDeletable {
    companion object : RmCompanion<ClientNote>()

    @Column
    var client_id: Int = 0

    @Column
    var client_contact_id: Int = 0

    @Column
    var assignee_id: Int? = 0

    @Column
    var priority: Priority? = Priority.LOW

    @Column
    var content: String = ""

    @Column
    var status: Status = Status.INITIAL

    /**
     * date for reminder
     */
    @Column
    var reminder_at: Instant? = null

    /**
     * date for completion
     */
    @Column
    var complete_at: Instant? = null

    @Column
    override var create_by_id: Int = 0


    //region +
    // we don't store this value currently
    override var update_by_id: Int = 0

    override var delete_at: Instant? = null

    @Relation
    var client: Client? = null

    @Relation
    var client_contact: ClientContact? = null

    @Relation
    var create_by: User? = null

    @Relation
    var update_by: User? = null

    @Relation(reference = "assignee_id")
    var assignee: User? = null
    //endregion

    enum class Status {
        INITIAL,
        COMPLETED,
        ON_HOLD,
        IN_PROGRESS,
        FOLLOW_UP
        ;
    }

    enum class Priority {
        URGENT,
        HIGH,
        MEDIUM,
        LOW
    }

    data class Query(
            @Criteria.Eq
            val id: Int? = null,
            @Criteria.IdsIn
            val ids: Set<Int>? = null,

            @Criteria.Eq
            val client_id: Int? = null,
            @Criteria.IdsIn
            val client_ids: Int? = null,

            @Criteria.Eq
            val client_contact_id: Int? = null,
            @Criteria.IdsIn
            val client_contact_ids: Int? = null,

            @Criteria.Eq
            val create_by_id: Int? = null,
            @Criteria.IdsIn
            val create_by_ids: Int? = null,

            @Criteria.Eq
            val status: Status? = null,
            @Criteria.In
            val status_in: Set<Status>? = null,
            @Criteria.NotIn
            val status_not_in: Set<Status>? = null,

            @Criteria.Eq
            val priority: Priority? = null,
            @Criteria.In
            val priority_in: Set<Priority>? = null,
            
            @Criteria.Eq
            val assignee_id: Int? = null,
            @Criteria.IdsIn
            val assignee_ids: Set<Int>? = null,

            @Criteria.Gte
            val reminder_at_gte: Instant? = null,
            @Criteria.Lte
            val reminder_at_lte: Instant? = null,
            @Criteria.Gte
            val complete_at_gte: Instant? = null,
            @Criteria.Lte
            val complete_at_lte: Instant? = null,
    ) : BaseQuery<ClientNote>()

}
