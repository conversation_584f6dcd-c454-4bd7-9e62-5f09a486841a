package com.cpvsn.rm.core.features.client.accountnote

import com.cpvsn.rm.core.annotation.OpenApiDefaultViewExclusion
import com.cpvsn.rm.core.base.pojo.BaseDtoCompanion
import com.cpvsn.rm.core.base.pojo.BaseEntityDto
import com.cpvsn.rm.core.base.pojo.ToolsApiView
import com.fasterxml.jackson.annotation.JsonView

@OpenApiDefaultViewExclusion
class ClientAccountNoteDto : BaseEntityDto<ClientAccountNote>() {
    companion object : BaseDtoCompanion<ClientAccountNoteDto, ClientAccountNote>()

    @get:JsonView(ToolsApiView::class)
    var client_id: Int = 0

    @get:JsonView(ToolsApiView::class)
    var client_contact_id: Int = 0

    @get:JsonView(ToolsApiView::class)
    var project_id: Int = 0

    @get:JsonView(ToolsApiView::class)
    var target_type: ClientAccountNote.TargetType = ClientAccountNote.TargetType.CLIENT

    @get:JsonView(ToolsApiView::class)
    var content: String = ""

    @get:JsonView(ToolsApiView::class)
    var tag_refs: List<ClientAccountNoteTagRefDto>? = null

    @get:JsonView(ToolsApiView::class)
    var communication_method: ClientAccountNote.CommMethod? = null
}