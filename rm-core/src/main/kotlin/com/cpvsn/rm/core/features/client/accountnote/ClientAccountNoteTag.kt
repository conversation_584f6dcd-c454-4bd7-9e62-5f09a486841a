package com.cpvsn.rm.core.features.client.accountnote

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import java.time.Instant

class ClientAccountNoteTag : RmEntity(), SoftDeletable {
    companion object : RmCompanion<ClientAccountNoteTag>()

    @Column
    var name: String = ""

    @Column(insertable = false, updatable = false)
    override var delete_at: Instant? = null


    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Eq
        val name: String? = null,
        @Criteria.Contains
        val name_contains:String?=null

        ) : BaseQuery<ClientAccountNoteTag>()
}