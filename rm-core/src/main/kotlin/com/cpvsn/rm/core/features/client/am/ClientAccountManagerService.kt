package com.cpvsn.rm.core.features.client.am

import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.cn_migration.pojos.CnResponse
import com.cpvsn.rm.core.features.user.team.TeamMember
import org.springframework.stereotype.Service


@Service
class ClientAccountManagerService : RmBaseRepository<ClientAccountManager>(),
    JdbcEntityBatchRepo<Int, ClientAccountManager> {

    override val batchDao: JdbcEntityBatchDao<Int, ClientAccountManager> by lazy {
        JdbcEntityBatchDao(ClientAccountManager::class, dataSource)
    }

    override fun handleJoin(
            list: List<ClientAccountManager>,
            include: Set<String>
    ): List<ClientAccountManager> {
        val res = super.handleJoin(list, include)
        if (include.contains(ClientAccountManager::team.name)) {
            val team_members = TeamMember.findAll(TeamMember.Query(
                    member_ids = res.ids(ClientAccountManager::uid),
            ), include = setOf(TeamMember::team.name))
            res.forEach { account_manager ->
                account_manager.team = team_members.find { it.member_id == account_manager.uid }?.team
            }
        }
        return res
    }
}
