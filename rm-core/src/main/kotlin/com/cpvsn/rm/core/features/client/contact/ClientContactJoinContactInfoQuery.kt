package com.cpvsn.rm.core.features.client.contact

import com.cpvsn.crud.mybatis.sqlprovider.MethodNameSqlProvider
import com.cpvsn.rm.core.features.advisor.AdvisorJoinContactInfoQuery
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo

data class ClientContactJoinContactInfoQuery(
        var linkedin_url: String? = null,
        var phone: String? = null,
        var email: String? = null,
) : MethodNameSqlProvider {

    // we use property instead of local variable to avoid sql injection
    val linkedin_url_suffix = linkedin_url?.let {
        "%${AdvisorJoinContactInfoQuery.retrieve_unq(it)}"
    }

    // we use property instead of local variable to avoid sql injection
    val linkedin_url_suffix_with_slash = linkedin_url_suffix?.let {
        "$it/"
    }

    // similar to com.cpvsn.web.cm.service.impl.ConsultantServiceImpl#get_exist_if_possible
    @Suppress("unused")
    fun one_by_contact_info(): String {
        val conditions = mutableListOf<String>()
        linkedin_url_suffix?.takeUnless { it.isBlank() }?.let {
            conditions.add("(c.type = '${ContactInfo.Type.LINKEDIN_URL}' and (c.value like #{linkedin_url_suffix} or c.value like #{linkedin_url_suffix_with_slash}))")
        }
        phone?.takeUnless { it.isBlank() }?.let {
            conditions.add("(c.type = '${ContactInfo.Type.PHONE}' and c.value = #{phone})")
        }
        email?.takeUnless { it.isBlank() }?.let {
            conditions.add("(c.type = '${ContactInfo.Type.EMAIL}' and c.value = #{email})")
        }

        val condition = if (conditions.isEmpty())
            "1=0"
        else
            conditions.joinToString(" or ")

        return """
            select 
                cc.* 
            from 
                client_contact cc
                left join contact_info c on cc.id = c.owner_id and c.owner_type = '${ContactInfo.OwnerType.CLIENT_CONTACT}'
            where 1=1
                and c.delete_at = 0
                and ($condition)
            limit 1
        """.trimIndent()
    }

}
