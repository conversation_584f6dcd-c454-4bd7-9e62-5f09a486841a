package com.cpvsn.rm.core.features.cn_migration.entity

import com.cpvsn.core.base.entity.BaseCompanion
import com.cpvsn.core.base.entity.BaseEntity
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Table
import com.cpvsn.crud.query.Criteria
import com.cpvsn.crud.spring.factory.DataSourceQualifier

@DataSourceQualifier("ndbDataSource")
@Table("ndb_project_consultation_task_compliance")
class NdbProjectConsultationTaskCompliance : BaseEntity() {
    companion object : BaseCompanion<NdbProjectConsultationTaskCompliance>()

    @Column
    var taskid: Int = 0

    @Column
    var uid: Int = 0

    @Column
    var createtime: Long = 0

    @Column
    var updatetime: Long = 0

    /*
    copy from ks_ndb_api: NdbProjectConsultationTaskComplianceConst

    public final static int STATUS_NO_NEED_APPROVE = 0;
    public final static int STATUS_TBA = 1;
    public final static int  STATUS_APPROVAL = 2;
    public final static int  STATUS_REJECTED = 3;
    */
    @Column
    var internalapproval: Int = 1

    @Column
    var externalapproval: Int = 1

    @Column
    var internalnotes: String = ""

    @Column
    var externalnotes: String = ""

    @Column
    var pretrialapprove: Int = 0

    @Column
    var pretrailnotes: String = ""

    @Column
    var CRE_DT: Long? = null

    @Column
    var CRE_UID: Int? = null

    @Column
    var UPD_TS: Long? = null

    @Column
    var UPD_UID: Int? = null

    @Column
    var pretrial_update_time: Long? = null

    @Column
    var pretrial_update_user: Int? = null

    @Column
    var clientapprove_update_time: Long? = null

    @Column
    var clientapprove_update_user: Int? = null

    @Column
    var clientapprove_way: Int? = null

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Eq(columnName = "taskid")
        val task_id: Int? = null,
        @Criteria.IdsIn(columnName = "taskid")
        val task_ids: Set<Int>? = null,
    ) : BaseQuery<NdbProjectConsultationTaskCompliance>()
}