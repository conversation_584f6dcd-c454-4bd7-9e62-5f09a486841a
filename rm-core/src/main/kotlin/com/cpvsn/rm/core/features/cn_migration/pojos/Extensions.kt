package com.cpvsn.rm.core.features.cn_migration.pojos

import com.cpvsn.core.base.entity.BaseEntity
import com.cpvsn.rm.core.base.model.MaybeHasNdbID

fun Set<Int>.to_ids_str(): String {
    return this.toList().sorted().joinToString(",")
}

/**
 * 相同ndb_id的只保留最新的记录(id更大的)
 */
fun <T> List<T>.normalize(): List<T>
        where T : BaseEntity, T : MaybeHasNdbID {
    if (this.isEmpty()) return emptyList()
    val map = this.groupBy { it.ndb_id }
        .mapValues {
            it.value.maxByOrNull { e -> e.id }!!
        }
    return map.values.toList().sortedBy { it.id }
}

fun String?.orEmptyIds0(): String {
    return if (this.isNullOrBlank()) {
        "0"
    } else {
        this
    }
}