package com.cpvsn.rm.core.features.cn_migration.pojos

import kotlin.reflect.KClass

enum class MigrationTarget(
    val corresponding_request_clazz: KClass<*>
) {
    Client(FetchCnReq.Client::class),
    ClientContracts(FetchCnReq.ClientContracts::class),
    ClientAgreements(FetchCnReq.ClientAgreements::class),
    Projects(FetchCnReq.Projects::class),

    TasksConsultation(FetchCnReq.TasksConsultation::class),
    TasksCommon(FetchCnReq.TasksCommon::class),
    Revenues(FetchCnReq.Revenues::class),
    TasksCa(FetchCnReq.TasksCa::class),
    TasksComplianceApproval(FetchCnReq.TasksComplianceApproval::class),

    Consultants(FetchCnReq.Consultants::class),
    ConsultantJobs(FetchCnReq.ConsultantJobs::class),
    ConsultantTclist(FetchCnReq.ConsultantTclist::class),

    Employees(FetchCnReq.Employees::class),
    Companies(FetchCnReq.Companies::class),
    Files(FetchCnReq.Files::class)
}