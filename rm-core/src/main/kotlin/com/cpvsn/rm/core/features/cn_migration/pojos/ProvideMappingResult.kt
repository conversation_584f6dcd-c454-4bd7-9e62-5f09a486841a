package com.cpvsn.rm.core.features.cn_migration.pojos

import com.cpvsn.core.base.entity.BaseEntity
import com.cpvsn.rm.core.base.model.MaybeHasNdbID

data class ProvideMappingResult<T>(
    val mapping: Map<Int?, T>,
    val already_migrated_ndb_ids: Set<Int>,
    val not_migrated_ndb_ids: Set<Int>,
    val newly_migrated: List<T>
) where T : BaseEntity, T : MaybeHasNdbID {
    val already_migrated_all: Boolean
        get() = not_migrated_ndb_ids.isEmpty()
}