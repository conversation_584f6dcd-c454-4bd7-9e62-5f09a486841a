package com.cpvsn.rm.core.features.compliance.tc.advisor

import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.compliance.tc.TcService
import com.cpvsn.rm.core.features.compliance.tc.tpl.TcTemplateService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service


@Service
class AdvisorTcRepo : RmBaseRepository<AdvisorTc>(), JdbcEntityBatchRepo<Int, AdvisorTc> {
    @Autowired
    private lateinit var tcTemplateService: TcTemplateService

    @Autowired
    private lateinit var tcService: TcService

    override val batchDao: JdbcEntityBatchDao<Int, AdvisorTc> by lazy {
        JdbcEntityBatchDao(AdvisorTc::class, dataSource)
    }

    override fun handleJoin(
            list: List<AdvisorTc>,
            include: Set<String>
    ): List<AdvisorTc> {
        val res = super.handleJoin(list, include)

        if (include.contains(AdvisorTc::content.name)) {
            res.forEach {
                it.content = tcTemplateService.process_default_content(it.id)
            }
        }
        if (include.contains(AdvisorTc::contents.name)) {
            res.forEach {
                it.contents = tcTemplateService.process_all_locale(it.id)
            }
        }
        if (include.contains(AdvisorTc::is_current_tc_jit.name)) {
            val current_tc_id_set = tcService.find_current().values.map { it.id }.toSet()
            res.forEach {
                it.is_current_tc_jit = it.tc_id.is_valid_id && it.tc_id in current_tc_id_set
            }
        }
        return res
    }

}
