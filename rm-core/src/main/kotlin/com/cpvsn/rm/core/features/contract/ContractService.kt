package com.cpvsn.rm.core.features.contract

import com.cpvsn.core.svc.email.EmailRequest
import com.cpvsn.core.util.extension.BD
import com.cpvsn.core.util.extension.assert_exist
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.rm.core.extensions.*
import com.cpvsn.rm.core.features.advisor.AdvisorService
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.contract.entity.ContractMaterial
import com.cpvsn.rm.core.features.contract.entity.ContractPaymentItem
import com.cpvsn.rm.core.features.contract.pojo.ManuallyAdjustContractStartTimeRequest
import com.cpvsn.rm.core.features.contract.pojo.ManuallyAdjustContractStartTimeResponse
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModelIds
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.finance.revenue.RevenueService
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import com.cpvsn.rm.core.util.biz_check
import com.cpvsn.rm.core.util.biz_require
import com.cpvsn.web.auth.AuthContext
import kong.unirest.Unirest
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import java.math.BigDecimal
import java.net.URLConnection
import java.time.Instant

@Service
class ContractService {
    //region @
    private val log = LoggerFactory.getLogger(this.javaClass)

    @Autowired
    private lateinit var revenueService: RevenueService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var advisorService: AdvisorService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate
    //endregion

    //region crud
    @Transactional
    fun patch_cascade(
            patch: Patch<Contract>,
            cascades: Set<Cascade<Contract, *>> = setOf(
                    Cascade.oneToMany(
                            Contract::payment_term,
                            // update payway is not allowed (this logic is copied from old code)
                            patchFields = ContractPaymentItem.updatableFields - ContractPaymentItem::payway.name,
                            callback = Cascade.Callback.prePersist { e, parent ->
                                // set payway before save
                                e.payway = parent.payway
                            }),
                    Cascade.oneToMany(
                            Contract::materials,
                            // we can only save or update material here
                            actions = setOf(Cascade.Action.SAVE, Cascade.Action.UPDATE)
                    ),
            ),
            include: Set<String> = emptySet(),
    ): Contract {
        val includes = include.plus((Contract::billing_contacts dot ClientContact::contact_infos).name)
        return Contract.patchThenGet(patch, cascades, include = includes)
    }

    @Transactional
    fun save_cascade_then_request(entity: Contract): Contract {
        val res = save_cascade(entity)
        request_file(res.id)
        return res
    }

    @Transactional
    fun save_cascade(entity: Contract): Contract {
        entity.creator_uid = AuthContext.get<User>().id
        return Contract.save(entity, cascades = setOf(
                Cascade.Save.oneToMany(Contract::payment_term, callback = Cascade.Callback.prePersist { e, parent ->
                    e.payway = parent.payway
                }),
                Cascade.Save.oneToMany(Contract::materials),
        ))
    }

    fun delete_cascade(id: Int) {
        transactionTemplate.execute {
            // check if it can be deleted.
            val revenues = Revenue.findAll(Revenue.Query(
                    contract_id = id
            ))
            biz_check(revenues.none { it.is_invoiced }) { "Not allowed to delete contracts that have been invoiced." }
            biz_check(revenues.none { it.is_locked }) { "Not allowed to delete contracts that associated with cutoff revenues." }

            // cascade delete
            Contract.delete(id, cascades = setOf(
                    Cascade.Delete.oneToMany(Contract::payment_term),
                    Cascade.Delete.oneToMany(Contract::materials),
            ))

            // unbind revenues
            revenues.forEach {
                revenueService.unbind_revenue(it)
            }
        }
    }
    //endregion

    //region find
    fun resolve_task_if_assigned_contract(task_id: Int): Contract? {
        val task = Task.get(task_id).assert_exist(task_id)
        val project = Project.get(task.project_id, Includes.setOf(Project::contract))
        return project.contract
    }

    private fun is_match_effective_longtime_prepay(
        task_time: Instant,
        contract: Contract
    ): Boolean {
        val is_effective_longtime_prepay = contract.payway == PayWay.PREPAY
                && contract.is_good_until_usage_expired
                && contract.effective_status == Contract.EffectiveStatus.EFFECTIVE
        if (!is_effective_longtime_prepay) return false
        return task_time > contract.start_time
    }

    /**
     * If there's effective contract match the time, return the effective contract;
     * else if use the previous expired contract if exists
     * else use former bound contract if exist
     */
    fun resolve_task_binding_by_time_match(
        task_id: Int,
        supposed_time: Instant? = null,
        task: Task? = null,
    ): Contract? {
        val task = task ?: Task.get(task_id).assert_exist(task_id)
        val task_time = supposed_time ?: task.start_time ?: return null
        val client_id = task.client_id.assert_valid_id()

        val effective_contracts = Contract.findAll(
            Contract.Query(
                client_id = client_id,
                effective_status = Contract.EffectiveStatus.EFFECTIVE,
                payway_ne = PayWay.PROJECT_BASE,
            )
        )

        var matched = effective_contracts.filter {
            (task_time > it.start_time && task_time <= it.end_time) || is_match_effective_longtime_prepay(task_time, it)
        }

        if (matched.isEmpty()) {
            val expired_contracts = Contract.findAll(
                Contract.Query(
                    client_id = client_id,
                    approval_status = Contract.ApprovalStatus.APPROVED,
                    effective_status = Contract.EffectiveStatus.EXPIRED,
                    payway_ne = PayWay.PROJECT_BASE,
                )
            ).sortedByDescending { it.end_time }
            matched = expired_contracts.firstOrNull()?.let { listOf(it) }.orEmpty()
        }

        return if (matched.isEmpty()) {
            val exist_revenue = Revenue.findAll(
                Revenue.Query(
                    task_id = task_id,
                    create_type = Revenue.CreateType.FULL
                ), Includes.setOf(Revenue::contract)
            )
            val former_contract = exist_revenue.firstOrNull()?.contract
            former_contract
        } else {
            matched.ensure_at_most_one(
                "Appliable contracts for task matched by time found more than one, please check client contract list."
            )
        }
    }

    fun resolve_transfer_successor(contract: Contract): Contract? {
        return Contract.findAll(Contract.Query(
                    client_id = contract.client_id,
                    approval_status = Contract.ApprovalStatus.APPROVED,
                    effective_status = Contract.EffectiveStatus.INEFFECTIVE,
                    payway_ne = PayWay.PROJECT_BASE,
            )).minByOrNull {
            it.start_time
        }
    }
    //endregion

    //region entry process

    fun request_file(
            contract_id: Int,
            note: String = "",
    ): Contract {
        val contract = Contract.get(contract_id)
        val res = Patch.fromMap(contract.id, mapOf(
                Contract::requester_uid to AuthContext.user_id,
                Contract::request_time to Instant.now(),
                Contract::request_note to note,
//                Contract::request_status to Contract.RequestStatus.IN_REQUEST,
                Contract::approval_status to Contract.ApprovalStatus.DRAFT_REQUESTED,
        )).patchThenGet()

        if (!res.ban_capvision_com_emails()) {
            emailService.send(EmailRequestPojo.from_template_result(
                    placeholderBasedEmailTemplateService.process_built_in_by_data_id(
                            EmailContentType.NOTIFY_CONTRACT_REQUEST,
                            PlaceholderBasedModelIds(
                                    client_id = contract.client_id,
                                    contract_id = contract_id
                            )
                    )
            ))
        }

        return res
    }

    fun request_reject(
            contract_id: Int,
            note: String,
    ): Contract {
        val contract = Contract.get(contract_id)
        val res = Patch.fromMap(contract.id, mapOf(
                Contract::generator_uid to AuthContext.user_id,
                Contract::generate_time to Instant.now(),
                Contract::generate_note to note,
//                Contract::request_status to Contract.RequestStatus.REQUEST_REJECTED,
                Contract::approval_status to Contract.ApprovalStatus.REQUEST_REJECTED,
        )).patchThenGet()

        if (!res.ban_capvision_com_emails()) {
            emailService.send(EmailRequestPojo.from_template_result(
                    placeholderBasedEmailTemplateService.process_built_in_by_data_id(
                            EmailContentType.NOTIFY_CONTRACT_REQUEST_REJECTED,
                            PlaceholderBasedModelIds(
                                    client_id = contract.client_id,
                                    contract_id = contract_id
                            )
                    )
            ))
        }

        return res
    }

    @Transactional
    fun request_complete(
            contract_id: Int,
            note: String,
            material: ContractMaterial,
    ): Contract {
        val contract = Contract.get(contract_id)
        val patch = Patch.fromMap(
                contract.id,
                mapOf(
                        Contract::generator_uid to AuthContext.user_id,
                        Contract::generate_time to Instant.now(),
                        Contract::generate_note to note,
//                Contract::request_status to Contract.RequestStatus.REQUEST_COMPLETED,
                        Contract::approval_status to Contract.ApprovalStatus.DRAFT_CREATED,
                        Contract::materials to listOf(material)
                ),
        )
        val res = patch_cascade(
                patch,
                include = Includes.setOf(Contract::materials)
        )

        // send email
        if (!res.ban_capvision_com_emails()) {
            val file_url = material.file_url.assert_exist()
            val fileResponse = Unirest.get(file_url).asBytes()
            val email = EmailRequestPojo.from_template_result(
                    placeholderBasedEmailTemplateService.process_built_in_by_data_id(
                            EmailContentType.NOTIFY_CONTRACT_REQUEST_COMPLETED,
                            PlaceholderBasedModelIds(
                                    client_id = contract.client_id,
                                    contract_id = contract_id
                            )
                    )
            ).copy(
                    attachments = listOf(EmailRequest.Attachment(
                            name = material.file_name.assert_exist(),
                            media_type = URLConnection.guessContentTypeFromName(material.file_name.assert_exist()) ?: "",
                            bytes = fileResponse.body
                    ))
            )
            emailService.send(email)
        }

        return res
    }

    @Transactional
    fun apply_contract(
            contract_id: Int,
            note: String,
            materials: List<ContractMaterial>,
    ): Contract {
        val contract = Contract.get(contract_id)

        // check: at most one applying contract
        val applying_contracts = Contract.findAll(Contract.Query(
                client_id = contract.client_id,
                approval_status = Contract.ApprovalStatus.PENDING_APPROVAL,
                effective_status_ne = Contract.EffectiveStatus.EXPIRED,
                payway_ne = PayWay.PROJECT_BASE,
        ))
        biz_require(applying_contracts.isEmpty()) {
            "There is already applying contract for this client!"
        }

        // check possible time range conflication
        if (contract.payway in setOf(PayWay.TRIAL, PayWay.PREPAY, PayWay.PAYGO)) {
            val approvedContracts = Contract.findAll(Contract.Query(
                    client_id = contract.client_id,
                    approval_status = Contract.ApprovalStatus.APPROVED,
                    effective_status_ne = Contract.EffectiveStatus.EXPIRED,
                    payway_ne = PayWay.PROJECT_BASE,
            ))
            for (crt in approvedContracts) {
                biz_require((contract.start_time to contract.end_time) none_intersection_with (crt.start_time to crt.end_time)) {
                    "Time range of contract conflicts with existing contract time range."
                }
            }
        }

        val res = patch_cascade(
                Patch.fromMap(contract.id, mapOf(
                        Contract::applier_uid to AuthContext.user_id,
                        Contract::apply_time to Instant.now(),
                        Contract::apply_note to note,
                        Contract::approval_status to Contract.ApprovalStatus.PENDING_APPROVAL,
                        Contract::materials to materials,
                )),
                cascades = setOf(
                        Cascade.oneToMany(Contract::materials)
                )
        )

        if (!res.ban_capvision_com_emails()) {
            emailService.send(EmailRequestPojo.from_template_result(
                    placeholderBasedEmailTemplateService.process_built_in_by_data_id(
                            EmailContentType.NOTIFY_CONTRACT_APPLY,
                            PlaceholderBasedModelIds(
                                    client_id = contract.client_id,
                                    contract_id = contract_id
                            )
                    )
            ))
        }

        return res
    }

    @Transactional
    fun apply_reject(
            contract_id: Int,
            note: String,
    ): Contract {
        val contract = Contract.get(contract_id)
        val res = Patch.fromMap(contract.id, mapOf(
                Contract::approver_uid to AuthContext.user_id,
                Contract::approve_time to Instant.now(),
                Contract::approve_note to note,
                Contract::approval_status to Contract.ApprovalStatus.REJECTED
        )).patchThenGet()

        if (!res.ban_capvision_com_emails()) {
            emailService.send(EmailRequestPojo.from_template_result(
                    placeholderBasedEmailTemplateService.process_built_in_by_data_id(
                            EmailContentType.NOTIFY_CONTRACT_APPLY_REJECTED,
                            PlaceholderBasedModelIds(
                                    client_id = contract.client_id,
                                    contract_id = contract_id
                            )
                    )
            ))
        }

        return res
    }

    @Transactional
    fun apply_approve(
            contract_id: Int,
            note: String,
    ): Contract {
        val contract = Contract.get(contract_id)
        val res = Patch.fromMap(contract_id, mapOf(
                Contract::approver_uid to AuthContext.user_id,
                Contract::approve_time to Instant.now(),
                Contract::approve_note to note,
                Contract::approval_status to Contract.ApprovalStatus.APPROVED
        )).patchThenGet()

        if (contract.payway == PayWay.PROJECT_BASE) {
            res.effective_status = Contract.EffectiveStatus.EFFECTIVE
            Contract.patch(Patch.of(res, fields = Includes.setOf(Contract::effective_status)))
        } else {
            val approvedContracts = Contract.findAll(Contract.Query(
                    client_id = contract.client_id,
                    approval_status = Contract.ApprovalStatus.APPROVED,
                    effective_status_ne = Contract.EffectiveStatus.EXPIRED,
                    exclude_project_base = true
            ))
            if (
                    approvedContracts.map { it.start_time to it.end_time }.none_intersection
                    &&
                    Instant.now() in contract.start_time..contract.end_time
            ) {
                turn_effective(contract)
            }
        }

        val client = Client.get(contract.client_id)
        if (client.compliance_status != Client.ComplianceStatus.APPROVED) {
            Client.patch(Patch.fromMap(client.id, mapOf(
                    Client::compliance_status to Client.ComplianceStatus.APPLYING
            )))
        }

        if (!res.ban_capvision_com_emails()) {
            emailService.send(EmailRequestPojo.from_template_result(
                    placeholderBasedEmailTemplateService.process_built_in_by_data_id(
                            EmailContentType.NOTIFY_CONTRACT_APPLY_APPROVED,
                            PlaceholderBasedModelIds(
                                    client_id = contract.client_id,
                                    contract_id = contract_id
                            )
                    )
            ))
        }

        return res
    }

    //endregion

    fun get_used_hours(contract: Contract): BigDecimal {
        val revenues = Revenue.findAll(
            Revenue.Query(
                contract_id = contract.id
            ))
        return revenues.sumBdBy { it.billing_hours }
    }

    fun get_used_size(contract: Contract): BigDecimal {
        val revenues = Revenue.findAll(
            Revenue.Query(
                contract_id = contract.id
            ))
        return revenues.sumBdBy { it.cash ?: 0.BD }
    }

    //region effectiveness
    @Transactional
    fun turn_expire(
            contract: Contract,
    ) {
        Patch.fromMap(contract.id, mapOf(
                Contract::effective_status to Contract.EffectiveStatus.EXPIRED
        )).patch()


        val effective_contracts = Contract.findAll(
                Contract.Query(
                        client_id = contract.client_id,
                        approval_status = Contract.ApprovalStatus.APPROVED,
                        effective_status = Contract.EffectiveStatus.EFFECTIVE,
                        payway_ne = PayWay.PROJECT_BASE,
                )
        )
        if (effective_contracts.isEmpty()) {
            Patch.fromMutator(Client()) {
                id = contract.client_id
                status = Client.Status.EXPIRED
            }.patch()
        }

        InvokeUtil.trigger(Event.CONTRACT_TURN_EXPIRED(contract_id = contract.id))
    }

    @Transactional
    fun turn_effective(
            contract: Contract,
    ) {
        biz_require(contract.start_time.lte(Instant.now())
                && contract.end_time.gte(Instant.now()))

        Patch.fromMap(contract.id, mapOf(
                Contract::effective_status to Contract.EffectiveStatus.EFFECTIVE
        )).patch()
        Patch.fromMutator(Client()) {
            id = contract.client_id
            status = Client.Status.EXECUTE
        }.patch()

        // check status
        val effective_list = Contract.findAll(Contract.Query(
                client_id = contract.client_id,
                effective_status = Contract.EffectiveStatus.EFFECTIVE,
                payway_ne = PayWay.PROJECT_BASE,
        ))
        biz_check(effective_list.size == 1) {
            "More than one non project-base contract are effective after turn effective."
        }
        InvokeUtil.trigger(Event.CONTRACT_TURN_EFFECTIVE(contract_id = contract.id))
    }


    /**
     * From ping:
     * For approved contracts, even if the effective date has not yet arrived,
     * certain personnel are allowed to manually activate them.
     *
     * In this case, if the end date of the previously active contract has not yet passed,
     * the end date of the previous contract should be updated,
     * and then the contract should be terminated.
     */
    @Transactional
    fun adjust_contract_start_time(
        request: ManuallyAdjustContractStartTimeRequest
    ): ManuallyAdjustContractStartTimeResponse {
        val contract = Contract.get(request.contract_id)
        biz_check(contract.approval_status == Contract.ApprovalStatus.APPROVED) {
            "Contract must be approved"
        }
        // update current effective contract's end_time if needed
        var current_effective = Contract.findAll(
            Contract.Query(
                client_id = contract.client_id,
                approval_status = Contract.ApprovalStatus.APPROVED,
                effective_status = Contract.EffectiveStatus.EFFECTIVE,
                payway_ne = PayWay.PROJECT_BASE,
            )
        ).firstOrNull()
        if (current_effective != null) {
            if (current_effective.end_time.isAfter(request.adjusted_start_time)) {
                current_effective = Patch.fromMutator(current_effective) {
                    this.end_time = request.adjusted_start_time
                }.patchThenGet()
            }
        }

        Patch.fromMutator(contract) {
            this.start_time = request.adjusted_start_time
        }.patch()

        return ManuallyAdjustContractStartTimeResponse(
            contract,
            current_effective
        )
    }

    @Transactional
    fun change_to_expired(
            contract_id: Int,
    ): Contract {
        val contract = Contract.get(contract_id)
        biz_require(contract.effective_status == Contract.EffectiveStatus.EFFECTIVE) {
            "Only those in effective status can be changed to expired status"
        }
        turn_expire(contract)
        return Contract.get(contract_id)
    }
    //endregion
}
