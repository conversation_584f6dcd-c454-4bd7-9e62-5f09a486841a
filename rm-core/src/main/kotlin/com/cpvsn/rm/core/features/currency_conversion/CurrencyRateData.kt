package com.cpvsn.rm.core.features.currency_conversion

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import java.math.BigDecimal

class CurrencyRateData : RmEntity() {

    companion object : RmCompanion<CurrencyRateData>()

    @Column
    var from_currency: String? = null

    var from_currency_enum: ISOCurrency? by PropDelegates.enum_nullable(this::from_currency)

    @Column
    var to_currency: String? = null

    var to_currency_enum: ISOCurrency? by PropDelegates.enum_nullable(this::to_currency)

    @Column
    var rate: BigDecimal? = null

    data class Query(
        @Criteria.Eq
        var id: Int? = null,
        @Criteria.IdsIn
        var ids: Set<Int>? = null,
        @Criteria.Eq
        var from_currency: String? = null,
        @Criteria.In
        var from_currency_in: Set<String>? = null,
        @Criteria.Eq
        var to_currency: String? = null
    ) : BaseQuery<CurrencyRateData>()

}