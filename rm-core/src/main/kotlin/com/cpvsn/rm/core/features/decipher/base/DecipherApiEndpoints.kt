package com.cpvsn.rm.core.features.decipher.base

import org.springframework.http.HttpMethod

/**
 * @see <a href="https://docs.developer.focusvision.com/docs/decipher/api">Decipher Rest Api</a>
 */

object DecipherApiEndpoints {

    data class Endpoint(
        val url: String,
        val httpMethod: HttpMethod,
    )

    const val BASE_URL = "https://sw2.decipherinc.com/api/v1"

    fun getSurvey(
        surveyId: String,
        fields: String = "",
    ) = Endpoint(
        url = "$BASE_URL/surveys/$surveyId/data$fields",
        httpMethod = HttpMethod.GET
    )
}