package com.cpvsn.rm.core.features.decipher.restapi

import com.fasterxml.jackson.annotation.JsonAnySetter
import com.fasterxml.jackson.annotation.JsonProperty


/**
 * The advisor_id is stored as either "ID" or "s{project_id}" depending on whether
 * experts come directly from the DB or are redirected from a 3rd party hosted survey
 * @param id The "ID" property of the Decipher REST API response.
 * This is often eqwuivalent to advisor_id, but sometimes advisor_id is stored with a key called "s{project_id}".
 * @param status Shows if this entry of this expert in this survey is qualified, terminated, or overquota.
 * @param list The source of the expert - Capvision experts are typically in list 1 or 3.
 * Outside sources of experts are often in list 2, and tests are usually in list 0.
 * @param dynamicProperties Contains values from the Decipher REST API not defined as parameters.
 * This will include "s{project_id}", which is the JSON key in Decipher for the advisor_id in many Capvision-hosted projects.
 */
data class DecipherGetSurveyResponse(
    //ID is capitalized to match the format of the decipher output
    @JsonProperty("ID")
    val id: String = "",

    val status: String = "",

    val list: String = "",

    //Since the advisor_id value is stored in a variable named "s{project_id}"
    //we need to have a place to store that value without knowing the variable name
    //this should contain the ID variable for self-hosted surveys
    //TODO add description to 'Result' field?
    //We don't return a result field in this iteration. It could be helpful for front-end debugging
    var dynamicProperties: MutableMap<String, Any> = mutableMapOf()
) {

    companion object {
        val CAPVISION_SOURCES = setOf(1, 3)
    }

    @JsonAnySetter
    fun setDynamicProperty(key: String, value: Any) {
        //I'm pretty sure this method's use is determined by the @JsonAnySetter annotation
        dynamicProperties[key] = value
    }

    fun getCustomSidValue(project_id: Int): Int? {
        val value = dynamicProperties["s$project_id"]
        return (value as? String)?.toIntOrNull() //Decipher returns the DynamicProperty values as strings, so we have to turn them into Ints
    }

    /**
     *getAdvisorId() takes the custom ID variable from dynamicProperties if possible.
     *If a project has a vendor, sometimes the vendor will have "ID" as the advisor_id variable name.
     *In that case, we'd want to return the "s$project_id" variable - if that exists, it's always the correct ID variable.
     * @param project_id The Capvision project ID
     * @return The advisor_id for this Decipher entry, whether it's the "ID" value or the "s{project_id}" value.
     */
    fun getAdvisorId(project_id: Int): Int? {
        val custom_id: Int? = getCustomSidValue(project_id)
        return custom_id ?: id.toIntOrNull()
    }

    fun is_capvision_sourced(): Boolean {
        return list.toIntOrNull() in CAPVISION_SOURCES
    }
}