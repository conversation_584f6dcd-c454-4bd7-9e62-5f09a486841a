package com.cpvsn.rm.core.features.email_template.constant

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.config.EmailProperties
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.am.ClientAccountManager
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntryService
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.email_template.EmailAddressModel
import com.cpvsn.rm.core.features.email_template.EmailAddressModel.Companion.to_email_address_model
import com.cpvsn.rm.core.features.email_template.EmailAddressModel.Companion.to_email_address_model_list
import com.cpvsn.rm.core.features.email_template.EmailAddressModel.Companion.to_outreach_email_address_model_list
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import com.cpvsn.rm.core.features.portal.clientlegal.PortalClientComplianceService
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectClientContact
import com.cpvsn.rm.core.features.project.ProjectMember
import com.cpvsn.rm.core.features.right.model.Role
import com.cpvsn.rm.core.features.right.model.UserRole
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.user.User
import org.springframework.beans.factory.getBean

enum class EmailAddressPlaceHolder(
    override val description: String = "",
    override val category: EmailTemplatePlaceholderCategory,
    override val hide_by_default: Boolean = false
) : IEmailAddressPlaceHolder {

    ADVISOR_EMAIL(
        description = "advisor email",
        category = EmailTemplatePlaceholderCategory.ADVISOR
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            return data.advisor?.to_email_address_model_list().orEmpty()
        }
    },

    RECRUITER_EMAIL(
        description = "recruiter email address",
        category = EmailTemplatePlaceholderCategory.USER
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            return data.user?.to_email_address_model_list().orEmpty()
        }
    },

    RECRUITER_OUTREACH_EMAIL(
        description = "recruiter outreach email",
        category = EmailTemplatePlaceholderCategory.USER
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            return data.user?.to_outreach_email_address_model_list().orEmpty()
        }
    },

    //region project
    PROJECT_MANAGER_EMAIL(
        description = "project manager email",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val project = data.project ?: return emptyList()
            if (project.manager?.user == null) {
                Project.join(project, Includes.setOf(Project::manager dot ProjectMember::user))
            }
            return project.manager?.user?.to_email_address_model_list(description = "project manager").orEmpty()
        }
    },

    PROJECT_MEMBER_EMAILS(
        description = "project member email list",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val project = data.project ?: return emptyList()
            if (project.members == null) {
                Project.join(
                    project, Includes.setOf(
                        Project::members dot ProjectMember::user,
                    )
                )
            }
            return project.members.orEmpty().mapNotNull {
                it.user?.to_email_address_model(description = "project member")
            }
        }
    },

    OUTSOURCE_PROJECT_MEMBER_EMAILS(
        description = "outsource project member email list",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            return User.findAll(
                query = User.Query(
                    user_role = UserRole.Query(
                        role = Role.Query(
                            name_in = setOf(Role.Enum.OUTSOURCE_SUPPORTER.value, Role.Enum.OUTSOURCE_MANAGER.value)
                        )
                    )
                )
            ).mapNotNull {
                it.to_email_address_model(description = "outsource project member")
            }
        }
    },

    PROJECT_SUPPORT_MEMBER_EMAILS(
        description = "project support member email list",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val project = data.project ?: return emptyList()
            if (project.members == null) {
                Project.join(
                    project, Includes.setOf(
                        Project::members dot ProjectMember::user,
                    )
                )
            }
            return project.members.orEmpty()
                .filter { it.role == ProjectMember.Role.SUPPORT_MEMBER }
                .mapNotNull {
                    it.user?.to_email_address_model(description = "project support member")
                }
        }
    },

    PROJECT_COMMON_CONTACT_EMAILS(
        description = "project common contact email list",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val project = data.project ?: return emptyList()
            if (project.client_common_contacts == null) {
                Project.join(
                    project,
                    Includes.setOf(Project::project_client_contacts dot ProjectClientContact::client_contact)
                )
            }
            return project.client_common_contacts.orEmpty()
                .mapNotNull { it.to_email_address_model(description = "project common client contact") }
        }
    },

    PROJECT_CLIENT_COMPLIANCE_OFFICERS_EMAILS(
        description = "project client compliance officers email list (providing they don't block email notification)",
        category = EmailTemplatePlaceholderCategory.PROJECT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val project = data.project ?: return emptyList()
            if (project.client_common_contacts == null) {
                Project.join(
                    project,
                    Includes.setOf(Project::project_client_contacts dot ProjectClientContact::client_contact)
                )
            }
            val portalClientComplianceService = data.app_context.getBean<PortalClientComplianceService>()
            return project.client_compliance_officers.orEmpty()
                .filter {
                    // it might be a wrong position to do filter
                    portalClientComplianceService.should_send_notification_email(
                        project_id = project.id,
                        contact_id = it.id
                    )
                }
                .mapNotNull { it.to_email_address_model(description = "project legal client contact") }
        }
    },
    // endregion

    //region task
    TASK_CONTACT_EMAIL(
        description = "task contact email",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val task = data.task ?: return emptyList()
            if (task.client_contact == null) {
                Task.join(task, Includes.setOf(Task::client_contact))
            }
            return listOfNotNull(
                task.client_contact?.to_email_address_model(description = "task contact")
            )
        }
    },

    /**
     * "2. 如果是点击链接，操作后，系统有操作结果通知邮件发送给 Email To Client 原所有接收成员"
     */
    TASK_LATEST_EMAIL_TO_CLIENT_ADVISOR_PICKER_RECIPIENT_EMAIL(
        description = "task latest 'email to client(advisor picker)' recipient email",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val task = data.task ?: return emptyList()
            Task.join(
                task, Includes.setOf(
                    Task::email_records dot CommunicationRecord::email_record,
                    Task::email_records dot CommunicationRecord::contact_portal,
                )
            )
            val record = task.email_records.orEmpty()
                .filter { it.contact_portal?.show_advisor_picker == true }
                .maxByOrNull { it.id } ?: return emptyList()
            return record.email_record?.to_list.orEmpty()
                .plus(record.email_record?.cc_list.orEmpty())
                .map {
                    it.to_email_address_model()
                }
        }
    },

    /**
     * "2. 如果是点击链接，操作后，系统有操作结果通知邮件发送给 Email To Client 原所有接收成员"
     */
    TASK_LATEST_EMAIL_TO_CLIENT_AG_RECIPIENT_EMAIL(
        description = "task latest 'email to client(availability grid)' recipient email",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val task = data.task ?: return emptyList()
            Task.join(
                task, Includes.setOf(
                    Task::email_records dot CommunicationRecord::email_record,
                    Task::email_records dot CommunicationRecord::contact_portal,
                )
            )
            val record = task.email_records.orEmpty()
                .filter { it.contact_portal?.show_availability_grid == true }
                .maxByOrNull { it.id } ?: return emptyList()
            return record.email_record?.to_list.orEmpty()
                .plus(record.email_record?.cc_list.orEmpty())
                .map {
                    it.to_email_address_model()
                }
        }
    },
    // endregion

    // region client
    CLIENT_AM_EMAILS(
        description = "client account manager email list",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val client = data.client ?: return emptyList()
            Client.join(
                client, Includes.setOf(
                    Client::account_managers dot ClientAccountManager::user,
                )
            )
            return client.account_managers.orEmpty()
                .mapNotNull { it.user?.to_email_address_model(description = "client account manager") }
        }
    },

    CLIENT_SEND_APPROVAL_TO_EMAILS(
        description = "send_approval_to_emails stored in compliance preference",
        category = EmailTemplatePlaceholderCategory.CLIENT,
        hide_by_default = true,
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val client = data.client ?: return emptyList()
            Client.join(client, Includes.setOf(Client::compliance_preference))
            return client.compliance_preference
                ?.rule_object
                ?.send_approval_to_emails.orEmpty()
                .map { it.to_email_address_model(description = "send_approval_to_emails stored in compliance preference") }
        }
    },
    // endregion

    // region client contact
    CLIENT_CONTACT_EMAIL(
        description = "contact email",
        category = EmailTemplatePlaceholderCategory.CONTACT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            return data.contact?.to_email_address_model_list().orEmpty()
        }
    },
    // endregion

    //region client portal user
    CLIENT_PORTAL_USER_EMAIL(
        description = "client portal user email",
        category = EmailTemplatePlaceholderCategory.CLIENT_PORTAL_USER,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            return data.client_portal_user?.to_email_address_model_list().orEmpty()
        }
    },

    TASK_SUPPORT_EMAIL(
        description = "task support email",
        category = EmailTemplatePlaceholderCategory.TASK,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val task = data.task ?: return emptyList()
            Task.join(task, Includes.setOf(Task::support))
            return task.support?.to_email_address_model_list(description = "task support")
                .orEmpty()
        }
    },
    //endregion

    //region general user
    GENERAL_USER_EMAIL(
        description = "general user email",
        category = EmailTemplatePlaceholderCategory.GENERAL_USER,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            return data.general_user?.to_email_address_model_list().orEmpty()
        }
    },
    //endregion

    // region contract
    CONTRACT_REQUESTER_EMAIL(
        description = "contract requester email",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            data.contract?.let {
                Contract.join(it, Includes.setOf(Contract::requester))
            }
            return data.contract?.requester?.to_email_address_model_list(description = "contract requester").orEmpty()
        }
    },

    CONTRACT_GENERATOR_EMAIL(
        description = "contract generator email",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            data.contract?.let {
                Contract.join(it, Includes.setOf(Contract::generator))
            }
            return data.contract?.generator?.to_email_address_model_list(description = "contract generator").orEmpty()
        }
    },

    CONTRACT_APPLIER_EMAIL(
        description = "contract applier email",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            data.contract?.let {
                Contract.join(it, Includes.setOf(Contract::applier))
            }
            return data.contract?.applier?.to_email_address_model_list(description = "contract applier").orEmpty()
        }
    },

    CONTRACT_APPROVER_EMAIL(
        description = "contract approver email",
        category = EmailTemplatePlaceholderCategory.CONTRACT,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            data.contract?.let {
                Contract.join(it, Includes.setOf(Contract::applier))
            }
            return data.contract?.approver?.to_email_address_model_list(description = "contract approver").orEmpty()
        }
    },

    // endregion

    // region misc
    DB_SENDER_EMAIL(
        description = "db sender email",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val cur_project = data.project ?: data.task?.let {
                Task.join(it, Includes.setOf(Task::project))
                it.project
            }
            if (cur_project != null) {
                Project.join(cur_project, Includes.setOf(Project::bcg_hub_project))
                if (cur_project.bcg_hub_project != null) {
                    return listOf(
                        EmailAddressModel(
                            email = "<EMAIL>",
                            name = "BCG Sender",
                            type = EmailAddressModel.Type.UNKNOWN,
                        )
                    )
                }
            }
            val properties = data.app_context.getBean<EmailProperties>()
            return properties.dbSender.to_email_address_model_list(name = "db sender")
        }
    },

    COMPLIANCE_LIST_SENDER_EMAIL(
        description = "compliance list sender email",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_COMPLIANCE_LIST_EMAIL)
                ?.to_email_address_model_list(description = "compliance list sender email").orEmpty()
        }
    },

    CAPVISION_AR_GROUP(
        description = "compliance ar team sender email",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_AR_TEAM_EMAIL_GROUP)
                ?.to_email_address_model_list(description = "compliance ar team email").orEmpty()
        }
    },

    CAPVISION_LEGAL_EMAIL_GROUP(
        description = "capvision legal email group",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_COMPLIANCE_OFFICER_EMAIL_GROUP)
                ?.to_email_address_model_list().orEmpty()
        }
    },

    CAPVISION_FINANCE_GROUP(
        description = "capvision finance email group",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_FINANCE_EMAIL_GROUP)
                ?.to_email_address_model_list().orEmpty()
        }
    },

    CAPVISION_KM_GROUP(
        description = "capvision km group",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_KM_EMAIL_GROUP)
                ?.to_email_address_model_list().orEmpty()
        }
    },

    CAPVISION_SURVEY_REPORT_GROUP(
        description = "capvision km group",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_SURVEY_REPORT_GROUP)
                ?.to_email_address_model_list().orEmpty()
        }
    },

    CAPVISION_SALES_USER_REPORT_GROUP(
        description = "capvision sales user report group",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_SALES_USER_REPORT_GROUP)
                ?.to_email_address_model_list().orEmpty()
        }
    },

    CAPVISION_VIKING_REPORT_GROUP(
        description = "viking report group",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_VIKING_REPORT_GROUP)
                ?.to_email_address_model_list().orEmpty()
        }
    },

    PAYMENT_TASK_MANAGER_EMAIL(
        description = "payment task manager email",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            @Suppress("UNCHECKED_CAST")
            return (data.context_params["payment"] as? Map<String, String?>)
                ?.get(this.name)?.to_email_address_model_list("task manager")
                ?: data.user?.to_email_address_model_list("task manager").orEmpty()
        }
    },

    CAPVISION_PAYMENTS_EMAIL(
        description = "Capvision Payment Email",
        category = EmailTemplatePlaceholderCategory.MISCELLANEOUS,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            val service = data.app_context.getBean<AppConfigEntryService>()
            return service.find(AppConfigKey.CAPVISION_PAYMENT_EMAIL)
                ?.to_email_address_model_list().orEmpty()
        }
    },

    // endregion

    OUTSOURCE_PROJECT_MANAGER_EMAIL(
        description = "outsource project manager email",
        category = EmailTemplatePlaceholderCategory.OUTSOURCE,
        hide_by_default = true
    ) {
        override fun resolve_address(data: PlaceholderBasedModel): List<EmailAddressModel> {
            return listOf(
                EmailAddressModel(
                    email = data.context_params[this.name].toString(),
                    type = EmailAddressModel.Type.USER,
                )
            )
        }
    },
    ;
}
