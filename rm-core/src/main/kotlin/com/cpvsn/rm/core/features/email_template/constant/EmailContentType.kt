package com.cpvsn.rm.core.features.email_template.constant

import com.cpvsn.rm.core.annotation.DocConstant

@DocConstant
enum class EmailContentType(
    val description: String = "",
    val timezone_preference: EmailTemplateTzPreference? = null,
    val corresponding_built_in_tags: List<EmailTemplateBuiltInTag> = emptyList()
) {
    //region send portal
    PORTAL_ADVISOR_PRE_CALL(
        description = "专家PreCall.可关联发portal: TC, SQ, PreCall CA, AG 等邮件",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.TC,
            EmailTemplateBuiltInTag.TC_PROJECT,
            EmailTemplateBuiltInTag.TC_GENERIC,
            EmailTemplateBuiltInTag.CA,
            EmailTemplateBuiltInTag.CA_LINK,
            EmailTemplateBuiltInTag.CA_EMBEDDING,
            EmailTemplateBuiltInTag.SQ,
            EmailTemplateBuiltInTag.AG,
            EmailTemplateBuiltInTag.NO_SQ,
            EmailTemplateBuiltInTag.POST_SQ,
            EmailTemplateBuiltInTag.RESCREEN,
        )
    ),
    PORTAL_ADVISOR_POST_CALL(
        description = "专家PostCall.可关联发portal: PostCall CA, Payment Confirm等邮件",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.PAYMENT_CONFIRM,
            EmailTemplateBuiltInTag.CA,
            EmailTemplateBuiltInTag.CA_LINK,
            EmailTemplateBuiltInTag.CA_EMBEDDING,
            EmailTemplateBuiltInTag.ADVISOR_FEEDBACK
        )
    ),

    PORTAL_CONTACT_PRE_CALL(
        description = "客户联系人PreCall.可关联发portal: Picker(review sq), AG 等邮件",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.ADVISOR_PICKER,
            EmailTemplateBuiltInTag.AG
        )
    ),
    PORTAL_CONTACT_POST_CALL(
        description = "客户联系人PostCall.可关联发portal: feeback 等邮件",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.FEEDBACK
        )
    ),

    PORTAL_COMPLIANCE("PORTAL_COMPLIANCE"),
    PORTAL_RESET_PWD("PORTAL_RESET_PWD"),
    PORTAL_ADVISOR_BANK_ACCOUNT("PORTAL_ADVISOR_BANK_ACCOUNT"),

    PORTAL_CLIENT_USER_REGISTER("PORTAL_CLIENT_USER_REGISTER"),

    PORTAL_COMPLIANCE_LOGIN_INSTRUCTIONS("login instructions, include account, password"),

    CONFERENCE_ASSET_NEW_RECORDING_AVAILABLE("provide client portal task conference asset"),
    CONFERENCE_ASSET_TEMPORARY_PIN("conference asset temporary pin"),

    ADVISOR_RECORD_CONSENT("advisor recording consent"),

    W9_FORM_COLLECT("W9 form collect"),
    //endregion

    //region user login
    SET_INITIAL_LOGIN_PASSWORD("set the initial password"),
    //endregion

    //region notify team
    EVENT_NOTIFICATION("通知邮件"),
    NOTIFY_KM_TC_ACCEPTED("专家已签署TC"),

    /**
     * @deprecated combined notification is not used anymore, use separated ones
     */
    NOTIFY_KM_TC_SQ_RESPONDED("专家签署了TC并回答了Screening Question"),
    NOTIFY_KM_SQ_RESPONDED("专家回答了Screening Question"),
    NOTIFY_KM_NPI_DUPLICATE("专家提交的NPI已经被其他专家使用"),

    /**
     * @deprecated combined notification is not used anymore, use separated ones
     */
    NOTIFY_KM_CA_AG_RESPONDED("专家填写了CA和Availability Grid"),
    NOTIFY_KM_CA_RESPONDED("专家填写了CA"),
    NOTIFY_KM_AG_RESPONDED("专家填写了Availability Grid"),

    NOTIFY_KM_POST_CA_RESPONDED("专家填写了 POST CA"),

    NOTIFY_KM_ADVISOR_BANK_ACCOUNT_RESPONDED("专家填写了 advisor bank account form"),
    NOTIFY_KM_ADVISOR_OUTREACH_UNSUBSCRIBED("专家取消订阅了 outreach"),

    NOTIFY_CONTACT_APPROVED(
        "客户联系人approve了专家",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.TO_INTERNAL_USER,
            EmailTemplateBuiltInTag.EXTERNAL_USER_INVOLVED
        )
    ),
    NOTIFY_CONTACT_REJECTED(
        "客户联系人reject了专家",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.TO_INTERNAL_USER,
            EmailTemplateBuiltInTag.EXTERNAL_USER_INVOLVED
        )
    ),
    NOTIFY_CONTACT_AG_MATCHED(
        "客户联系人确定了与专家匹配的task schedule",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.TO_INTERNAL_USER,
            EmailTemplateBuiltInTag.EXTERNAL_USER_INVOLVED
        )
    ),
    NOTIFY_CONTACT_AG_NOT_MATCHED(
        "客户联系人提供的task schedule与专家AG不匹配",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.TO_INTERNAL_USER,
            EmailTemplateBuiltInTag.EXTERNAL_USER_INVOLVED
        )
    ),

    NOTIFY_CONTACT_SELECTED(
        "",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.TO_INTERNAL_USER,
        )
    ),

    NOTIFY_KM_CAP_LEGAL_APPROVED("capvision legal approved"),
    NOTIFY_KM_CAP_LEGAL_DIRECT_APPROVED("capvision legal direct approved"),
    NOTIFY_KM_CAP_LEGAL_REJECTED("capvision rejected"),

    NOTIFY_CAP_LEGAL_CLIENT_APPROVED("capvision legal help client legal to approve a task"),
    NOTIFY_CAP_LEGAL_CLIENT_REJECTED("capvision legal help client legal to reject a task"),

    NOTIFY_CLIENT_CLIENT_LEGAL_APPROVED("client legal approved, notify client"),
    NOTIFY_CLIENT_CLIENT_LEGAL_REJECTED("client legal rejected, notify client"),

    NOTIFY_CAP_LEGAL_CONSULTATION_NEED_APPROVE("consultation need approve"),

    NOTIFY_KM_CONTACT_FEEDBACK_RESPONDED("notify km, contact feedback responded"),

    NOTIFY_KM_TC_UPDATED("notify KM(and legal team?), tc updated"),

    NOTIFY_DUPLICATE_ADVISOR_BANK_ACCOUNT("notify duplicate bank account of different advisors"),
    //endregion

    //region notify team/Contract Entry Process
    NOTIFY_CONTRACT_REQUEST("NOTIFY_CONTRACT_REQUEST"),
    NOTIFY_CONTRACT_REQUEST_COMPLETED("NOTIFY_CONTRACT_REQUEST_COMPLETED"),
    NOTIFY_CONTRACT_REQUEST_REJECTED("NOTIFY_CONTRACT_REQUEST_REJECTED"),
    NOTIFY_CONTRACT_APPLY("NOTIFY_CONTRACT_APPLY"),
    NOTIFY_CONTRACT_APPLY_APPROVED("NOTIFY_CONTRACT_APPLY_APPROVED"),
    NOTIFY_CONTRACT_APPLY_REJECTED("NOTIFY_CONTRACT_APPLY_REJECTED"),

    NOTIFY_CONTRACT_EXPIRATION("NOTIFY_CONTRACT_EXPIRATION"),
    NOTIFY_CONTRACT_USAGE("NOTIFY_CONTRACT_USAGE"),
    //endregion

    //region notify team/Client Approval
    NOTIFY_CAP_LEGAL_CLIENT_APPROVAL("NOTIFY_CAP_LEGAL_CLIENT_APPROVAL"),
    NOTIFY_AM_CLIENT_APPROVAL_APPROVED("NOTIFY_AM_CLIENT_APPROVAL_APPROVED"),
    NOTIFY_AM_CLIENT_APPROVAL_REJECTED("NOTIFY_AM_CLIENT_APPROVAL_REJECTED"),
    //endregion

    //region notify team/Invoice
    NOTIFY_PAYMENT_TERM_INVOICE_DATE("NOTIFY_PAYMENT_TERM_INVOICE_DATE"),
    //endregion

    NOTIFY_CLIENT_AM_TEAM_CHANGED("notify client am team changed"),

    //region misc
    /**
     * "
     * Project Closing Message to Experts: Can we create an email template that is automatically sent to all
     * experts who have interacted on the project notifying them that the project has closed? As in,
     * if an expert has completed Screening Questions, accepted a CA, or we have entered Availability for an expert.
     * When a Research team member moves the project to “Closed” this email would automatically email advisors
     * notifying them  that the project has closed
     * "
     * -- 2021.12.31 email
     */
    NOTIFY_ADVISOR_PROJECT_CLOSED("notify advisors project closed"),
    NOTIFY_AR_PROJECT_MEMBER_PROJECT_CLOSED("notify AR and Project Member project closed"),

    NOTIFY_ADVISOR_PAYMENT_CONFIRMED("Sent to advisor after they fill out the form to confirm payment details."),
    NOTIFY_ADVISOR_PAYMENT_CONFIRMED_SURVEY("Sent to advisor after they fill out the form to confirm payment details."),
    NOTIFY_ADVISOR_PAYMENT_CONFIRMED_TROLLEY("Sent to advisor after they fill out the form to confirm payment details."),
    NOTIFY_ADVISOR_PAYMENT_CONFIRMED_SURVEY_TROLLEY("Sent to advisor after they fill out the form to confirm payment details."),

    CLIENT_LEGAL_CONSULTATION_NEED_APPROVE("consultation need client legal approve (when compliance_preference.approve_method is 'EMAIL')"),

    TASK_CLIENT_LEGAL_REQUEST_MORE_INFO("client legal request more task info (via compliance portal)"),
    PROJECT_CLIENT_LEGAL_REQUEST_MORE_INFO("client legal request more project info (via compliance portal)"),

    PROJECT_OUTREACH(
        "project outreach related template", corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.SURVEY_OUTREACH,
            EmailTemplateBuiltInTag.NEW_TEMPLATE_PLACEHOLDER,
        )
    ),

    OUTSOURCE_PROJECT_AUTO_CREATION_NOTIFICATION("outsource project auto creation notification"),

    OUTSOURCE_PROJECT_AUTO_CREATION_FAILED("outsource project auto creation failed"),
    // consider adding PROJECT_SURVEY_OUTREACH type?

    CLIENT_OUTREACH("client outreach related template"),

    LINKEDIN_MESSAGE(
        "linked-in message for advisor", corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.SURVEY_OUTREACH,
            EmailTemplateBuiltInTag.NEW_TEMPLATE_PLACEHOLDER,
        )
    ),

    LINKEDIN_MESSAGE_CLIENT("linked-in message for client contact"),

    ADVISOR_RECOMMENDATION(
        description = "advisor recommendation", corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.ADVISOR_PICKER,
            EmailTemplateBuiltInTag.AG
        )
    ),

    ADVISOR_REFERRAL_PAYMENT_CONFIRM(description = "advisor referral payment confirm"),

    CUSTOM("CUSTOM"),

    // fragment
    ADVISOR_UNSUBSCRIBE_OUTREACH_FRAGMENT("advisor unsubscribe outreach fragment"),
    ADVISOR_DECLINE_FRAGMENT("advisor decline fragment"),
    CONTACT_ADVISOR_RECOMMEND_CLIENT_PORTAL_FRAGMENT("contact advisor recommend client portal fragment"),
    ANGLE_DISCUSSION_TOPIC_FRAGMENT("angle discussion topic fragment"),
    //endregion

    //region misc/arrange
    TASK_CLIENT_SCHEDULED_TO_ADVISOR(
        "task arrange to advisor without calendar",
        timezone_preference = EmailTemplateTzPreference.ADVISOR,
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.TC,
            EmailTemplateBuiltInTag.LEGAL_TO_BE_APPROVED,
            EmailTemplateBuiltInTag.LEGAL_APPROVED,
            EmailTemplateBuiltInTag.LOOPUP,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.NOT_LOOPUP
        ),
    ),
    TASK_CLIENT_SCHEDULED_TO_CONTACT(
        "task arrange to contact without calendar",
        timezone_preference = EmailTemplateTzPreference.PROJECT,
    ),

    CALENDAR_TASK_ARRANGE_ADVISOR(
        "task arrange to advisor with calendar",
        timezone_preference = EmailTemplateTzPreference.ADVISOR,
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.TC,
            EmailTemplateBuiltInTag.LEGAL_TO_BE_APPROVED,
            EmailTemplateBuiltInTag.LEGAL_APPROVED,
            EmailTemplateBuiltInTag.LOOPUP,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.ZOOM,
            EmailTemplateBuiltInTag.NOT_LOOPUP
        )
    ),
    CALENDAR_TASK_ARRANGE_CONTACT(
        description = "task arrange to contact with calendar",
        timezone_preference = EmailTemplateTzPreference.PROJECT,
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.LEGAL_TO_BE_APPROVED,
            EmailTemplateBuiltInTag.LEGAL_APPROVED,
            EmailTemplateBuiltInTag.LOOPUP,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.ZOOM,
            EmailTemplateBuiltInTag.NOT_LOOPUP
        )
    ),

    ARRANGE_VETTING_CALL_TO_ADVISOR(
        description = "arrange vetting call to advisor",
        timezone_preference = EmailTemplateTzPreference.ADVISOR,
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.LOOPUP,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.OTHER_BRIDGE
        )
    ),

    ARRANGE_VETTING_CALL_TO_CONTACT(
        description = "arrange vetting call to contact",
        timezone_preference = EmailTemplateTzPreference.PROJECT,
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.LOOPUP,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.OTHER_BRIDGE
        )
    ),

    /*------------------------------------- currently only for twilio ------------------------------------------------*/
    CALENDAR_TASK_ARRANGE_TO_CLIENT_COMPLIANCE(
        description = "task arrange to client compliance with calendar",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.LEGAL_TO_BE_APPROVED,
            EmailTemplateBuiltInTag.LEGAL_APPROVED,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.ZOOM,
        )
    ),
    CALENDAR_TASK_ARRANGE_TO_CLIENT_SPONSOR(
        description = "task arrange to client sponsor with calendar",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.LEGAL_TO_BE_APPROVED,
            EmailTemplateBuiltInTag.LEGAL_APPROVED,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.ZOOM,
        )
    ),
    CALENDAR_TASK_ARRANGE_TO_MODERATOR(
        description = "task arrange to moderator with calendar",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.LEGAL_TO_BE_APPROVED,
            EmailTemplateBuiltInTag.LEGAL_APPROVED,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.ZOOM,
        )
    ),
    CALENDAR_TASK_ARRANGE_TO_TRANSLATOR(
        description = "task arrange to translator with calendar",
        corresponding_built_in_tags = listOf(
            EmailTemplateBuiltInTag.LEGAL_TO_BE_APPROVED,
            EmailTemplateBuiltInTag.LEGAL_APPROVED,
            EmailTemplateBuiltInTag.TWILIO,
            EmailTemplateBuiltInTag.ZOOM,
        )
    ),
    //endregion

    //region outsource
    OUTSOURCE_RECOMMENDED_NOTIFICATION(
        description = "notification to local project manager when outsource api called to import advisor"
    ),
    //endregion
    ;
}
