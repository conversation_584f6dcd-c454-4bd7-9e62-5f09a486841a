package com.cpvsn.rm.core.features.email_template.constant

import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import java.time.ZoneId

enum class EmailTemplateTzPreference(
    val category: EmailTemplatePlaceholderCategory,
    val description: String,
    val resolve_timezone: (PlaceholderBasedModel) -> (ZoneId?),
) {
    ADVISOR(
        category = EmailTemplatePlaceholderCategory.ADVISOR,
        description = "prefer to use advisor's timezone",
        resolve_timezone = { it.advisor?.zoneId }
    ),
    CONTACT(
        category = EmailTemplatePlaceholderCategory.CONTACT,
        description = "prefer to use contact's timezone",
        resolve_timezone = { it.contact?.zoneId }
    ),
    USER(
        category = EmailTemplatePlaceholderCategory.USER,
        description = "prefer to use user's timezone",
        resolve_timezone = { it.user?.zoneId }
    ),
    PROJECT(
        category = EmailTemplatePlaceholderCategory.PROJECT,
        description = "prefere to use project's timezone",
        resolve_timezone = { it.project?.zoneId }
    )
    ;
}
