package com.cpvsn.rm.core.features.finance.cronjob

import com.cpvsn.rm.core.base.AbstractJob
import com.cpvsn.rm.core.config.schedule.CronJobWeb
import com.cpvsn.rm.core.features.finance.revenue.RevenueService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import javax.annotation.PostConstruct

@CronJobWeb
class ProdInvoiceBillingJob : AbstractJob(), PaygoBillingJob {

    companion object {
        private val logger = LoggerFactory.getLogger(ProdInvoiceBillingJob::class.java)
    }

    @Autowired
    private lateinit var revenueService: RevenueService

    @PostConstruct
    fun postConstruct() {
        logger.debug("init")
    }

    // At 00:00:00am, on the 1st day, every month
    // https://www.freeformatter.com/cron-expression-generator-quartz.html
    @Scheduled(cron = "0 0 0 1 * ?")
    @SchedulerLock(name = "invoice_billing")
    fun invoice_billing() {
        logger.info("enter monthly job")
        if (!should_run) {
            logger.info("job won't run")
            logger.debug("module: ${envProperties.module}")
            logger.debug("env: ${envService.current()}")
            return
        }
        revenueService.invoice_billing_job()
    }

}
