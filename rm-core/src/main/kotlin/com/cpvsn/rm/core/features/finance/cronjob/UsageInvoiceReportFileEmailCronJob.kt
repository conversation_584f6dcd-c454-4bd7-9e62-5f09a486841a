package com.cpvsn.rm.core.features.finance.cronjob

import com.cpvsn.core.svc.email.EmailRequest
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.config.schedule.CronJobWeb
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.client.ClientPreference
import com.cpvsn.rm.core.features.contract.PayWay
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.finance.invoice.Invoice
import com.cpvsn.rm.core.features.finance.invoice.InvoiceUtilsService
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.finance.usage.UsageExcelModel
import com.cpvsn.rm.core.features.finance.usage.UsageExcelService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import java.io.ByteArrayOutputStream
import java.time.LocalDate
import java.time.ZoneId
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream

@CronJobWeb
class UsageInvoiceReportFileEmailCronJob {


    @Autowired
    private lateinit var invoiceUtilsService: InvoiceUtilsService

    @Autowired
    private lateinit var usageExcelService: UsageExcelService

    @Autowired
    private lateinit var emailService: EmailService

    companion object {
        val NEW_YORK_TIME_ZONE = ZoneId.of("America/New_York")
    }

    @Scheduled(cron = "0 30 0 1 * ?")
    @SchedulerLock(name = "usage_invoice_report_file_email")
    fun usage_invoice_report_file_email() {
        if (Region.current != Region.US) {
            return
        }
        val firstDayOfMonth = LocalDate.now().withDayOfMonth(1)
        val firstDayMidnight = firstDayOfMonth.atStartOfDay()
        val month_start_instant = firstDayMidnight.atZone(NEW_YORK_TIME_ZONE).toInstant()
        val last_month_start_instant = firstDayOfMonth.minusMonths(1L).withDayOfMonth(1).atStartOfDay()
            .atZone(NEW_YORK_TIME_ZONE).toInstant()

        val last_month_revenues = Revenue.findAll(
            query = Revenue.Query(
                create_at_gte = last_month_start_instant,
                create_at_lte = month_start_instant,
            ),
            include = Includes.setOf(Revenue::contract)
        )
        val filtered_last_month_revenues = last_month_revenues.filter { it.contract_id != null }

        val invoice_list = Invoice.findAll(
            query = Invoice.Query(
                ids = filtered_last_month_revenues.ids(Revenue::invoice_id)
            ),
            include = Includes.setOf(
                Invoice::revenues,
                Invoice::contract,
            ),
        )
        val no_prepay_invoices = invoice_list.filter { it.contract?.payway != PayWay.PREPAY }
        val no_prepay_usage_zip_stream = create_zip_stream(
            no_prepay_invoices.associate {
                val file_info = invoiceUtilsService.generate_usage_excel(
                    id = it.id,
                )
                val file_name =
                    file_info.file_name.substringBefore(".xlsx") + " - ${it.contract?.payway}(${it.contract?.id})-INV${it.id}.xlsx"
                val file_content = file_info.content
                file_name to file_content
            }
        )
        val invoice_zip_stream = create_zip_stream(
            invoice_list.associate {
                val file_info = invoiceUtilsService.generate_word_doc(
                    id = it.id,
                    persist_result = false,
                    group_revenues = true,
                )
                val file_name = file_info.file_name
                val file_content = file_info.content
                file_name to file_content
            }
        )
        val prepay_revenues = filtered_last_month_revenues.filter { it.contract?.payway == PayWay.PREPAY }
        val grouped_prepay_revenues = prepay_revenues.groupBy {
            it.contract_id
        }
        val prepay_usage_zip_stream = create_zip_stream(grouped_prepay_revenues.map { entry ->
            val contract_id = entry.key
            val revenues = entry.value
            val client_id = revenues.first().client_id
            val client_preference = ClientPreference.firstOrNull(
                query = ClientPreference.Query(
                    client_id = client_id,
                )
            )
            val columns = client_preference?.usage_excel_fields
            val file_info = usageExcelService.generate_usage_excel(
                query = Revenue.Query(
                    ids = revenues.ids()
                ),
                columns = columns ?: UsageExcelModel.AvailableFields.standards,
                group_revenues = true,
            )
            val file_name =
                file_info.file_name.substringBefore(".xlsx") + " - ${PayWay.PREPAY}(${contract_id}).xlsx"
            file_name to file_info.content
        }.toMap())
        val last_month_revenues_without_contract_id = last_month_revenues.filter {
            it.contract_id == null
        }
        val no_contract_revenue_client_ids = last_month_revenues_without_contract_id.ids(Revenue::client_id)
        emailService.send(
            EmailRequestPojo(
                subject = "US DB Last Month Invoice and Usage Report",
                content = "Hello,<br/>Here are the US DB last month invoices and usage reports. <br/>And ${
                    no_contract_revenue_client_ids.takeUnless { it.isEmpty() }?.joinToString(
                        ","
                    )?.let {
                        "clients with id: $it have no"
                    } ?: "all these clients have"
                } effective contracts when generating revenues.<br/>",
                to_list = listOf("<EMAIL>", "<EMAIL>", "<EMAIL>"),
                from = "<EMAIL>",
                attachments = listOf(
                    EmailRequest.Attachment(
                        name = "invoice.zip",
                        bytes = invoice_zip_stream.toByteArray(),
                    ),
                    EmailRequest.Attachment(
                        name = "prepay_usage.zip",
                        bytes = prepay_usage_zip_stream.toByteArray(),
                    ),
                    EmailRequest.Attachment(
                        name = "no_prepay_usage.zip",
                        bytes = no_prepay_usage_zip_stream.toByteArray(),
                    )
                )
            )
        )
    }


    private fun create_zip_stream(files: Map<String, ByteArray>): ByteArrayOutputStream {
        val outputStream = ByteArrayOutputStream()
        ZipOutputStream(outputStream).use { zipOut ->
            for ((fileName, fileContent) in files) {
                val entry = ZipEntry(fileName)
                zipOut.putNextEntry(entry)
                zipOut.write(fileContent)
                zipOut.closeEntry()
            }
        }
        return outputStream
    }
}