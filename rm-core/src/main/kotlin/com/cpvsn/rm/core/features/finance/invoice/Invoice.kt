package com.cpvsn.rm.core.features.finance.invoice

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.PatchFields
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.model.HasClient
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.contract.entity.ContractPaymentItem
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.fasterxml.jackson.annotation.JsonIgnore
import java.math.BigDecimal
import java.time.Instant

class Invoice : RmEntity(), UserAuditing, SoftDeletable, HasClient {
    companion object : RmCompanion<Invoice>()

    @Column
    override var client_id: Int = 0

    @Column
    var contract_id: Int = 0

    // bi-directional reference
    @Column
    var contract_payment_item_id: Int? = null

    @Column
    var file_path: String = ""

    @Column
    var currency: String = "USD"

    @get: JsonIgnore
    @PatchFields(["currency"])
    var currency_enum: BuiltInCurrency by PropDelegates.enum(this::currency)

    @Column
    var amount: BigDecimal = BigDecimal.ZERO

    @Column
    var invoice_time: Instant = Instant.EPOCH

    // this field is currently only to generate usage report's name with invoice id to separate the different usage
    // reports in the invoices generated by project divided
    @Column
    var generated_project_divided: Boolean = false

    @Column
    override var create_by_id: Int = 0
    override var update_by_id: Int = 0

    @Column(updatable = false, insertable = false)
    override var delete_at: Instant? = null

    @Relation
    var revenues: List<Revenue>? = null

    @Relation
    var client: Client? = null

    @Relation
    var contract: Contract? = null

    @Relation
    var contract_payment_item: ContractPaymentItem? = null

    class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Eq
        val contract_id: Int? = null,
        @Criteria.IdsIn
        val contract_ids: Set<Int>? = null,

        @Criteria.Eq
        val client_id: Int? = null,
        @Criteria.IdsIn
        val client_ids: Set<Int>? = null,

        @Criteria.Gt
        val invoice_time_gt: Instant? = null,
        @Criteria.Lt
        val invoice_time_lt: Instant? = null,
        @Criteria.Gte
        val invoice_time_gte: Instant? = null,
        @Criteria.Lte
        val invoice_time_lte: Instant? = null,
    ) : BaseQuery<Invoice>()
}
