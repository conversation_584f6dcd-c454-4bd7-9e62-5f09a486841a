package com.cpvsn.rm.core.features.finance.payment

import com.fasterxml.jackson.annotation.JsonIgnore

interface Payable {

    var payment_topic_id: String?

    @get: JsonIgnore
    val has_payment_topic: Boolean
        get() = payment_topic_id != null

    /**
     * create a brand new payment topic using this payable
     * without persisting it
     */
    @JsonIgnore
    fun to_payment_topic(): PaymentTopic

    /**
     * if has_payment_topic
     *   return current payment topic
     * else
     *   create a brand new payment topic using this payable, then persist and return it
     */
    @JsonIgnore
    fun get_or_create_payment_topic(): PaymentTopic

}
