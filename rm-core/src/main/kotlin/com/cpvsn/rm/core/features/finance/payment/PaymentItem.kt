package com.cpvsn.rm.core.features.finance.payment

import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.core.util.experimental.BeanUtil
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.orm.annotation.*
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.crud.query.CriteriaQuery
import com.cpvsn.rm.core.base.agg.AggregationMapper
import com.cpvsn.rm.core.base.agg.AggregationQuery
import com.cpvsn.rm.core.base.agg.AggregationService
import com.cpvsn.rm.core.base.entity.ColumnDefinitions
import com.cpvsn.rm.core.base.entity.RmCompanion2
import com.cpvsn.rm.core.base.entity.RmEntity2
import com.cpvsn.rm.core.base.service.RmBaseTsIdRepository
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.bridge.Bridgeable
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.finance.payment.base.IPaymentItem
import com.cpvsn.rm.core.features.finance.payment.bridge.BrgPaymentItem
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.fasterxml.jackson.annotation.JsonIgnore
import org.apache.ibatis.annotations.Mapper
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Component
import org.springframework.stereotype.Repository
import java.math.BigDecimal
import java.time.Instant

@TableDefinition(
    indices = [
        TableDefinition.IndexDefinition(
            columns = ["payment_id"]
        ),
        TableDefinition.IndexDefinition(
            columns = ["form_id"]
        ),
        TableDefinition.IndexDefinition(
            columns = ["latest_pushed_at"]
        ),
        TableDefinition.IndexDefinition(
            columns = ["update_at"]
        ),
    ]
)
class PaymentItem : RmEntity2(),
    Bridgeable<PaymentItem, BrgPaymentItem>, SoftDeletable, IPaymentItem {

    companion object : RmCompanion2<PaymentItem>()

    @Column
    var form_id: Int? = null

    @Column
    @ColumnDefinition(type = ColumnDefinitions.TSID)
    override var payment_id: String = ""

    @Column
    @ColumnDefinition(type = ColumnDefinitions.TSID)
    override var topic_id: String = ""

    @Column
    @ColumnDefinition(type = ColumnDefinitions.TSID)
    override var advisor_tsid: String = ""

    @Column
    override var minutes: Int = 0

    @Column
    override var amount: BigDecimal = BigDecimal.ZERO

    @Column
    @ColumnDefinition(type = BuiltInCurrency.COLUMN_DEFINITION)
    override var currency_name: String = ""

    @PatchFields(["currency_name"])
    @get: JsonIgnore
    var currency_enum: BuiltInCurrency? by PropDelegates.enum_nullable(this::currency_name)

    @Column
    @get: JsonIgnore
    @ColumnDefinition(type = "varchar(40)")
    override var status_name: String = Status.INITIAL.name

    @PatchFields(["status_name"])
    var status: Status? by PropDelegates.enum_nullable(this::status_name)

    @Column
    override var is_adjustment: Boolean = false

    @Column
    override var paid_at: Instant? = null

    @Column
    override var latest_pushed_at: Instant? = null

    @Column
    override var latest_pulled_at: Instant? = null

    @Column
    override var legacy_id: Int? = null

    @Column(updatable = false, insertable = false)
    @ColumnDefinition(defaultValue = ColumnDefinitions.DEFAULT_DELETE_AT)
    override var delete_at: Instant? = null

    @Relation
    var payment: Payment? = null

    @Relation
    var topic: PaymentTopic? = null

    @Relation
    var form: PaymentForm? = null

    @Relation
    var events: List<PaymentItemEvent>? = null

    @Relation(reference = "advisor_tsid", backReference = "tsid")
    var advisor: Advisor? = null

    override fun toBridge(latest_pushed_at: Instant): BrgPaymentItem {
        val res = BeanUtil.convert(this, BrgPaymentItem::class)
        res.latest_pulled_at = null
        res.latest_pushed_at = latest_pushed_at
        res.legacy_id = null
        return res
    }

    enum class Status(
        val description: String
    ) {
        INITIAL("initial"),
        RESET("reset"),
        REJECTED("rejected by finance team"),
        ADVISOR_CONFIRMING("advisor confirming"),

        ADVISOR_CONFIRMED("advisor confirmed"),
        PAYING("paying"),
        PAID("paid successfully"),
        CANCELLED("the incorrect older payment is marked as Cancelled, and the newer payment contains the updated value"),

        PAY_FAILED("failed to pay"),
        ;

        val allow_to_confirm: Boolean
            get() = this in setOf(
                INITIAL,
                RESET,
                REJECTED,
                ADVISOR_CONFIRMING,
                PAY_FAILED,
            )
    }

    data class Query(
        @Criteria.Eq
        val id: String? = null,
        @Criteria.IdsIn
        val ids: Set<String>? = null,
        @Criteria.Gte
        val id_gte: String? = null,

        val status_in: Set<Status>? = null,
        @Criteria.In
        val status_name_in: Set<String>? = status_in?.map { it.name }?.toSet(),
        @Criteria.Eq
        val status_name: String? = null,
        val status_not_in: Set<Status>? = null,
        @Criteria.NotIn
        val status_name_not_in: Set<String>? = status_not_in?.map { it.name }?.toSet(),

        @Criteria.Gte
        val paid_at_gte: Instant? = null,
        @Criteria.Lte
        val paid_at_lte: Instant? = null,

        @Criteria.IdsIn
        val legacy_ids: Set<Int>? = null,

        @Criteria.IsNull
        val latest_pushed_at_is_null: Boolean? = null,
        @Criteria.Gte
        val create_at_gte: Instant? = null,

        @Criteria.Join
        val payment: Payment.Query? = null,
        @Criteria.Join
        val topic: PaymentTopic.Query? = null,
        @Criteria.Join
        val events: PaymentItemEvent.Query? = null,
        @Criteria.Join
        val form: PaymentForm.Query? = null,
        @Criteria.Or
        val or: List<Query>? = null,
        override var aggregation: Set<String>? = null,
    ) : CriteriaQuery<PaymentItem>(), AggregationQuery

    @Mapper
    @Component
    interface MybatisMapper : AggregationMapper

    @Repository
    class Repo : RmBaseTsIdRepository<PaymentItem>(),
        JdbcEntityBatchRepo<String, PaymentItem>,
        AggregationService {

        override val batchDao: JdbcEntityBatchDao<String, PaymentItem> by lazy {
            JdbcEntityBatchDao(PaymentItem::class, dataSource)
        }

        @Autowired
        override lateinit var aggregation_mapper: MybatisMapper

        @Autowired
        private lateinit var paymentRepo: Payment.Repo

        @Autowired
        private lateinit var brgPaymentItemRepo: BrgPaymentItem.Repo

        fun push_all(entities: List<PaymentItem>): List<PaymentItem> {
            require(entities.all { it.has_id })

            PaymentItem.joinOnce(
                entities,
                Includes.setOf(
                    PaymentItem::payment,
                    PaymentItem::topic,
                ),
            )

            val (to_save, to_patch) = entities
                .partition { !it.pushed }
            // we can only "create" a bridge payment item if
            // its topic.region is current region
            val filtered_to_save = to_save
                .filter {
                    it.topic?.region == Region.current
                }
            push_dependencies(filtered_to_save)

            // we can only "update" a bridge payment item if
            // its payment.region is current region
            // otherwise, we can only create "adjustment" bridge payment item
            val filtered_to_patch = to_patch
                .filter {
                    it.payment?.region == Region.current
                }

            val now = Instant.now()
            brgPaymentItemRepo.batchSave(
                filtered_to_save.map { it.toBridge(latest_pushed_at = now) }
            )
            brgPaymentItemRepo.batchPatch(
                filtered_to_patch.map {
                    it.toBridge(
                        latest_pushed_at = now
                    )
                },
                fields = Includes.setOf(
                    BrgPaymentItem::status,
                    BrgPaymentItem::status_name,
                    BrgPaymentItem::paid_at,
                    BrgPaymentItem::latest_pushed_at,
                )
            )

            entities.forEach {
                it.latest_pushed_at = now
            }
            batchPatch(
                entities = entities,
                fields = Includes.setOf(PaymentItem::latest_pushed_at),
            )
            return entities
        }

        private fun push_dependencies(
            entities: List<PaymentItem>
        ) {
            PaymentItem.joinOnce(
                entities,
                Includes.setOf(PaymentItem::payment),
            )
            // note that topics is considered as payment's dependence
            // not payment_items' dependence
            val payments = entities
                .mapNotNull { it.payment }
                .filter { !it.pushed }
                .distinctBy { it.id }
            paymentRepo.push_all(payments)
        }
    }

}
