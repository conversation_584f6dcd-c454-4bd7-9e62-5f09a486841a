package com.cpvsn.rm.core.features.finance.payment.base

import com.fasterxml.jackson.annotation.JsonIgnore
import java.math.BigDecimal
import java.time.Instant

interface IPaymentItem {
    var advisor_tsid: String
    var topic_id: String
    var payment_id: String
    var minutes: Int

    @get: JsonIgnore
    var hours: BigDecimal
        set(value) {
            minutes = (value * 60.toBigDecimal()).toInt()
        }
        get() {
            return (minutes.toDouble() / 60.0).toBigDecimal()
        }
    val amount: BigDecimal
    var is_adjustment: Boolean

    var currency_name: String
    var status_name: String
    var paid_at: Instant?

    // legacy payment id
    var legacy_id: Int?
}
