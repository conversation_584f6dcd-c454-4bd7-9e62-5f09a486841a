package com.cpvsn.rm.core.features.finance.usage

import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import java.time.Instant
import java.time.ZoneId

class CommonUsageExcelModel(
    client: Client,
    contract: Contract,
    report_date: Instant,
    billing_contact: ClientContact?,
    zone_id: ZoneId,
) : UsageExcelModel(client, contract, report_date, billing_contact, zone_id) {
    companion object {
        private val common_fields =
            AvailableFields.values().filter { it.specifed_model == null }
        val computedFields = AvailableFields.compute_all(
            standard = AvailableFields.standards,
            all = common_fields
        )
    }

    override fun handle_items(revenues: List<Revenue>): UsageExcelModel {
        this.items = revenues.map {
            CommonUsageItem.handle_common(it, zone_id)
        }
        return this
    }
}
