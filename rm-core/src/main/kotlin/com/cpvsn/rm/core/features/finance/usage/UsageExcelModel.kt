package com.cpvsn.rm.core.features.finance.usage

import com.cpvsn.core.base.model.BaseModelCompanion
import com.cpvsn.core.base.model.RequireExtra
import com.cpvsn.core.svc.spring.CoreAppContextHolder
import com.cpvsn.crud.model.Includes
import com.cpvsn.poiext.excel.extToByteArray
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.finance.usage.specified.bcg.BCGUsageExcelModel
import com.cpvsn.rm.core.features.finance.usage.specified.d1.D1UsageExcelModel
import com.cpvsn.rm.core.features.finance.usage.specified.ga.GAUsageExcelModel
import com.cpvsn.rm.core.features.project.ProjectWorkflow
import com.cpvsn.rm.core.features.template.FileTemplate
import com.cpvsn.rm.core.features.template.FileTemplateService
import com.cpvsn.rm.core.features.template.FileWrapper
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.ByteArrayInputStream
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

abstract class UsageExcelModel(
    val client: Client,
    val contract: Contract,
    val report_date: Instant, // we need client timezone to format date
    // regarding to revenue
    @RequireExtra(
        [
            "project.client_contacts",
            "project.investment_target_companies",
            "project.client_office",
            "task.lead",
            "task.advisor.location.country",
            "task.advisor_profile.company",
            "task.advisor.current_jobs.company",
            "task.arrangement",
            "task.advisor_payments",
            "task.payment_topic.payments.items",
            "task.db_page_url_jit",
            "client_contact.location",
            "client.preference",
        ]
    )
    val billing_contact: ClientContact?,
    // will be used in template
    val zone_id: ZoneId,
) {

    companion object : BaseModelCompanion<UsageExcelModel>() {
        private val report_date_formatter =
            DateTimeFormatter.ofPattern("MMM dd, yyyy")

        fun of(
            client: Client,
            report_date: Instant,
            revenues: List<Revenue>, // we need client timezone to format date
            contract: Contract,
            billing_contact: ClientContact?,
            zone_id: ZoneId = billing_contact?.zoneId ?: ZoneId.systemDefault(),
        ): UsageExcelModel {
            if (client.preference?.preferred_workflow == null) {
                Client.join(
                    client, Includes.setOf(
                        Client::preference
                    )
                )
            }
            val sorted_revenues = revenues.sortedBy { it.revenue_time }
            return when (client.preference?.preferred_workflow) {
                ProjectWorkflow.GA -> {
                    GAUsageExcelModel(
                        client = client,
                        contract = contract,
                        report_date = report_date,
                        billing_contact = billing_contact,
                        zone_id = zone_id,
                    ).handle_items(sorted_revenues)
                }

                ProjectWorkflow.BCG -> {
                    BCGUsageExcelModel(
                        client = client,
                        contract = contract,
                        report_date = report_date,
                        billing_contact = billing_contact,
                        zone_id = zone_id,
                    ).handle_items(sorted_revenues)
                }

                ProjectWorkflow.D1 -> {
                    D1UsageExcelModel(
                        client = client,
                        contract = contract,
                        report_date = report_date,
                        billing_contact = billing_contact,
                        zone_id = zone_id,
                    ).handle_items(sorted_revenues)
                }

                else -> {
                    CommonUsageExcelModel(
                        client = client,
                        contract = contract,
                        report_date = report_date,
                        billing_contact = billing_contact,
                        zone_id = zone_id,
                    ).handle_items(sorted_revenues)
                }

            }
        }
    }

    open fun process_to_file_wrapper(columns: List<AvailableFields>): FileWrapper {
        val wrapper = CoreAppContextHolder.getBean<FileTemplateService>().process_built_in(
            content_type = FileTemplate.ContentType.USAGE_REPORT,
            data = this,
        )
        val workbook = XSSFWorkbook(ByteArrayInputStream(wrapper.content))
        CoreAppContextHolder.getBean<UsageExcelService>().update_generated_excel(
            workbook,
            data_row_count = this.items.size,
            columns = columns,
        )
        return FileWrapper(
            file_name = wrapper.file_name,
            media_type = wrapper.media_type,
            content = workbook.extToByteArray()
        )
    }

    var items: List<UsageItem> = emptyList()

    // will be used in template
    val date_formatter = DateTimeFormatter
        .ofPattern(
            "yyyy/M/dd",
            Locale.US,
        ).withZone(zone_id)

    /**
     * will be used in template
     *
     * 另外Usage report可选字段中在添加一个Month的字段，
     * 显示格式为日期的月份：格式为：Apr-2023，May-2023 这样
     *
     * -- Ping's requirement
     */
    val month_year_formatter = DateTimeFormatter
        .ofPattern(
            "MMM-yyyy",
            Locale.US, // necessary when system locale is not US/English
        ).withZone(zone_id)

    // will be used in template
    val unit_price_description: String
        get() {
            with(contract) {
                if (client?.preference?.preferred_workflow == ProjectWorkflow.BCG) {
                    return "${payway}@${
                        unit_price_us?.toPlainString().orEmpty()
                    }$currency/hr"
                } else {
                    return "${payway}@${
                        unit_price_china?.toPlainString().orEmpty()
                    }$currency/hr"
                }
            }
        }

    // will be used in template
    val report_date_str: String
        get() {
            return report_date_formatter.withZone(zone_id).format(report_date)
        }

    abstract fun handle_items(revenues: List<Revenue>): UsageExcelModel

    /**
     * WARN: the define order(ordinal) matter!
     * change the definition order will affect the default order of the enum in API
     */
    enum class AvailableFields(
        val displayName: String,
        /**
         * column index in standard template
         * DO NOT CHANGE IT WITHOUT CHANGE THE STANDARD TEMPLATE AT THE SAME TIME
         */
        val template_column_index: Int,
        var is_standard: Boolean = false,
        var specifed_model: ProjectWorkflow? = null,
    ) {
        REVENUE_TIME("Date", 0),
        REVENUE_MONTH("Month", 21),

        CLIENT_CONTACT_NAME("User", 1),
        CLIENT_CONTACT_EMAIL("User Mail", 2),
        CLIENT_CONTACT_LOCATION("User Location", 3),

        PROJECT("Project", 4),
        PROJECT_CODE("Project Code", 5),

        TASK_ID("Task id", 6),
        TASK_LEAD("KM", 7),
        TASK_TIME_RANGE("Time", 8),
        TASK_SUB_TYPE("Subtype", 9),

        ADVISOR_ID("Expert Number", 10),
        ADVISOR_NAME("Expert Name", 11),
        ADVISOR_POSITION("Expert's Position", 12),
        ADVISOR_COMPANY("Expert's Company", 13),
        ADVISOR_COUNTRY("Expert Country", 14),

        TASK_INTERVIEW_TYPE("Type", 15),
        RATE("Rate", 16),
        REVENUE_CLIENT_HOUR("Call Duration", 17),
        REVENUE_BILLABLE_HOUR("Hours", 18),

        REVENUE_CASH("Charge", 19),
        TASK_BILLING_NOTES("Remarks", 20),

        //https://www.notion.so/capvision/GA-Usage-report-e111878717d54f1786af9d3713b30068
        //these fields are only for GA currently
        //The current fixed value for the ACCOUNT_TYPE field is "General Atlantic"
        ACCOUNT_TYPE("Account Type", 22, specifed_model = ProjectWorkflow.GA),


        EXPERT_CURRENT_COMPANY("Expert Current Company", 23),
        EXPERT_CURRENT_POSITION("Expert Current Position", 24),
        EXPERT_CURRENT_COMPANY_PUBLIC_STATUS("Expert Current Company Public Status", 25),
        REFUND("Refund", 26),
        INVOICE_NUMBER("Invoice Number", 27),

        //https://www.notion.so/capvision/BCG-Usage-Report-c5bffb2e3fb94c0bbe3731431ac1d3e7
        //only some fields are only for BCG currently
        EXPERT_ID("Expert Id", 28, specifed_model = ProjectWorkflow.BCG),
        START_TIME("Start Time", 29),
        END_TIME("End Time", 30),
        BCG_BILLED_HOURS("BCG Billed Hours", 31, specifed_model = ProjectWorkflow.BCG),
        CLIENT_RATE("Client Rate", 32),
        AGENCY_NAME("Agency's Name", 33, specifed_model = ProjectWorkflow.BCG),
        EXPERT_LEVEL("Expert Level", 34, specifed_model = ProjectWorkflow.BCG),
        OTHER_CHARGE("Other Charge", 35, specifed_model = ProjectWorkflow.BCG),
        BILLED_OR_NOT("Billed or Not", 36, specifed_model = ProjectWorkflow.BCG),
        INVESTMENT_TARGET("Investment Target", 37),

        // add project case_type
        PROJECT_CASE_TYPE("Project Case Type", 38),

        //https://www.notion.so/capvision/Warburg-Usage-Report-64c99200ea0749848516863f11e0eb4c
        BILLING_DATE("Billing Date", 39),
        SECTOR("Sector", 40),
        CLIENT_REQUEST("Client Request", 41),

        /**
         * Project User2: 项目的**Client Contacts中去除掉这个Task绑定的Client Contact之后的列表中的第一个Client Contact的名字**
         * Project User3: 项目的**Client Contacts中去除掉这个Task绑定的Client Contact之后的列表中的第二个Client Contact的名字**
         */
        PROJECT_USER2("Project User2", 42),
        PROJECT_USER3("Project User3", 43),
        COMPLIANCE_CHAPERONED("Compliance Chaperoned", 44),
        DURATION("Duration", 45),
        EXPERT_NETWORK("Expert Network", 46),
        INTERACTION_TYPE("Interaction Type", 47),
        ADDITIONAL_COLUMN_1("Additional Column 1", 48),
        ADDITIONAL_COLUMN_2("Additional Column 2", 49),

        //https://www.notion.so/capvision/Tengyue-Usage-Report-f870216a6dfe452fac4ed420c7149eb4
        REFERENCE_NUMBER("Reference Number", 50),

        //https://www.notion.so/capvision/Usage-Report-Change-e6b22a10128f4d9a98b1b248cceb491e
        EXPERT_BACKGROUND("Expert Background", 51),

        //https://www.notion.so/capvision/Kora-Capital-US-Compliance-Approval-Date-for-Usage-Report-e2eab3a7101e42d4a74cc6a95f1abef4
        CLIENT_COMPLIANCE_APPROVAL_DATE("Client Compliance Approval Date", 52),

        INVESTMENT_TARGET_IS_PUBLIC("Is Investment Target Public?", 53),
        ALL_PROJECT_CLIENT_CONTACTS("All Project Users", 54),
        ACTUAL_EXPERT_DURATION("Actual Expert Duration", 55, specifed_model = ProjectWorkflow.BCG),
        EXPERT_PAYMENT_AMOUNT("Expert Payment Amount", 56, specifed_model = ProjectWorkflow.BCG),
        TASK_LINK("Task Link", 57)
        ;

        companion object {
            /**
             * > US DB 的 usage report 的默认为如下字段格式：
             * Date,
             * User,
             * Project,
             * Expert's Name,
             * Expert's Position,
             * Expert's Company,
             * Type,
             * Hours,
             * Charge,
             * Remarks
             * -- Ping's requirement
             */
            val standards = listOf(
                REVENUE_TIME,
                CLIENT_CONTACT_NAME,
                PROJECT,
                ADVISOR_NAME,
                ADVISOR_POSITION,
                ADVISOR_COMPANY,
                TASK_INTERVIEW_TYPE,
                REVENUE_BILLABLE_HOUR,
                REVENUE_CASH,
                TASK_BILLING_NOTES
            )

            val WORKFLOW_MAP = ProjectWorkflow.values().associateWith {
                CommonUsageExcelModel.computedFields
            } + mapOf(
                ProjectWorkflow.BCG to BCGUsageExcelModel.computedFields,
                ProjectWorkflow.GA to GAUsageExcelModel.computedFields,
            )

            fun compute_all(
                standard: List<AvailableFields>,
                all: List<AvailableFields>,
            ): List<AvailableFields> {
                val default_order = standard.withIndex()
                    .associate { (index, value) ->
                        value.is_standard = true
                        value to index
                    }
                return all.sortedWith(compareBy(
                    // 1st, by order in standard list
                    { default_order[it] ?: Int.MAX_VALUE },
                    // 2nd, by order definition order(ordinal property)
                    { it.ordinal }
                ))
            }
        }
    }
}
