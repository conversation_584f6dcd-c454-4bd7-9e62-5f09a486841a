package com.cpvsn.rm.core.features.finance.usage

import com.cpvsn.rm.core.extensions.compute_compliance_chaperoned
import com.cpvsn.rm.core.extensions.compute_expert_name
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.misc.company.Company
import com.cpvsn.rm.core.features.project.Project
import java.math.BigDecimal
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter
import java.util.*

interface UsageItem

open class CommonUsageItem(
    var revenue: Revenue,
    var revenue_time: Instant,
    var type: String,
    var subtype: String,
    var case_type: String,
    var task_tsid_with_mark: String,
    // will be used in template
    var task_time_range: String,
    var contact_info: ContactInfo,
    var compliance_chaperoned: String,
    var expert_name: String,
    var current_company: String,
    var current_position: String,
    var current_company_public_status: String,
    var refund: String,
    val start_time_without_date: String,
    val end_time_without_date: String,
    val client_rate: Double?,
    var investment_target: String,
    var duration: String,
    var interaction_type: String,
    var tengyue_refernce_number: String,
    var compliance_approval_date: String,
    var investment_target_is_public: String,
    var project_client_contacts: String,
    var task_link: String
) : UsageItem {

    // those fields are only for specified client currently - GA
    var ga_model_info: GaModelInfo? = null

    data class GaModelInfo(
        val account_type: String,
    )

    // those fields are only for specified client - BCG
    var bcg_model_info: BcgModelInfo? = null

    data class BcgModelInfo(
        val expert_id: String,
        val bcg_billed_hours: Double?,
        val agency_name: String,
        val expert_level: String,
        val other_charge: Double?,
        val billed_or_not: String,
        val actual_expert_duration: Int?,
        val expert_payment_amount: BigDecimal?
    )

    data class ContactInfo(
        val project_user2_name: String,
        val project_user3_name: String,
    )

    companion object {
        private val time_formatter = DateTimeFormatter.ofPattern("HH:mm")

        fun handle_common(
            revenue: Revenue,
            zoneId: ZoneId,
        ): CommonUsageItem {
            val type = when (revenue.project?.sub_type) {
                Project.SubType.Survey, Project.SubType.Patients -> {
                    revenue.project?.sub_type?.name.orEmpty()
                }

                else -> {
                    revenue.task?.interview_type?.usage_excel_name ?: "Phone"
                }
            }
            val revenue_time = revenue.task?.start_time ?: revenue.revenue_time
            val case_type = revenue.project?.case_type?.value.orEmpty()
            val start_time = revenue.task?.start_time
                ?.let {
                    time_formatter.withZone(zoneId).format(it)
                }
            val end_time = revenue.task?.end_time
                ?.let {
                    time_formatter.withZone(zoneId).format(it)
                }
            val task_time_range = if (start_time == null || end_time == null) {
                "N/A"
            } else {
                "$start_time-$end_time"
            }
            val subtype = revenue.task?.sub_type_enum?.display_name.orEmpty()
            val project_client_contacts = revenue.project?.client_contacts?.joinToString { it.name } ?: ""
            val binded_client_contact_id = revenue.task?.client_contact_id
            val client_contacts_exlude_cur_task = revenue.project?.client_contacts?.filter {
                it.id != binded_client_contact_id
            }
            val contact_info = ContactInfo(
                project_user2_name = client_contacts_exlude_cur_task?.firstOrNull()?.name.orEmpty(),
                project_user3_name = client_contacts_exlude_cur_task?.getOrNull(1)?.name.orEmpty()
            )
            val task_tsid_with_mark = revenue.task?.tsid?.let {
                "CAP_${it}"
            }.orEmpty()
            val advisor_current_job = revenue.task?.advisor?.current_jobs?.sortedBy { it.update_at }?.lastOrNull()
            val current_company = advisor_current_job?.company?.name.orEmpty()
            val current_position = advisor_current_job?.position.orEmpty()
            val current_company_public_status =
                when (advisor_current_job?.company?.type) {
                    Company.Type.LISTED, Company.Type.PART_OF_LISTED_GROUP -> "Listed"
                    Company.Type.OWNED -> "Owned"
                    Company.Type.PRIVATE -> "Private"
                    else -> ""
                }
            val refund = if (revenue.billing_notes?.contains("discount") == true) {
                "YES"
            } else {
                "NO"
            }
            val date_formatter = DateTimeFormatter.ofPattern(
                "yyyy/M/dd",
                Locale.US,
            ).withZone(zoneId)
            val only_time_formatter = DateTimeFormatter.ofPattern("HH:mm").withZone(zoneId)
            val start_time_without_date = revenue.task?.start_time?.let { only_time_formatter.format(it) }.orEmpty()
            val end_time_without_date = revenue.task?.end_time?.let { only_time_formatter.format(it) }.orEmpty()
            val client_rate = revenue.task?.client_rate_usd?.toDouble()
            val investment_target = revenue.project?.investment_target_companies?.mapNotNull {
                it.name
            }?.joinToString(", ").orEmpty()
            val investment_target_is_public = revenue.project?.investment_target_companies?.mapNotNull {
                it.type?.value
            }?.joinToString(", ").orEmpty()
            val duration = revenue.task?.client_duration?.toString().orEmpty()
            val interaction_type =
                if (revenue.project?.type == Project.Type.SURVEY || revenue.project?.sub_type == Project.SubType.Survey) {
                    "Survey"
                } else {
                    "Call"
                }
            val task_link = revenue.task?.db_page_url_jit ?: ""
            return CommonUsageItem(
                revenue = revenue,
                revenue_time = revenue_time,
                type = type,
                subtype = subtype,
                case_type = case_type,
                task_time_range = task_time_range,
                contact_info = contact_info,
                compliance_chaperoned = compute_compliance_chaperoned(revenue),
                expert_name = compute_expert_name(revenue),
                task_tsid_with_mark = task_tsid_with_mark,
                current_company = current_company,
                current_position = current_position,
                current_company_public_status = current_company_public_status,
                refund = refund,
                start_time_without_date = start_time_without_date,
                end_time_without_date = end_time_without_date,
                client_rate = client_rate,
                investment_target = investment_target,
                duration = duration,
                interaction_type = interaction_type,
                tengyue_refernce_number = (revenue.task?.client_custom_fields?.get("reference_number") as? String).orEmpty(),
                compliance_approval_date = revenue.task?.compliance_approval_date?.let {
                    date_formatter.format(it)
                }.orEmpty(),
                investment_target_is_public = investment_target_is_public,
                project_client_contacts = project_client_contacts,
                task_link = task_link
            )
        }
    }
}