package com.cpvsn.rm.core.features.finance.usage.specified.d1

import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.finance.usage.CommonUsageItem
import com.cpvsn.rm.core.features.finance.usage.UsageItem
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.project.Project
import java.math.RoundingMode
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class D1UsageItem(
    val task_tsid: String,
    val call_start_date: String,
    val call_end_date: String,
    val event_info: String,
    val user_name: String,
    val user_email: String,
    val normal_item: CommonUsageItem,
) : UsageItem {
    companion object {

        fun handle_d1_item(revenue: Revenue, zone_id: ZoneId): D1UsageItem {
            val project_type = revenue.project?.sub_type
            val task = revenue.task
            val hours = revenue.billing_hours.setScale(2, RoundingMode.HALF_UP).stripTrailingZeros().toPlainString()
            val cash = revenue.cash?.setScale(2, RoundingMode.HALF_UP)?.stripTrailingZeros()?.toPlainString()
            val event_info = if (project_type == Project.SubType.Survey) {
                "incoming call ${if (revenue.cash_currency == BuiltInCurrency.USD) "$" else ""}${cash}"
            } else {
                "incoming call w/${task?.advisor?.name} ${hours} hour ${if (revenue.cash_currency == BuiltInCurrency.USD) "$" else ""}${cash}"
            }
            val formatter = DateTimeFormatter.ofPattern("M/d/yy HH:mm")
                .withZone(zone_id)
            return D1UsageItem(
                task_tsid = revenue.task?.tsid.orEmpty(),
                call_start_date = revenue.task?.start_time?.atZone(zone_id)?.format(formatter)
                    .orEmpty(),
                call_end_date = revenue.task?.end_time?.atZone(zone_id)?.format(formatter)
                    .orEmpty(),
                event_info = event_info,
                user_name = revenue.client_contact?.name.orEmpty(),
                user_email = revenue.client_contact?.email.orEmpty(),
                normal_item = CommonUsageItem.handle_common(
                    revenue = revenue,
                    zoneId = zone_id,
                )
            )
        }
    }

}