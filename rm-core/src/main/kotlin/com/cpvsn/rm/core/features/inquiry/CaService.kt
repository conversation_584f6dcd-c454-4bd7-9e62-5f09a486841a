package com.cpvsn.rm.core.features.inquiry

import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.rm.core.extensions.ensure_at_most_one
import com.cpvsn.rm.core.features.inquiry.model.Inquiry
import com.cpvsn.rm.core.features.inquiry.model.InquiryBranch
import com.cpvsn.rm.core.util.biz_check
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CaService : TypedService {

    //region @
    @Autowired
    private lateinit var inquiryBranchService: InquiryBranchService
    //endregion

    fun check_exist_when_create_ca_inquiry(inquiry: Inquiry) {

        val client_id = inquiry.client_id.assert_valid_id()
        val exists = Inquiry.findAll(Inquiry.Query(
                client_id = client_id,
                type = inquiry.type
        )).ensure_at_most_one("More than one CA of type:${inquiry.type} found for client:$client_id.")

        biz_check(exists == null) {
            "Client:$client_id already has an CA of type:${inquiry.type}."
        }
    }

    fun check_exist_when_create_outsourced_project_ca_inquiry(inquiry: Inquiry) {
        val project_id = inquiry.project_id.assert_valid_id()
        val exists = Inquiry.findAll(Inquiry.Query(
            project_id = project_id,
            type = inquiry.type
        )).ensure_at_most_one("More than one CA of type:${inquiry.type} found for outsourced project:$project_id.")

        biz_check(exists == null) {
            "Project:$project_id already has an CA of type:${inquiry.type}."
        }
    }

    override fun decide_default_when_create_branch(branch: InquiryBranch) {
        // rule: at most one branch of locale is default
        val existBranches = InquiryBranch.findAll(InquiryBranch.Query(
                inquiry_id = branch.inquiry_id,
                locale = branch.locale
        ))
        biz_check(existBranches.filter { it.is_default_using }.size <= 1) {
            "Default branch of CA:${branch.inquiry_id}, ${branch.locale} is more than one."
        }
        when {
            existBranches.isEmpty() -> branch.is_default_using = true
            branch.is_default_using -> inquiryBranchService.erase_branch_default_info_of_inquiry(branch.inquiry_id, locale = branch.locale)
        }
    }

}
