package com.cpvsn.rm.core.features.inquiry

import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.id
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.features.inquiry.model.Inquiry
import com.cpvsn.rm.core.features.inquiry.model.InquiryBranch
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.inquiry.model.InquiryQuestion
import com.cpvsn.rm.core.features.inquiry.pojo.UpdateOneOffRequest
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import com.cpvsn.rm.core.util.biz_error
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.RequestBody
import java.time.Instant

@Service
class InquiryInstanceService {
    //region @
    @Autowired
    private lateinit var inquiryBranchService: InquiryBranchService

    @Autowired
    private lateinit var inquiryInstanceRepo: InquiryInstanceRepo
    //endregion

    fun answer(
        instance_id: Int,
        qa_list: List<InquiryQuestion>,
        passed_client_publish_question_ids: List<Int>? = null,
    ): InquiryInstance {
        if (passed_client_publish_question_ids != null) {
            return answer(
                patch = Patch.of(
                    entity = InquiryInstance {
                        this.id = instance_id
                        this.client_publish_question_ids = passed_client_publish_question_ids
                        this.qa_list_snapshot = qa_list
                    },
                    fields = Includes.setOf(
                        InquiryInstance::qa_list_snapshot,
                        InquiryInstance::client_publish_question_ids
                    )
                )
            )
        } else {
            return answer(
                patch = Patch.of(
                    entity = InquiryInstance {
                        this.id = instance_id
                        this.qa_list_snapshot = qa_list
                    },
                    fields = Includes.setOf(
                        InquiryInstance::qa_list_snapshot,
                    )
                )
            )
        }
    }

    fun answer(
        patch: Patch<InquiryInstance>,
    ): InquiryInstance {
        InquiryInstance.patch(patch)
        val instance = InquiryInstance.get(patch.id)
        val auto_judged_result_status = auto_judge(patch.entity)
        InquiryInstance {
            id = instance.id
            status = auto_judged_result_status ?: InquiryInstance.Status.SUBMITTED
            submit_time = Instant.now()
        }.patch(
            Includes.setOf(
                InquiryInstance::status,
                InquiryInstance::submit_time,
            )
        )

        return InquiryInstance.get(patch.id)
    }

    fun auto_judge(
        instance: InquiryInstance,
    ): InquiryInstance.Status? {
        val selected_options = instance.questions.filter { it.is_option_question }
            .flatMap { question ->
                val ans = question.answer
                when (question.type) {
                    InquiryQuestion.Type.YES_NO, InquiryQuestion.Type.RADIO ->
                        ans?.radio_selected?.let { n -> listOf(question.options!![n]) } ?: emptyList()

                    InquiryQuestion.Type.CHECKBOX ->
                        ans?.checkbox_selected?.let { ns -> ns.map { question.options!![it] } } ?: emptyList()

                    else -> error("impossible situation")
                }
            }

        val res_on_option_questions = when {
            selected_options.any { it.type == InquiryQuestion.OptionType.DENY } -> InquiryInstance.Status.AUTO_REJECTED
            selected_options.any { it.type == InquiryQuestion.OptionType.HOLD } -> InquiryInstance.Status.AUTO_HOLD
            else -> InquiryInstance.Status.AUTO_PASSED
        }
        return when {
            instance.questions.all { it.is_option_question } -> res_on_option_questions
            res_on_option_questions == InquiryInstance.Status.AUTO_REJECTED -> InquiryInstance.Status.AUTO_REJECTED
            else -> null
        }
    }

    fun km_comment(
        patch: JacksonPatch<InquiryInstance>,
    ): InquiryInstance {
        InquiryInstance.patch(patch)

        InquiryInstance {
            id = patch.id
            km_commented = true
        }.patch(Includes.setOf(InquiryInstance::km_commented))

        return InquiryInstance.get(patch.id)
    }


    fun bulk_update_instance_oneoff(
        requests: List<UpdateOneOffRequest>
    ): List<InquiryInstance> {
        return requests.map {
            update_instance_oneoff(it)
        }
    }

    /**
     * 添加task specific的questions.给新加的问题生成一个id: -1,-2...
     */
    fun update_instance_oneoff(
        @RequestBody request: UpdateOneOffRequest,
    ): InquiryInstance {
        val added = request.qa_list_snapshot.filter {
            it.id == 0
        }
        val currentMinId = request.qa_list_snapshot.minOf {
            it.id
        }.takeIf { it <= 0 } ?: 0
        added.forEachIndexed { index, inquiryQuestion ->
            inquiryQuestion.id = currentMinId - 1 - index
            inquiryQuestion.is_one_off_added_question = true
        }

        val res = if (request.id != null) {
            val original_inquiry_instance = InquiryInstance.get(request.id)
            val ids_after_added = original_inquiry_instance.advisor_screening_question_ids?.let {
                original_inquiry_instance.advisor_screening_question_ids?.plus(
                    request.qa_list_snapshot.map { it.id }.subtract(
                        original_inquiry_instance.qa_list_snapshot.map { it.id }.toSet()
                    )
                )
            }
            val res = Patch.fromMap(
                request.id, mapOf(
                    InquiryInstance::qa_list_snapshot to request.qa_list_snapshot,
                    InquiryInstance::display_previous_answered_questions to request.display_previous_answered_questions,
                    InquiryInstance::display_previous_answeres_read_only to request.display_previous_answeres_read_only,
                    InquiryInstance::allow_expert_to_reanswer to request.allow_expert_to_reanswer,
                    InquiryInstance::advisor_screening_question_ids to ids_after_added,
                )
            ).patchThenGet()

            Patch.fromMutator(res) {
                this.is_one_off_question = true
                this.client_publish_question_ids =
                    this.client_publish_question_ids?.let { not_null_client_publish_ids ->
                        not_null_client_publish_ids.toSet() + added.ids()
                    }?.toList() ?: res.qa_list_snapshot.ids().toList()
            }.patchThenGet()
        } else {
            val task = request.task_id?.let { Task.find(it) } ?: biz_error("task not found")
            val project = task.project_id.let { Project.find(it) } ?: biz_error("project not found")
            val project_branch = InquiryBranch.firstOrNull(
                query = InquiryBranch.Query(
                    inquiry_id = project.sq_id ?: 0
                )
            ) ?: biz_error("no sq for this project")
            // need to create one if original sq instance not exists
            inquiryInstanceRepo.direct_save(
                InquiryInstance {
                    this.source_id = request.task_id
                    this.branch_id = project_branch.id
                    this.inquiry_type = Inquiry.Type.SCREENING
                    this.create_type = InquiryInstance.CreateType.BEFORE_TASK
                    this.branch_snapshot = project_branch
                    this.qa_list_snapshot = request.qa_list_snapshot
                    this.display_previous_answered_questions = request.display_previous_answered_questions
                    this.display_previous_answeres_read_only = request.display_previous_answeres_read_only
                    this.allow_expert_to_reanswer = request.allow_expert_to_reanswer
                    this.advisor_screening_question_ids = qa_list_snapshot.ids().toList()
                    this.client_publish_question_ids = added.ids().toList()
                    this.is_one_off_question = true
                }
            )
        }
        // sync es
        if (res.inquiry_type == Inquiry.Type.SCREENING) {
            InvokeUtil.trigger(Event.ADVISOR_DOC_CHANGED(advisor_id = res.receiver_id))
        }
        return res
    }

    fun fix_0_id_qa_snapshot(
        inquiry_instances: List<InquiryInstance>
    ) {
        inquiry_instances.forEach {
            val qa_list_snapshot =
                CoreJsonUtil.parse<List<InquiryQuestion>>(CoreJsonUtil.stringify(it.qa_list_snapshot))
            val filtered_qa_list = qa_list_snapshot.filter { it.id != 0 }.sortedBy { it.sort_order }
            filtered_qa_list.forEachIndexed { index, qa ->
                qa.sort_order = index
                qa.display_order = index + 1
            }
            Patch.fromMutator(it) {
                this.qa_list_snapshot = filtered_qa_list
            }.patch()
        }
    }
}
