package com.cpvsn.rm.core.features.inquiry.pojo

import com.cpvsn.rm.core.features.inquiry.model.InquiryQuestion

class InquiryQuestionOption {
    var text: String = ""
    var type: InquiryQuestion.OptionType = InquiryQuestion.OptionType.HOLD
    var value: String = ""

    var desc_prompt: String = ""
    var description: Boolean = false   // Whether an additional explanation is needed
    var require_desc: Boolean = false  // Whether the explanation is mandatory
    var desc_type: DescType = DescType.ADVISOR_EXPLAIN_ANSWER_CHOICE

    var sub_type: InquiryQuestion.OptionSubType = InquiryQuestion.OptionSubType.COMMON
    var order: Int = 0
    var is_red_herring: Boolean? = null
}

enum class DescType {
    ADVISOR_EXPLAIN_ANSWER_CHOICE,
    FOLLOW_UP_QUESTION_ANSWER_CHOICE,
}
