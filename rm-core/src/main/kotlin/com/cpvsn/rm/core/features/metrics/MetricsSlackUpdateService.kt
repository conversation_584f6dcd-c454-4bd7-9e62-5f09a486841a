package com.cpvsn.rm.core.features.metrics

import com.cpvsn.core.util.extension.biz_assert_not_null
import com.cpvsn.rm.core.extensions.toDefaultInstant
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntryService
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.user.team.Team
import com.slack.api.methods.response.chat.ChatPostMessageResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class MetricsSlackUpdateService {

    @Autowired
    private lateinit var slackService: SlackService

    @Autowired
    private lateinit var appConfigEntryService: AppConfigEntryService

    /**
     * Sends a message to a predetermined Slack channel that details the number of calls scheduled today, and the number of new projects created today, for a specific client.
     */
    fun send_projects_slack_update(client_id: Int): ChatPostMessageResponse? {
        val today = LocalDate.now()
        val beginning_of_day = today.atStartOfDay().toDefaultInstant()
        val end_of_day = today.plusDays(1).atStartOfDay().toDefaultInstant()

        /**
         * Ensure that only Research Team calls are counted
         */
        val team = Team.findAll(
            Team.Query(
                description_contains = "Entire US Research Team"
            ),
            include = setOf(Team::team_members.name)
        ).firstOrNull().biz_assert_not_null()
        val user_ids = team.team_members?.mapNotNull { it.member_id }

        val client = Client.get(client_id)
        // count all BCG call scheduled for today
        val tasks = Task.findAll(
            Task.Query(
                start_time_gte = beginning_of_day,
                start_time_lte = end_of_day,
                client_id = client_id,
                project_sub_type_in = setOf(
                    Project.SubType.Consultation,
                    Project.SubType.`Investor Call`,
                    Project.SubType.`Physician Consultation`,
                    Project.SubType.`Longer Term Engagement`,
                    Project.SubType.Patients
                ),
                general_status_in = setOf(
                    TaskStatus.General.COMPLETED,
                    TaskStatus.General.ARRANGED,
                    TaskStatus.General.SCHEDULED
                ),
                call_tracker_status_is_null = true,
                or = user_ids?.let {
                    listOf(
                        Task.Query(
                            support_ids = user_ids.toSet()
                        ),
                        Task.Query(
                            lead_ids = user_ids.toSet()
                        )
                    )
                }
            ),
            include = setOf(Task::project.name)
        )
        val projects = Project.findAll(
            Project.Query(
                create_at_gte = beginning_of_day,
                create_at_lte = end_of_day,
                client_id = client_id
            )
        )
        val counts = ProjectsSlackUpdate(client.name ?: "", tasks, projects)
        val channel = appConfigEntryService.find(AppConfigKey.CALL_TRACK_SLACK_CHANNEL_ID)?.value
        return channel?.let {
            slackService.postTextMessageToChannel(
                counts.message,
                it
            )
        }
    }

    /**
     * Sends a message to a predetermined Slack channel that details the number of calls scheduled for today.
     */
    fun send_call_track_slack(): ChatPostMessageResponse? {
        val today = LocalDate.now()
        val beginning_of_day = today.atStartOfDay().toDefaultInstant()
        val end_of_day = today.plusDays(1).atStartOfDay().toDefaultInstant()
        val beginning_of_month = today.withDayOfMonth(1).atStartOfDay().toDefaultInstant()
        val end_of_month = today.plusMonths(1).withDayOfMonth(1).atStartOfDay().toDefaultInstant()
        val end_of_next_month = today.plusMonths(2).withDayOfMonth(1).atStartOfDay().toDefaultInstant()

        /**
         * Ensure that only Research Team calls are counted
         */
        val team = Team.findAll(
            Team.Query(
                description_contains = "Entire US Research Team"
            ),
            include = setOf(Team::team_members.name)
        ).firstOrNull().biz_assert_not_null()
        val user_ids = team.team_members?.mapNotNull { it.member_id }

        val tasks_count = Task.count(
            Task.Query(
                start_time_gte = beginning_of_day,
                start_time_lte = end_of_day,
                general_status_in = setOf(
                    TaskStatus.General.COMPLETED,
                    TaskStatus.General.ARRANGED,
                    TaskStatus.General.SCHEDULED
                ),
                project_sub_type_in = setOf(
                    Project.SubType.Consultation,
                    Project.SubType.`Investor Call`,
                    Project.SubType.`Physician Consultation`,
                    Project.SubType.`Longer Term Engagement`,
                    Project.SubType.Patients
                ),
                call_tracker_status_is_null = true,
                or = user_ids?.let {
                    listOf(
                        Task.Query(
                            support_ids = user_ids.toSet()
                        ),
                        Task.Query(
                            lead_ids = user_ids.toSet()
                        )
                    )
                }
            )
        )
        val month_tasks_count = Task.count(
            Task.Query(
                start_time_gte = beginning_of_month,
                start_time_lte = end_of_month,
                general_status_in = setOf(
                    TaskStatus.General.COMPLETED,
                    TaskStatus.General.ARRANGED,
                    TaskStatus.General.SCHEDULED
                ),
                project_sub_type_in = setOf(
                    Project.SubType.Consultation,
                    Project.SubType.`Investor Call`,
                    Project.SubType.`Physician Consultation`,
                    Project.SubType.`Longer Term Engagement`,
                    Project.SubType.Patients
                ),
                call_tracker_status_is_null = true,
                or = user_ids?.let {
                    listOf(
                        Task.Query(
                            support_ids = user_ids.toSet()
                        ),
                        Task.Query(
                            lead_ids = user_ids.toSet()
                        )
                    )
                }
            )
        )
        val next_month_tasks_count = Task.count(
            Task.Query(
                start_time_gte = end_of_month,
                start_time_lte = end_of_next_month,
                general_status_in = setOf(
                    TaskStatus.General.COMPLETED,
                    TaskStatus.General.ARRANGED,
                    TaskStatus.General.SCHEDULED
                ),
                project_sub_type_in = setOf(
                    Project.SubType.Consultation,
                    Project.SubType.`Investor Call`,
                    Project.SubType.`Physician Consultation`,
                    Project.SubType.`Longer Term Engagement`,
                    Project.SubType.Patients
                ),
                call_tracker_status_is_null = true,
                or = user_ids?.let {
                    listOf(
                        Task.Query(
                            support_ids = user_ids.toSet()
                        ),
                        Task.Query(
                            lead_ids = user_ids.toSet()
                        )
                    )
                }
            )
        )
        val message = "There are $tasks_count calls scheduled for today. There are $month_tasks_count calls scheduled for this month and $next_month_tasks_count calls scheduled for next month."
        val channel = appConfigEntryService.find(AppConfigKey.CALL_TRACK_SLACK_CHANNEL_ID)?.value
        return channel?.let {
            slackService.postTextMessageToChannel(
                message,
                it
            )
        }
    }

    /**
     * Counts the tasks and projects in each case_type and reports the results in a structured message
     */
    data class ProjectsSlackUpdate(
        val client_name: String,
        val tasks: List<Task>,
        val projects:List<Project>
    ) {
        val task_due_diligence_count = tasks.count{ it.project?.case_type == Project.CaseType.DUE_DILIGENCE}
        val task_strategy_count = tasks.count{ it.project?.case_type == Project.CaseType.STRATEGY}
        val task_proposal_count = tasks.count{ it.project?.case_type == Project.CaseType.PROPOSAL_OR_LOP}
        val task_knowledge_effort_count = tasks.count{ it.project?.case_type == Project.CaseType.KNOWLEDGE_EFFORT_OR_INTERNAL}

        val task_message = "- $task_due_diligence_count due diligence calls scheduled\n- $task_strategy_count strategy calls scheduled\n- $task_proposal_count proposal calls scheduled\n- $task_knowledge_effort_count knowledge effort calls scheduled"

        val project_due_diligence_count = projects.count{ it.case_type == Project.CaseType.DUE_DILIGENCE}
        val project_strategy_count = projects.count{ it.case_type == Project.CaseType.STRATEGY}
        val project_proposal_count = projects.count{ it.case_type == Project.CaseType.PROPOSAL_OR_LOP}
        val project_knowledge_effort_count = projects.count{ it.case_type == Project.CaseType.KNOWLEDGE_EFFORT_OR_INTERNAL}

        val project_message = "- $project_due_diligence_count new due diligence projects\n- $project_strategy_count new strategy projects\n- $project_proposal_count new proposal projects\n- $project_knowledge_effort_count knowledge effort projects"

        val message = "Today, $client_name has:\n\n$task_message\n\n$project_message"
    }
}