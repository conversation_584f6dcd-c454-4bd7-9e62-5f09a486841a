package com.cpvsn.rm.core.features.metrics.pojo

import java.math.BigDecimal

enum class CallStatus {
    SCHEDULED,
    SELECTED,
    COMPLETED
}

class EmployeeCallsSummary {
    var uid: Int = 0
    var username: String = ""
    var name: String = ""

    var total_calls: Int = 0
    var total_client_hours: BigDecimal? = null
    var sum_of_billable_hours: BigDecimal? = null
    var call_status: CallStatus? = null
}

class ClientCallsSummary {
    var client_id: Int = 0
    var client_name: String = ""

    var total_calls: Int = 0
    var sum_of_billable_hours: BigDecimal? = null
    var call_status: CallStatus? = CallStatus.SCHEDULED
}

class IndividualPerformanceSummary {
    var uid: Int = 0
    var username: String = ""
    var name: String = ""

    var total_calls: Int = 0
    var total_client_hours: BigDecimal? = null
    var team_avg_client_hour: BigDecimal? = null
    var remarks: String? = null
}