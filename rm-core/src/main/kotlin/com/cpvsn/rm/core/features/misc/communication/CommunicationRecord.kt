package com.cpvsn.rm.core.features.misc.communication

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.model.HasClientContactId
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.email.EmailRecord
import com.cpvsn.rm.core.features.email.EmailRecordTracking
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisor
import com.cpvsn.rm.core.features.portal.clientcontact.PortalClientContact
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.user.User
import java.time.Instant

class CommunicationRecord : RmEntity(), UserAuditing, HasClientContactId<Int?> {

    companion object : RmCompanion<CommunicationRecord>()

    @Column
    var project_id: Int? = null

    @Column
    var task_id: Int? = null

    @Column
    var advisor_id: Int? = null

    @Column
    var client_id: Int? = null

    @Column
    override var client_contact_id: Int? = null

    @Column
    var email_record_id: Int = 0

    @Column
    var email_content_type: EmailContentType? = null

    @Column
    var email_template_id: Int? = null

    @Column
    var portal_id: Int? = null

    @Column
    var portal_type: PortalType? = null

    @Column
    var via_linkedin: Boolean? = false

    @Column
    override var create_by_id: Int = 0

    override var update_by_id: Int = 0

    @Relation
    var email_record: EmailRecord? = null

    @Relation
    var email_template: EmailTemplate? = null

    @Relation
    var client: Client? = null

    @Relation
    var project: Project? = null

    @Relation
    var client_contact: ClientContact? = null

    @Relation
    var create_by: User? = null

    var contact_portal: PortalClientContact? = null
    var advisor_portal: PortalAdvisor? = null

    var advisor_tracking: EmailRecordTracking? = null
    var client_contact_tracking: EmailRecordTracking? = null

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.IsNull
        val id_is_null: Boolean? = null,

        @Criteria.Eq
        val project_id: Int? = null,
        @Criteria.IdsIn
        val project_ids: Set<Int>? = null,

        @Criteria.Eq
        val task_id: Int? = null,
        @Criteria.IdsIn
        val task_ids: Set<Int>? = null,

        @Criteria.Eq
        val advisor_id: Int? = null,
        @Criteria.IdsIn
        val advisor_ids: Set<Int>? = null,
        @Criteria.IsNull
        val advisor_id_is_null: Boolean? = null,

        @Criteria.Eq
        val client_id: Int? = null,
        @Criteria.IdsIn
        val client_ids: Set<Int>? = null,

        @Criteria.Eq
        val client_contact_id: Int? = null,
        @Criteria.IdsIn
        val client_contact_ids: Set<Int>? = null,
        @Criteria.IsNull
        val client_contact_id_is_null: Boolean? = null,

        @Criteria.Eq
        val portal_id: Int? = null,
        @Criteria.Eq
        val create_by_id: Int? = null,
        @Criteria.IdsIn
        val create_by_ids: Set<Int>? = null,

        @Criteria.Gte
        val create_at_gte: Instant? = null,
        @Criteria.Lte
        val create_at_lte: Instant? = null,

        @Criteria.Eq
        val email_content_type: EmailContentType? = null,

        @Criteria.Eq
        val email_record_id: Int? = null,

        @Criteria.Join
        val email_record: EmailRecord.Query? = null,
        @Criteria.Join
        val email_template: EmailTemplate.Query? = null,

        @Criteria.Join(on = "{this}.email_record_id={that}.email_record_id and {this}.advisor_id = {that}.advisor_id")
        val advisor_tracking: EmailRecordTracking.Query? = null,
        @Criteria.Join(on = "{this}.email_record_id={that}.email_record_id and {this}.client_contact_id = {that}.client_contact_id")
        val client_contact_tracking: EmailRecordTracking.Query? = null,
    ) : BaseQuery<CommunicationRecord>()

}
