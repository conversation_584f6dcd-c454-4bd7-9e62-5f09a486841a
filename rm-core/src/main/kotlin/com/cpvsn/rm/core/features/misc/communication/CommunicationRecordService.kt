package com.cpvsn.rm.core.features.misc.communication

import com.cpvsn.core.util.extension.biz_error
import com.cpvsn.core.util.extension.remove_prefix
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.util.*
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.email.EmailRecordTracking
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email.pojo.PersistableEmailRequest
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisor
import com.cpvsn.rm.core.features.portal.clientcontact.PortalClientContact
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.outreach.OutreachBatchRequest
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CommunicationRecordService : RmBaseRepository<CommunicationRecord>() {

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var taskEventService: TaskEventService

    fun send_outreach_email(
        email_request: PersistableEmailRequest,
        task: Task
    ): CommunicationRecord {
        val email_record = emailService.send_outreach(email_request)
        val record = CommunicationRecord {
            this.project_id = task.project_id
            this.task_id = task.id
            this.advisor_id = task.advisor_id
            this.email_content_type = EmailContentType.PROJECT_OUTREACH
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id

            // this is important, we need to access this property later
            this.email_record = email_record
        }
        val res = CommunicationRecord.save(record)

        // record latest outreach record id and latest task communication time
        Patch.fromMutator(task) {
            latest_outreach_record_id = res.id
            latest_task_communication_time = res.create_at
        }.patch()

        return res
    }

    fun send_mq_bulk_outreach_email(
        request: OutreachBatchRequest,
        prefer_vendor: String? = null,
    ): List<CommunicationRecord> {
        val email_records = emailService.send_mq_bulk_outreach(
            request.batch.map {
                it.email
            },
            prefer_vendor
        )
        val tasks_mapped_by_id = Task.findAll(
            query = Task.Query(
                ids = request.batch.map { it.task_id }.toSet()
            )
        ).associateBy { it.id }
        val communication_records = request.batch.mapIndexed { index, cur_request ->
            val cur_task = tasks_mapped_by_id[cur_request.task_id] ?: biz_error("task not found")
            val cur_record = email_records[index]
            val res = CommunicationRecord.save(
                CommunicationRecord {
                    this.project_id = cur_task.project_id
                    this.task_id = cur_task.id
                    this.advisor_id = cur_task.advisor_id
                    this.email_content_type = EmailContentType.PROJECT_OUTREACH
                    this.email_record_id = cur_record.id
                    this.email_template_id = cur_record.template_id

                    // this is important, we need to access this property later
                    this.email_record = cur_record
                }
            )
            taskEventService.trigger_event(
                TaskEvent {
                    this.task_id = cur_task.id
                    type = TaskEventType.OUTREACH
                }
            )
            Patch.fromMutator(cur_task) {
                latest_outreach_record_id = res.id
                latest_task_communication_time = res.create_at
            }.patch()
            res
        }
        return communication_records
    }

    fun send_contact_outreach_email(
        email_request: PersistableEmailRequest,
        client_contact: ClientContact,
    ): CommunicationRecord {
        val email_record = emailService.send_outreach(email_request)
        val record = CommunicationRecord {
            this.client_id = client_contact.client_id
            this.client_contact_id = client_contact.id
            this.email_content_type = EmailContentType.CLIENT_OUTREACH
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id

            this.email_record = email_record
        }
        return CommunicationRecord.save(record)
    }

    fun send_advisor_portal_email(
        email_request: PersistableEmailRequest,
        portal: PortalAdvisor,
        task: Task? = null,
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.project_id = task?.project_id
            this.task_id = task?.id
            this.advisor_id = portal.advisor_id
            this.email_content_type = portal.type.corresponding_email_content_type
            this.portal_id = portal.id
            this.portal_type = portal.type
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
        }
        return save(record)
    }

    fun send_advisor_payment_confirm_email(
        email_request: PersistableEmailRequest,
        email_content_type: EmailContentType,
        portal: Portal,
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.advisor_id = portal.advisor_id
            this.portal_id = portal.id
            this.portal_type = portal.type
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
            this.email_content_type = email_content_type
        }
        return save(record)
    }

    fun send_contact_portal_email(
        email_request: PersistableEmailRequest,
        project: Project,
        portal: PortalClientContact
    ): List<CommunicationRecord> {
        val email_record = emailService.send_and_persist(email_request)
        val items = portal.task_id_set.map {
            CommunicationRecord {
                this.project_id = project.id
                this.task_id = it
                this.email_content_type =
                    portal.type.corresponding_email_content_type
                this.portal_id = portal.id
                this.portal_type = portal.type
                this.email_record_id = email_record.id
                this.email_template_id = email_record.template_id
            }
        }
        return save(items)
    }

    fun send_need_approve_email_to_client_legal(
        email_request: PersistableEmailRequest,
        project: Project,
        tasks: List<Task>
    ): List<CommunicationRecord> {
        val email_record = emailService.send_and_persist(email_request)
        val record_addresses = (email_record.to_list + email_record.cc_list).toSet()
        val client_contacts = ClientContact.findAll(
            ClientContact.Query(email_in = record_addresses)
        )
        val items = tasks.flatMap { task ->
            val records = if (client_contacts.isEmpty()) {
                val communicationRecord = CommunicationRecord {
                    this.project_id = project.id
                    this.task_id = task.id
                    this.email_content_type = EmailContentType.PORTAL_COMPLIANCE
                    this.email_record_id = email_record.id
                    this.email_template_id = email_record.template_id
                }
                listOf(communicationRecord)
            } else {
                client_contacts.map { client_contact ->
                    CommunicationRecord {
                        this.project_id = project.id
                        this.task_id = task.id
                        this.client_contact_id = client_contact.id
                        this.email_content_type = EmailContentType.PORTAL_COMPLIANCE
                        this.email_record_id = email_record.id
                        this.email_template_id = email_record.template_id
                    }
                }
            }
            records
        }
        return save(items)
    }

    fun send_client_scheduled_email_to_advisor(
        email_request: PersistableEmailRequest,
        task: Task,
        email_content_type: EmailContentType,
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.project_id = task.project_id
            this.advisor_id = task.advisor_id
            this.task_id = task.id
            // this.client_id = task.client_id
            // this.client_contact_id = task.client_contact_id
            this.email_content_type = email_content_type
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
        }
        return save(record)
    }

    fun send_client_scheduled_email_to_contact(
        email_request: PersistableEmailRequest,
        task: Task,
        email_content_type: EmailContentType,
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.project_id = task.project_id
            // this.advisor_id = task.advisor_id
            this.task_id = task.id
            this.client_id = task.client_id
            this.client_contact_id = task.client_contact_id
            this.email_content_type = email_content_type
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
        }
        return save(record)
    }

    fun send_project_close_email(
        email_request: PersistableEmailRequest,
        project: Project,
        advisor: Advisor,
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.project_id = project.id
            this.advisor_id = advisor.id
            this.email_content_type = EmailContentType.NOTIFY_ADVISOR_PROJECT_CLOSED
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
        }
        record.save()
        return record
    }

    fun send_calendar_email_to_advisor(
        email_request: PersistableEmailRequest,
        task: Task,
        content_type: EmailContentType
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.project_id = task.project_id
            this.task_id = task.id
            this.advisor_id = task.advisor_id
            this.email_content_type = content_type
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
        }
        return save(record)
    }

    fun send_calendar_email_to_contact(
        email_request: PersistableEmailRequest,
        task: Task,
        content_type: EmailContentType
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.project_id = task.project_id
            this.task_id = task.id
            this.client_id = task.client_id
            this.client_contact_id = task.client_contact_id
            this.email_content_type = content_type
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
        }
        return save(record)
    }

    fun send_cancel_calendar_email(
        email_request: PersistableEmailRequest,
        task: Task,
        schedule_role: Schedule.Role? = null,
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.project_id = task.project_id
            this.task_id = task.id
            this.advisor_id = task.advisor_id
            this.client_id = task.client_id
            if (schedule_role != Schedule.Role.ADVISOR) {
                // in the communication record page, client find records via client contact_id, it's goal is to filter the email sent to advisor
                this.client_contact_id = task.client_contact_id
            }
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
        }
        return save(record)
    }

    fun send_advisor_record_consent_email(
        email_request: PersistableEmailRequest,
        task: Task,
        email_content_type: EmailContentType,
    ): CommunicationRecord {
        val email_record = emailService.send_and_persist(email_request)
        val record = CommunicationRecord {
            this.project_id = task.project_id
            this.task_id = task.id
            this.advisor_id = task.advisor_id
            this.email_record_id = email_record.id
            this.email_template_id = email_record.template_id
            this.email_content_type = email_content_type
        }
        return CommunicationRecord.save(record)
    }

    override fun handleJoin(
        list: List<CommunicationRecord>,
        include: Set<String>
    ): List<CommunicationRecord> {
        val res = super.handleJoin(list, include)

        if (include.contains(CommunicationRecord::contact_portal.name)) {
            val sub_list = res.filter {
                it.portal_type in setOf(PortalType.CONTACT_PRE_CALL)
            }
            val items = PortalClientContact.findAll(
                PortalClientContact.Query(
                    ids = sub_list.ids(CommunicationRecord::portal_id),
                ),
                include = include.remove_prefix(CommunicationRecord::contact_portal.name)
            )
            sub_list join items on CommunicationRecord::portal_id eq PortalClientContact::id into CommunicationRecord::contact_portal
        }

        if (include.contains(CommunicationRecord::advisor_portal.name)) {
            val sub_list = res.filter {
                it.portal_type in setOf(PortalType.ADVISOR_PRE_CALL)
            }
            val items = PortalAdvisor.findAll(
                PortalAdvisor.Query(
                    ids = sub_list.ids(CommunicationRecord::portal_id),
                ),
                include = include.remove_prefix(CommunicationRecord::advisor_portal.name)
            )
            sub_list join items on CommunicationRecord::portal_id eq PortalAdvisor::id into CommunicationRecord::advisor_portal
        }

        if (include.contains(CommunicationRecord::advisor_tracking.name)) {
            val map = EmailRecordTracking.findAll(
                EmailRecordTracking.Query(
                    email_record_ids = res.ids(CommunicationRecord::email_record_id)
                ), include.remove_prefix(CommunicationRecord::advisor_tracking)
            ).groupBy {
                it.email_record_id
            }
            res.forEach { record ->
                record.advisor_tracking = map[record.email_record_id]
                    .orEmpty()
                    .find { it.advisor_id == record.advisor_id }
            }
        }

        if (include.contains(CommunicationRecord::client_contact_tracking.name)) {
            val map = EmailRecordTracking.findAll(
                EmailRecordTracking.Query(
                    email_record_ids = res.ids(CommunicationRecord::email_record_id)
                ),
                include.remove_prefix(CommunicationRecord::client_contact_tracking)
            ).groupBy {
                it.email_record_id
            }
            res.forEach { record ->
                record.client_contact_tracking = map[record.email_record_id]
                    .orEmpty()
                    .find { it.client_contact_id == record.client_contact_id }
            }
        }

        return res
    }
}
