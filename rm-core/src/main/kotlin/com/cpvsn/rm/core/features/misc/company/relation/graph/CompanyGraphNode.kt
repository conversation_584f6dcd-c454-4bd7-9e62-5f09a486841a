package com.cpvsn.rm.core.features.misc.company.relation.graph

import com.cpvsn.rm.core.features.misc.company.Company

data class CompanyGraphNode(
    val level: Int = 0,
    val company: Company,
    var customers: List<CompanyGraphNode>? = null,
    var customers_count: Int? = null,
    var suppliers: List<CompanyGraphNode>? = null,
    var suppliers_count: Int? = null,
    var distributes_for: List<CompanyGraphNode>? = null,
    var distributes_for_count: Int? = null,
    var distributed_by: List<CompanyGraphNode>? = null,
    var distributed_by_count: Int? = null,
    var competitors: List<CompanyGraphNode>? = null,
    var competitors_count: Int? = null,
    var parents: List<CompanyGraphNode>? = null,
    var parents_count: Int? = null,
    var subsidiaries: List<CompanyGraphNode>? = null,
    var subsidiaries_count: Int? = null,
    var other_related: List<CompanyGraphNode>? = null,
    var other_related_count: Int? = null,
)
