package com.cpvsn.rm.core.features.misc.company.relation.graph

import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.features.misc.company.Company
import com.cpvsn.rm.core.features.misc.company.relation.CompanyRelationSubType
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class CompanyRelationGraphService {

    private val CompanyRelationSubType.nodes_property
        get() = when (this) {
            CompanyRelationSubType.CUSTOMER -> CompanyGraphNode::customers
            CompanyRelationSubType.SUPPLIER -> CompanyGraphNode::suppliers
            CompanyRelationSubType.DISTRIBUTE_FOR -> CompanyGraphNode::distributes_for
            CompanyRelationSubType.DISTRIBUTE_BY -> CompanyGraphNode::distributed_by
            CompanyRelationSubType.COMPETITOR -> CompanyGraphNode::competitors
            CompanyRelationSubType.PARENT -> CompanyGraphNode::parents
            CompanyRelationSubType.SUBSIDIARY -> CompanyGraphNode::subsidiaries
            CompanyRelationSubType.OTHER -> CompanyGraphNode::other_related
        }

    private val CompanyRelationSubType.nodes_count_property
        get() = when (this) {
            CompanyRelationSubType.CUSTOMER -> CompanyGraphNode::customers_count
            CompanyRelationSubType.SUPPLIER -> CompanyGraphNode::suppliers_count
            CompanyRelationSubType.DISTRIBUTE_FOR -> CompanyGraphNode::distributes_for_count
            CompanyRelationSubType.DISTRIBUTE_BY -> CompanyGraphNode::distributed_by_count
            CompanyRelationSubType.COMPETITOR -> CompanyGraphNode::competitors_count
            CompanyRelationSubType.PARENT -> CompanyGraphNode::parents_count
            CompanyRelationSubType.SUBSIDIARY -> CompanyGraphNode::subsidiaries_count
            CompanyRelationSubType.OTHER -> CompanyGraphNode::other_related_count
        }

    @Autowired
    private lateinit var companyRepo: Company.Repo

    fun graph(
        company_id: Int,
        sub_type_in: Set<CompanyRelationSubType>,
        max_level: Int,
    ): CompanyGraphNode {
        return graph_recursive(
            setOf(Company.get(company_id)),
            sub_type_in,
            level = 1,
            max_level = max_level,
        ).first()
    }

    /**
     * this algo is meant to reduce DB query times
     * DB query times should be in proportion to the max_level
     */
    internal fun graph_recursive(
        companies: Collection<Company>,
        sub_type_in: Set<CompanyRelationSubType>,
        level: Int,
        max_level: Int,
    ): List<CompanyGraphNode> {
        if (companies.isEmpty()) return emptyList()
        // NOTE: here we deliberately fetch one more level
        // for Node.has_children property
        if (level > max_level + 1)
            return emptyList()

        val all_relations = companyRepo
            .fetch_relations(companies.ids(), sub_type_in)

        // Given a set of companies e.g. A, B, C, D
        // and a set of relations e.g. A->B, C->C, C->E, E->F, D->A
        // we need to know what are the next level companies
        // in the example case, it should be: A, B, C, E
        // Warn: prone to error
        // Remark1: Counterintuitively, we cannot just use all_relations.map { it.to } here.
        //   i.e. both relation.from and relation.to can point to the next level company
        val next_level_companies = companies
            .flatMap { company ->
                all_relations.mapNotNull { relation ->
                    when (company.id) {
                        relation.from_id -> {
                            // if this is null, it means relation.to doesn't point to the next level company
                            // e.g. at this level, we have nodes: A, B. relations: A -> C, B <- A
                            // even though we have a relation B <- A
                            // doesn't necessarily mean B is the next level node.
                            // the correct next level nodes are A, C.
                            relation.to
                        }
                        relation.to_id -> {
                            // if this is null, it means relation.from doesn't point to the next level company
                            relation.from
                        }
                        else -> null
                    }
                }
            }.distinctBy { it.id }

        // recursive call
        val sub_nodes = graph_recursive(
            next_level_companies,
            sub_type_in,
            level + 1,
            max_level,
        ).associateBy { it.company.id }

        return companies.map { company ->
            val res = CompanyGraphNode(
                level = level,
                company = company,
            )
            val relations_map = CompanyRelationSubType
                .map_relations(company.id, all_relations)
            relations_map
                .filterKeys { it in sub_type_in }
                .forEach { (sub_type, relations) ->
                    val related_companies = if (!sub_type.reverse) {
                        relations.mapNotNull { it.to }
                    } else {
                        relations.mapNotNull { it.from }
                    }
                    val related_nodes = related_companies
                        .mapNotNull { sub_nodes[it.id] }
                    sub_type.nodes_property.set(res, related_nodes.takeIf {
                        // NOTE: if we reached the level limit here
                        // this data merely tells us the size of the related nodes.
                        // we should not return this data to the caller.
                        // because we return empty list when the level exceeds max_level
                        // we won't fetch children's children anymore, their children will always be empty
                        // and their has_children prop will always be false, even they do have children
                        // i.e.
                        // related_nodes.size <= correct
                        // related_nodes.first().customers <= always be empty because level exceeds, return such data to caller can be misleading
                        // related_nodes.first().customers_count <= always be 0 because level exceeds, return such data to caller can be misleading
                        level < max_level
                    })
                    sub_type.nodes_count_property.set(res, related_nodes.size)
                }
            res
        }
    }
}
