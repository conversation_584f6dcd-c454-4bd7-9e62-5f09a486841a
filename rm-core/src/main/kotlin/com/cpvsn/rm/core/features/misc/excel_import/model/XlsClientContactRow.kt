package com.cpvsn.rm.core.features.misc.excel_import.model

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import org.springframework.stereotype.Repository

class XlsClientContactRow : RmEntity() {
    companion object : RmCompanion<XlsClientContactRow>()

    @Column
    var filename: String = ""
    @Column
    var file_url: String = ""
    @Column
    var sheet: String = ""
    @Column
    var row_num: Int = 0

    @Column
    var firstname: String = ""
    @Column
    var lastname: String = ""

    @Column
    var title: String = ""
    @Column
    var company: String = ""

    @Column
    var phone: String = ""
    @Column
    var email: String = ""

    @Column
    var location: String = ""
    @Column
    var city: String = ""
    @Column
    var country: String = ""
    @Column
    var notes: String = ""

    @Column
    var client_id: Int? = null
    @Column
    var location_id: Int? = null
    @Column
    var exist_client_contact_id: Int? = null
    @Column
    var imported_client_contact_id: Int? = null


    // 2023/12/4 added
    @Column
    var status: ClientContact.Status? = null

    @Column
    var email_2: String = ""

    @Column
    var linkedin_url: String = ""

    @Column
    var type: ClientContact.Type? = null

    //region +
    fun generateContactInfos(): List<ContactInfo> {
        val items = mutableListOf<ContactInfo>()
        if (email.isNotBlank() && email != "n/a") {
            items.add(ContactInfo().apply {
                this.type = ContactInfo.Type.EMAIL
                this.value = email
                this.is_main = true
            })
        }
        if (email_2.isNotBlank() && email_2 != "n/a") {
            items.add(ContactInfo().apply {
                this.type = ContactInfo.Type.EMAIL
                this.value = email_2
                this.is_main = email.isBlank()
            })
        }
        if (phone.isNotBlank() && phone != "n/a") {
            items.add(ContactInfo().apply {
                this.type = ContactInfo.Type.PHONE
                this.value = phone
                this.is_main = true
            })
        }
        if (linkedin_url.isNotBlank() && linkedin_url != "n/a") {
            items.add(ContactInfo().apply {
                this.type = ContactInfo.Type.LINKEDIN_URL
                this.value = linkedin_url
                this.is_main = true
            })
        }
        return items
    }

    @Relation(reference = "imported_client_contact_id")
    var imported_client_contact: ClientContact? = null

    val result_client_contact_id: Int?
        get() = exist_client_contact_id ?: imported_client_contact_id
    //endregion

    data class Query(
            @Criteria.Eq
            val id: Int? = null,
            @Criteria.IdsIn
            val ids: Set<Int>? = null,
            @Criteria.Lte
            val id_lte: Int? = null,
            @Criteria.Gte
            val id_gte: Int? = null,

            @Criteria.Eq
            val row_num: Int? = null,
            @Criteria.IdsIn
            val row_nums: Set<Int>? = null,
            @Criteria.Gte
            val row_num_gte: Int? = null,
            @Criteria.Lte
            val row_num_lte: Int? = null,

            @Criteria.Eq
            val sheet: String? = null,
            @Criteria.Eq("sheet")
            val client_name: String? = null,

            @Criteria.Eq
            val file_url: String? = null,
            @Criteria.Eq
            val filename: String? = null,

            @Criteria.IsNull
            val imported_client_contact_id_is_null: Boolean? = null,
    ) : BaseQuery<XlsClientContactRow>()

    @Repository
    class Repo : RmBaseRepository<XlsClientContactRow>(), JdbcEntityBatchRepo<Int, XlsClientContactRow> {
        override val batchDao: JdbcEntityBatchDao<Int, XlsClientContactRow> by lazy {
            JdbcEntityBatchDao(XlsClientContactRow::class, dataSource)
        }
    }
}