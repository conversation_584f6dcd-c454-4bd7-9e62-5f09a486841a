package com.cpvsn.rm.core.features.misc.file

import com.cpvsn.core.base.ValueEnum
import com.cpvsn.core.base.entity.BaseCompanion
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.TimeAuditingEntity
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.PatchFields
import com.cpvsn.crud.orm.annotation.Table
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.model.MaybeHasNdbID
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.fasterxml.jackson.annotation.JsonIgnore
import org.springframework.stereotype.Repository
import java.time.Instant

@Table("file")
class RmFile : TimeAuditingEntity(), UserAuditing, SoftDeletable, MaybeHasNdbID {
    companion object : BaseCompanion<RmFile>()

    @Column
    override var ndb_id: Int? = null

    @Column
    var name: String = ""

    @Column
    var path: String? = null

    /**
     * for migration
     */
    @Column
    var cndb_path: String? = null

    @Column
    var size: Int? = null

    @Column
    var media_type: String? = null

    @Column
    var content_type: String? = null

    @get:JsonIgnore
    @PatchFields(["content_type"])
    var content_type_enum: ContentType? by PropDelegates.value_enum_nullable(this::content_type)

    @Column
    override var create_by_id: Int = 0

    // we don't persist this property
    override var update_by_id: Int = 0

    @Column(insertable = false, updatable = false)
    override var delete_at: Instant? = null

    enum class ContentType(
            val upload_dir: String?
    ) : ValueEnum<String> {
        TASK_CA("ca_respond"),
        CONTRACT("contract file")
        ;

        override val value: String
            get() = this.name

        companion object : ValueEnum.ValueEnumCompanion<String, ContentType>()
    }

    data class Query(
            @Criteria.Eq
            val id: Int? = null,
            @Criteria.IdsIn
            val ids: Set<Int>? = null,
            @Criteria.Eq
            val ndb_id: Int? = null,
            @Criteria.IdsIn
            val ndb_ids: Set<Int>? = null,
    ) : BaseQuery<RmFile>()

    @Repository
    class Repo : RmBaseRepository<RmFile>(), JdbcEntityBatchRepo<Int, RmFile> {
        override val batchDao: JdbcEntityBatchDao<Int, RmFile> by lazy {
            JdbcEntityBatchDao(RmFile::class, dataSource)
        }
    }

}
