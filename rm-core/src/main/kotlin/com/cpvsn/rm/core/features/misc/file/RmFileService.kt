package com.cpvsn.rm.core.features.misc.file

import com.cpvsn.core.util.RandomUtil
import com.cpvsn.core.util.UrlEncoder
import com.cpvsn.rm.core.core.storage.StorageService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.nio.file.Paths
import java.util.*

@Service
class RmFileService {

    @Autowired
    private lateinit var storageService: StorageService

    fun create(
            file: MultipartFile,
            file_name: String? = null,
            dir: String? = null,
            content_type: String? = null,
    ): RmFile {
        val f = file_name
                ?: file.originalFilename
                ?: file.name
                ?: RandomUtil.random_alphabetic_str(8)
        val d = dir
                ?: content_type?.let { RmFile.ContentType.getOrNull(it)?.upload_dir }.orEmpty()
        val extension = get_file_extension(f)
        val path = Paths.get(d, UUID.randomUUID().toString() + extension)
        val respond_url = storageService.save(file.inputStream, path.toString())
        val encoded_file_name = UrlEncoder.quote(f)
        val url = "$respond_url?rename=$encoded_file_name"

        val rm_file = RmFile {
            this.name = f
            this.path = url
            this.size = file.size.toInt()
            this.media_type = file.contentType
            this.content_type = content_type
        }

        return RmFile.save(rm_file)
    }

    fun get_file_extension(
        file_name: String
    ): String {
        val lastDotIndex = file_name.lastIndexOf('.')
        return if (lastDotIndex != -1 && lastDotIndex != file_name.length - 1) {
            file_name.substring(lastDotIndex)
        } else {
            ""
        }
    }

}
