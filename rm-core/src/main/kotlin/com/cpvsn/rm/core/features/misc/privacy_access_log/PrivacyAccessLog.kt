package com.cpvsn.rm.core.features.misc.privacy_access_log

import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.auth.GeneralUserRole

class PrivacyAccessLog : RmEntity() {
    companion object : RmCompanion<PrivacyAccessLog>()

    // access who
    @Column
    var owner_id: Int = 0

    @Column
    var owner_type: GeneralUserRole = GeneralUserRole.ADVISOR

    // who access
    @Column
    var user_id: Int = 0

    @Column
    var type: Type = Type.CONTACT_INFO

    var privacy_info: Any? = null

    enum class Type(val description: String) {
        ID_NO("id no"),
        BANK_ACCOUNT("bank account"),

        /**
         * it actually means AdvisorPaymentForm instead of PaymentForm
         *
         * @see AdvisorPaymentForm
         * @see PaymentForm
         */
        PAYMENT_FORM("payment form"),
        CONTACT_INFO("contact info"),
        ;
    }
}
