package com.cpvsn.rm.core.features.oauth2

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.features.client.ClientPreference
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.client.contact.ClientContactService
import com.cpvsn.rm.core.features.oauth2.entity.OAuth2Authorization
import com.cpvsn.rm.core.features.oauth2.pojo.ClientConfAudioAssetRecord
import com.cpvsn.rm.core.features.oauth2.pojo.OAuth2AuthorizationException
import com.cpvsn.rm.core.features.oauth2.pojo.OAuth2ResponseStatus
import com.cpvsn.rm.core.features.oauth2.config.ThirdPartyClient
import com.cpvsn.rm.core.features.oauth2.entity.OAuth2ThirdPartyAccessLog
import com.cpvsn.rm.core.features.oauth2.pojo.ThirdPartyApiEnum
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.arrange.TaskArrangement
import com.cpvsn.rm.core.features.task.arrange.enums.ArrangementType
import com.cpvsn.rm.core.features.task.constant.BridgeType
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.task.constant.TaskType
import com.cpvsn.rm.core.features.twiliosdk.entity.TwilioAsset
import com.cpvsn.rm.core.features.twiliosdk.entity.TwilioVoiceConference
import com.cpvsn.rm.core.features.twiliosdk.entity.TwilioVoiceConferenceInstance
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomAsset
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomMeeting
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomMeetingInstance
import com.cpvsn.rm.core.util.PageUtil
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import kotlin.math.ceil

@Service
class Provide3rdPartyService {

    //region @
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var clientContactService: ClientContactService
    //endregion


    companion object {
        const val PAGE_SIZE = 10
        val sidGenerator = SidGenerator(key_16_bit_utf8 = "capvision1234987".toByteArray(Charsets.UTF_8))
    }

    fun log_access(
        userAuth: OAuth2Authorization,
        api: ThirdPartyApiEnum,
        resource: String
    ): OAuth2ThirdPartyAccessLog {
        val log = OAuth2ThirdPartyAccessLog {
            this.authorization_id = userAuth.id
            this.api_enum = api
            this.resource = resource
        }
        return OAuth2ThirdPartyAccessLog.save(log)
    }


    //region helpers
    fun get_client_contact(
        userAuth: OAuth2Authorization
    ): ClientContact {
        if (userAuth.third_party_client != ThirdPartyClient.COLOOP && userAuth.user_id.isBlank()) {
            throw OAuth2AuthorizationException(OAuth2ResponseStatus.invalid_client_credential)
        }
        // For CoLoop, user_id refers to client contact's email
        val client_contact =
            userAuth.client_contact_id?.let { ClientContact.find(it) }
                ?: clientContactService.find_client_contact_by_email(userAuth.user_id)
                ?: throw OAuth2AuthorizationException(OAuth2ResponseStatus.unknown_user)

        return client_contact
    }


    /**
     * 相当于一张虚拟的asset list,方便汇总
     * 注意一个task可能对应多条asset记录
     */
    fun list_client_audio_asset_records(
        client_id: Int,
        project_id: Int? = null
    ): List<ClientConfAudioAssetRecord> {
        // 已经completed的、使用twilio(有录音)的访谈
        val tasks = Task.findAll(
            Task.Query(
                client_id = client_id,
                project_id = project_id,
                type = TaskType.CONSULTATION,
                general_status_in = setOf(TaskStatus.General.ARRANGED, TaskStatus.General.COMPLETED),
                arrangement = TaskArrangement.Query(
                    type = ArrangementType.COMMON,
                    bridge_type_in = setOf(BridgeType.TWILIO, BridgeType.ZOOM)
                )
            ),
            include = Includes.setOf(
                Task::angle,
                Task::project,
                Task::arrangement dot TaskArrangement::twilio_voice_conference,
                Task::arrangement dot TaskArrangement::zoom_meeting
            )
        )
        val map_task = tasks.associateBy { it.id }

        // *************************************************************************************************************
        /**
         * twilio
         */
        // 以上tasks的twilio会议
        val map_taskid_twilio_conference = tasks.mapNotNull { it.arrangement }
            .associateBy(
                keySelector = { it.task_id },
                valueTransform = { it.twilio_voice_conference }
            )
            .filter { it.value != null }
            .let {
                @Suppress("UNCHECKED_CAST")
                it as Map<Int, TwilioVoiceConference>
            }

        // 相关的会议instances
        // 限定参会者超过两人的会议instance (排除单人误进产生的instance)
        val twilio_conference_instances = TwilioVoiceConferenceInstance.findAll(
            TwilioVoiceConferenceInstance.Query(
                twilio_voice_conference_ids = map_taskid_twilio_conference.values.ids(TwilioVoiceConference::id)
            )
        ).filter {
            it.participants_count > 1
        }

        val client_twilio_assets = TwilioAsset.findAll(
            TwilioAsset.Query(
                conference_sids = twilio_conference_instances.ids(TwilioVoiceConferenceInstance::conference_sid)
            )
        ).filter {
            it.capvision_file_url.isNotBlank()
        }.groupBy { it.conference_sid }

        val twilio_records = client_twilio_assets.mapNotNull { (conference_sid, assets) ->
            val conference_instance = twilio_conference_instances.first {
                it.conference_sid == conference_sid
            }
            val twilio_conference_id = conference_instance.twilio_voice_conference_id
            val entry = map_taskid_twilio_conference.entries.first {
                it.value.id == twilio_conference_id
            }
            val task_id = entry.key
            val task = map_task[task_id]!!
            val audio_asset = assets.firstOrNull {
                it.type == TwilioAsset.Type.VOICE_CONFERENCE_RECORDING
            } ?: return@mapNotNull null

            // instanize
            val record = ClientConfAudioAssetRecord().apply {
                this.project_id = task.project_id
                this.angle_id = task.angle_id ?: 0
                this.task_id = task.id
                this.bridge = BridgeType.TWILIO
                this.audio_asset_id = audio_asset.id
                this.audio_file_url = audio_asset.capvision_file_url
                this.transcription_asset_id = assets.firstOrNull {
                    it.type == TwilioAsset.Type.VOICE_CONFERENCE_RECORDING
                }?.id

                // nested
                this.task = task
                this.angle = task.angle
            }
            record


        }
        // *************************************************************************************************************
        /**
         * zoom
         */
        val map_taskid_zoom_meeting = tasks.mapNotNull { it.arrangement }
            .associateBy(
                keySelector = { it.task_id },
                valueTransform = { it.zoom_meeting }
            )
            .filter { it.value != null }
            .let {
                @Suppress("UNCHECKED_CAST")
                it as Map<Int, ZoomMeeting>
            }

        // 限定参会者超过两人的会议instance (排除单人误进产生的instance)
        val zoom_meeting_instances = ZoomMeetingInstance.findAll(
            ZoomMeetingInstance.Query(
                meeting_entity_ids = map_taskid_zoom_meeting.values.ids()
            )
        ).filter {
            it.participants_count > 1
        }

        val client_zoom_assets = ZoomAsset.findAll(
            ZoomAsset.Query(
                meeting_uuid_in = zoom_meeting_instances.ids(ZoomMeetingInstance::meeting_uuid)
            )
        ).filter {
            it.capvision_file_url.isNotBlank()
        }.groupBy { it.meeting_uuid }

        val zoom_records = client_zoom_assets.mapNotNull { (_, assets) ->
            val entry = map_taskid_zoom_meeting.entries.first {
                it.value.meeting_id == assets.first().meeting_id
            }
            val task_id = entry.key
            val task = map_task[task_id]!!
            val audio_asset = assets.firstOrNull {
                it.file_type == ZoomAsset.FileType.M4A
            } ?: return@mapNotNull null

            // instanize
            val record = ClientConfAudioAssetRecord().apply {
                this.project_id = task.project_id
                this.angle_id = task.angle_id ?: 0
                this.task_id = task.id
                this.bridge = BridgeType.ZOOM
                this.audio_asset_id = audio_asset.id
                this.audio_file_url = audio_asset.capvision_file_url
                this.transcription_asset_id = assets.firstOrNull {
                    it.file_type == ZoomAsset.FileType.TRANSCRIPT
                }?.id

                // nested
                this.task = task
                this.angle = task.angle
            }

            record
        }

        // *************************************************************************************************************

        val res = (twilio_records + zoom_records).sortedWith(
            compareByDescending<ClientConfAudioAssetRecord> { it.project_id }
                .thenByDescending { it.task_id }
                .thenBy { it.audio_asset_id }
        )

        // add sequence: 部分task可能对应多条asset, 那么给asset加上sequence
        val multi_asset_records = res.groupBy { it.task_id }
            .filterValues { it.size > 1 }
            .mapValues {
                it.value.forEachIndexed { index, clientConfAudioAssetRecord ->
                    clientConfAudioAssetRecord.sequence = index + 1
                }
            }

        return res
    }

    //endregion

    fun getUserProfile(
        userAuth: OAuth2Authorization
    ): Provide3rdParty.ClientUserProfile {
        val client_contact = get_client_contact(userAuth)
        val res = Provide3rdParty.ClientUserProfile(
            name = client_contact.name
        )
        return res
    }

    fun listProjects(
        userAuth: OAuth2Authorization,
        request: Provide3rdParty.ListProjectRequest
    ): Provide3rdParty.ListProjectResponse {
        val client_contact = get_client_contact(userAuth)

        val list_client_all_records = list_client_audio_asset_records(
            client_id = client_contact.client_id,
            project_id = null
        )


        // pagination
        val entry_ids = list_client_all_records.map { it.project_id }.distinct()
        val request_page_num = when {
            request.page > 0 -> request.page
            request.page_token.isNotBlank() -> sidGenerator.sid_to_intid(request.page_token)
            else -> 1
        }
        val current_page = PageUtil.page(
            list = entry_ids,
            page = request_page_num,
            size = PAGE_SIZE
        )
        val page_count = ceil(current_page.totalItems.toDouble() / PAGE_SIZE).toInt()
        val current_page_entry_ids = current_page.items


        // response
        val map_project = Project.findAll(
            Project.Query(ids = current_page_entry_ids.toSet())
        ).associateBy { it.id }


        val items = current_page_entry_ids.map { projectid ->
            val project = map_project[projectid]!!
            val angles = list_client_all_records.filter { it.project_id == project.id }
                .mapNotNull { it.angle }.distinctBy { it.id }

            val vo = Provide3rdParty.Project(
                project_id = project.id,
                project_sid = sidGenerator.intid_to_sid(project.id),
                name = project.name.orEmpty(),
                topic = "",
                angles = angles.map {
                    Provide3rdParty.Angle(
                        angle_id = it.id,
                        angle_sid = sidGenerator.intid_to_sid(it.id),
                        name = it.name,
                        topic = it.topic.orEmpty()
                    )
                }
            )
            vo
        }

        val response = Provide3rdParty.ListProjectResponse(
            projects = items,
            page_meta = Provide3rdParty.PageMeta(
                page = request_page_num,
                page_count = page_count,
                page_size = PAGE_SIZE,
                total_size = current_page.totalItems.toInt(),

                next_page_token = sidGenerator.intid_to_sid(request_page_num + 1),
                previous_page_token = if (request_page_num != 1) sidGenerator.intid_to_sid(request_page_num - 1) else ""

            )
        )
        return response
    }

    fun listProjectResources(
        userAuth: OAuth2Authorization,
        request: Provide3rdParty.ProjectResourcesRequest
    ): Provide3rdParty.ProjectResourcesResponse {
        val client_contact = get_client_contact(userAuth)

        val project_id = sidGenerator.sid_to_intid(request.project_sid)
        val angle_id = if (request.angle_sid.isNotBlank()) {
            sidGenerator.sid_to_intid(request.angle_sid)
        } else {
            null
        }

        var list_project_all_records = list_client_audio_asset_records(
            client_id = client_contact.client_id,
            project_id = project_id
        )

        val map_record = list_project_all_records.associateBy { it.virtual_distinct_id }

        if (angle_id != null) {
            list_project_all_records = list_project_all_records.filter {
                it.angle_id == angle_id
            }
        }

        // pagnation
        val entry_ids = list_project_all_records.map { it.virtual_distinct_id }.distinct()
        val request_page_num = when {
            request.page > 0 -> request.page
            request.page_token.isNotBlank() -> sidGenerator.sid_to_intid(request.page_token)
            else -> 1
        }
        val current_page = PageUtil.page(
            list = entry_ids,
            page = request_page_num,
            size = PAGE_SIZE
        )
        val page_count = ceil(current_page.totalItems.toDouble() / PAGE_SIZE).toInt()
        val current_page_entry_ids = current_page.items


        // response
        val map_task = Task.findAll(
            Task.Query(
                ids = list_project_all_records.filter { it.virtual_distinct_id in current_page_entry_ids }
                    .map { it.task_id }.toSet()
            ),
            include = Includes.setOf(Task::advisor)
        ).associateBy { it.id }

        // name display
        val client_preference = ClientPreference.firstOrNull(
            ClientPreference.Query(client_id = client_contact.client_id)
        )
        val blind_expert_name = client_preference?.blind_expert_names_in_to_client_and_portal == true

        val items = current_page_entry_ids.mapNotNull { virtual_id ->
            val record = map_record[virtual_id] ?: return@mapNotNull null
            val task = map_task[record.task_id] ?: return@mapNotNull null
            val expert = if (blind_expert_name) {
                "Expert ${task.display_id}"
            } else {
                task.advisor?.full_name
            }

            val vo = Provide3rdParty.ProjectResource(
                task_id = record.task_id,
                resource_id = record.audio_asset_id,
                bridge = record.bridge,
                resource_sid = sidGenerator.intid_to_sid(record.audio_asset_id),
                title = if (record.sequence == null) {
                    "Interview with $expert"
                } else {
                    "Interview with $expert - Part ${record.sequence}"
                },
                file_url = record.audio_file_url,
                type = Provide3rdParty.ProjectResourceType.audio,
                task_time = task.start_time!!
            )
            vo
        }

        val res = Provide3rdParty.ProjectResourcesResponse(
            resources = items,
            page_meta = Provide3rdParty.PageMeta(
                page = request_page_num,
                page_count = page_count,
                page_size = PAGE_SIZE,
                total_size = current_page.totalItems.toInt(),

                next_page_token = sidGenerator.intid_to_sid(request_page_num + 1),
                previous_page_token = if (request_page_num != 1) sidGenerator.intid_to_sid(request_page_num - 1) else "",
            )
        )
        return res
    }
}