package com.cpvsn.rm.core.features.oauth2.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component


@Component
@ConfigurationProperties(prefix = "oauth2-server")
class OAuth2ServerConfig {

    class ClientConfig {
        var client_enum: ThirdPartyClient = ThirdPartyClient.COLOOP
        var client_id: String = ""
        var client_secret: String = ""
    }

    var clients: List<ClientConfig> = emptyList()

}