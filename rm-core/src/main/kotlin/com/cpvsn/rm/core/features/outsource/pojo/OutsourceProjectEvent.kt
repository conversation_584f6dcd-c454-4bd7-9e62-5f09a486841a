package com.cpvsn.rm.core.features.outsource.pojo

import com.cpvsn.bridge.sdk.model.ProjectEventType
import com.cpvsn.core.base.entity.BaseCompanion
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.ColumnDefinition
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.bridge.Region

class OutsourceProjectEvent : RmEntity() {

    companion object : BaseCompanion<OutsourceProjectEvent>()

    @Column
    var project_approval_id: Int? = null

    @Column
    var event_type: ProjectEventType? = null

    @Column
    var message: String? = null

    @Column
    @ColumnDefinition(type = "varchar(100)")
    var operator_name: String? = null

    @Column
    var operator_email: String? = null

    @Column
    var from_region: Region? = null

    @Column
    var target_region: Region? = null

    val message_source: Region?
        get() = this.from_region


    data class Query(
        @Criteria.Eq
        val project_approval_id: String? = null,
    ) : BaseQuery<OutsourceProjectEvent>()


}