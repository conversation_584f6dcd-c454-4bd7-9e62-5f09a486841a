package com.cpvsn.rm.core.features.portal

import com.cpvsn.rm.core.annotation.DocConstant
import com.cpvsn.rm.core.config.PortalProperties
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.portal.common.payload.*
import com.cpvsn.rm.core.util.un_support
import kotlin.reflect.KClass

@DocConstant
enum class PortalType(
    private val distinguish_char: String = "",
    /**
     * decide if a token is still valid after it has been consumed
     */
    val only_consume_once: Boolean = false,
) {
    ADVISOR_PRE_CALL("AP1"), // pre call
    ADVISOR_POST_CALL("AP2"),

    /**
     * 第一版的 client portal
     * 联系人可以在 call 前/后 登录 portal 执行指定操作
     * rm-portal module
     */
    CONTACT_PRE_CALL("CP1"),

    /**
     * 第二版的 client portal
     * client-portal module
     *
     * https://www.notion.so/Client-Portal-one-click-authorization-and-Account-User-setting-17fcd2d7a2924a0f94732c05e4a31c03
     */
    CONTACT_ADVISOR_RECOMMENDATION("CAR"),
    CONTACT_POST_CALL("CP2"),

    CLIENT_COMPLIANCE("CC"),

    /**
     * direct sign-in via https://compliance2.capvision.com/client-portal/#/email-login
     */
    CLIENT_ASSET_ACCESS_SIGNIN("CAAS"),

    /**
     * 联系人注册, 在完成第一步填邮箱以后,
     * 发送执行第二步注册的链接至该邮箱.
     *
     * com.cpvsn.rm.clientportal.auth.ClientPortalUserAuthService#register_step2
     */
    CLIENT_USER_REGISTER("CUR", only_consume_once = true),

    RESET_PWD("RP", only_consume_once = true),

    TASK_CONFERENCE_DIAL_CONFIRMATION("TCDC"),
    TASK_CONFERENCE_ASSET("TCA"),
    TASK_ADVISOR_RECORD_CONSENT("TARC"),

    /**
     * collect AdvisorPaymentForm information
     * for historical reason, this type is called 'ADVISOR_BANK_ACCOUNT'
     */
    ADVISOR_BANK_ACCOUNT("ABA"),

    ADVISOR_UNSUBSCRIBE_OUTREACH("AUO"),

    ADVISOR_THIRD_PARTY_SURVEY("ATPS"),
    ADVISOR_DECLINE("ADEC"),
    ADVISOR_PAYMENT_CONFIRM("APC"),

    ADVISOR_W9_FORM_COLLECT("AW9F"),
    ;

    val corresponding_email_content_type: EmailContentType?
        get() = when (this) {
            ADVISOR_PRE_CALL -> EmailContentType.PORTAL_ADVISOR_PRE_CALL
            ADVISOR_POST_CALL -> EmailContentType.PORTAL_ADVISOR_POST_CALL
            CLIENT_COMPLIANCE -> EmailContentType.PORTAL_COMPLIANCE
            CLIENT_USER_REGISTER -> EmailContentType.PORTAL_CLIENT_USER_REGISTER
            CONTACT_PRE_CALL -> EmailContentType.PORTAL_CONTACT_PRE_CALL
            CONTACT_POST_CALL -> EmailContentType.PORTAL_CONTACT_POST_CALL
            RESET_PWD -> EmailContentType.PORTAL_RESET_PWD
            TASK_CONFERENCE_DIAL_CONFIRMATION -> null
            TASK_CONFERENCE_ASSET -> EmailContentType.CONFERENCE_ASSET_NEW_RECORDING_AVAILABLE
            TASK_ADVISOR_RECORD_CONSENT -> EmailContentType.ADVISOR_RECORD_CONSENT


            ADVISOR_BANK_ACCOUNT -> EmailContentType.PORTAL_ADVISOR_BANK_ACCOUNT

            ADVISOR_UNSUBSCRIBE_OUTREACH -> EmailContentType.PROJECT_OUTREACH
            CONTACT_ADVISOR_RECOMMENDATION -> EmailContentType.ADVISOR_RECOMMENDATION
            ADVISOR_THIRD_PARTY_SURVEY -> EmailContentType.PROJECT_OUTREACH
            ADVISOR_DECLINE -> null
            ADVISOR_PAYMENT_CONFIRM -> null
            CLIENT_ASSET_ACCESS_SIGNIN -> null

            PortalType.ADVISOR_W9_FORM_COLLECT -> EmailContentType.W9_FORM_COLLECT
        }

    val token_placeholder: String
        get() = "{{{$distinguish_char}}}"

    /**
     * this is used when a portal has default email template
     * i.e. we can generate a default email from portal
     */
    val default_email_content_type: EmailContentType?
        get() = when (this) {
            CLIENT_USER_REGISTER -> EmailContentType.PORTAL_CLIENT_USER_REGISTER
            RESET_PWD -> EmailContentType.PORTAL_RESET_PWD
            TASK_CONFERENCE_DIAL_CONFIRMATION -> null
            TASK_CONFERENCE_ASSET -> EmailContentType.CONFERENCE_ASSET_NEW_RECORDING_AVAILABLE
            TASK_ADVISOR_RECORD_CONSENT -> EmailContentType.ADVISOR_RECORD_CONSENT
            ADVISOR_BANK_ACCOUNT -> EmailContentType.PORTAL_ADVISOR_BANK_ACCOUNT
            ADVISOR_PRE_CALL -> null
            ADVISOR_POST_CALL -> null
            CLIENT_COMPLIANCE -> null
            CONTACT_PRE_CALL -> null
            CONTACT_POST_CALL -> null
            ADVISOR_UNSUBSCRIBE_OUTREACH -> EmailContentType.ADVISOR_UNSUBSCRIBE_OUTREACH_FRAGMENT
            CONTACT_ADVISOR_RECOMMENDATION -> null
            ADVISOR_THIRD_PARTY_SURVEY -> null
            ADVISOR_DECLINE -> null
            ADVISOR_PAYMENT_CONFIRM -> null
            CLIENT_ASSET_ACCESS_SIGNIN -> null
            ADVISOR_W9_FORM_COLLECT -> EmailContentType.W9_FORM_COLLECT
        }

    val payload_class: KClass<out PortalPayload>?
        get() = when (this) {
            CLIENT_USER_REGISTER, RESET_PWD -> GeneralUserPortalPayload.SimpleGeneralUserPayload::class
            ADVISOR_BANK_ACCOUNT -> AdvisorPaymentFormConfirmPayload::class
            TASK_CONFERENCE_DIAL_CONFIRMATION -> TaskConferenceDialPayload::class
            TASK_CONFERENCE_ASSET -> TaskConferenceAssetPayload::class
            TASK_ADVISOR_RECORD_CONSENT -> null
            CONTACT_ADVISOR_RECOMMENDATION -> AdvisorRecommendationPayload::class
            ADVISOR_UNSUBSCRIBE_OUTREACH -> AdvisorUnsubscribeOutreachPayload::class
            ADVISOR_THIRD_PARTY_SURVEY -> Advisor3rdPartySurveyPayload::class
            ADVISOR_PAYMENT_CONFIRM -> AdvisorPaymentConfirmPayload::class
            ADVISOR_DECLINE -> null
            ADVISOR_PRE_CALL,
            ADVISOR_POST_CALL,
            CONTACT_PRE_CALL,
            CONTACT_POST_CALL,
            CLIENT_COMPLIANCE -> un_support()
            CLIENT_ASSET_ACCESS_SIGNIN -> ClientAssetAccessSigninPayload::class
            ADVISOR_W9_FORM_COLLECT -> null
        }

    /**
     * add prefix to random token
     * so that we can kinda distinguish portal type by token string
     */
    fun add_prefix(token: String): String {
        return "$distinguish_char$token"
    }

    /**
     * detect if a token can be a valid token of this portal type
     * reduce db hit times
     */
    fun can_be_valid(
        properties: PortalProperties,
        token: String
    ): Boolean {
        return token.startsWith(distinguish_char)
    }

    /**
     * unit: minutes
     */
    fun get_default_token_duration(properties: PortalProperties): Long? {
        return when (this) {
            ADVISOR_PRE_CALL -> {
                // https://www.notion.so/capvision/T-C-Links-Remove-expiration-244f02e78e52478d91cf824c821d8bfd
                60 * 24 * 30
            }
            ADVISOR_POST_CALL -> {
                // "payment. confirm 的link的有效期你也调到30天" --ping
                60 * 24 * 30
            }
            CONTACT_PRE_CALL -> properties.contactPortalTokenDuration
            CONTACT_POST_CALL -> properties.contactPortalTokenDuration
            CLIENT_COMPLIANCE -> null
            CLIENT_USER_REGISTER -> properties.clientUserRegisterTokenDuration
            RESET_PWD -> properties.resetPwdTokenDuration
            TASK_CONFERENCE_DIAL_CONFIRMATION -> null
            TASK_CONFERENCE_ASSET -> null
            TASK_ADVISOR_RECORD_CONSENT -> null
            ADVISOR_BANK_ACCOUNT -> properties.advisorBankAccountTokenDuration
            ADVISOR_UNSUBSCRIBE_OUTREACH -> {
                // "The unsubscribe link should no expire(as per data protection rules recipients have to be able to unsubscribe whenever"
                // -- Jasper Grothues, Monday, August 7, 2023 10:49 AM
                null
            }
            CONTACT_ADVISOR_RECOMMENDATION -> properties.contactPortalTokenDuration
            ADVISOR_THIRD_PARTY_SURVEY -> {
                // "这种survey类型的链接的有效期也调整为30天吧"  --ping
                60 * 24 * 30
            }
            ADVISOR_DECLINE -> {
                // "Adjust the decline project link to the same as T&Cs (hopefully we can do 90 days for both)"
                // -- Jasper Grothues, Monday, August 7, 2023 10:49 AM
                // After negotiation, we decide use 30 days
                60 * 24 * 30
            }
            ADVISOR_PAYMENT_CONFIRM -> {
                // "payment. confirm 的link的有效期你也调到30天" --ping
                60 * 24 * 30
            }
            CLIENT_ASSET_ACCESS_SIGNIN -> {
                60 * 24 * 30
            }
            ADVISOR_W9_FORM_COLLECT -> null
        }
    }
}
