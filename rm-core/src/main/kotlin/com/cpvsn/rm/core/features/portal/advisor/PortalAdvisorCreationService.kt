package com.cpvsn.rm.core.features.portal.advisor

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.config.PortalProperties
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.compliance.tc.advisor.AdvisorTcService
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.ca.TaskCa
import com.cpvsn.rm.core.features.task.ca.TaskCaType
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import com.cpvsn.rm.core.util.RandomUtil
import com.cpvsn.rm.core.util.biz_error
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate
import java.time.Instant
import java.time.temporal.ChronoUnit

@Service
class PortalAdvisorCreationService {

    private val logger =
        LoggerFactory.getLogger(this::class.java)

    //region @
    @Autowired
    private lateinit var taskEventService: TaskEventService

    @Autowired
    private lateinit var advisorTcService: AdvisorTcService

    @Autowired
    private lateinit var portalProperties: PortalProperties

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate
    //endregion

    //region create
    fun process_email_request(
        email: EmailRequestPojo,
        portal: PortalAdvisor,
    ): Pair<EmailRequestPojo, PortalAdvisor> {
        val p = create(portal)
        val mail = get_email(p, email)
        return mail to p
    }

    fun create(
        portal: PortalAdvisor,
        trigger_task_events: Boolean = !portal.is_public_portal
    ): PortalAdvisor {
        return transactionTemplate.extExecute {
            val task = portal.task_id.takeIf { it.is_valid_id }?.let {
                Task.find(
                    it, include = setOf(
                        Task::advisor.name,
                        Task::project.name
                    )
                )
            }
            if (task == null) {
                portal.expect_steps.forEach {
                    if (it.require_task_context)
                        biz_error("Step: '$it' need to be executed in task context")
                }
            }
            portal.task = task
            portal.advisor_id = task?.advisor_id
                ?: portal.advisor_id // if task exist, we use task.advisor_id
            portal.project_id = task?.project_id ?: portal.project_id
            portal.advisor = task?.advisor

            create_advisor_tc(portal)
            create_task_ca(portal)

            if (TaskEventType.SQ_SEND in portal.trigger_events) {
                // 有sq_id,代表发的是"Oneoff questions", 则在原有基础上复制一份创建新的instance来回答, 旧的需要作为历史保存
                if (portal.sq_id.is_valid_id) {

                    create_one_off_sq_instance(portal)

                } else {
                    create_sq_instance(portal)
                }
            }

            // save portal
            PortalAdvisor.save(portal) // get auto increment id
            Patch.fromMutator(portal) {
                token = portal.type.add_prefix(
                    RandomUtil.random_code(
                        portal.id,
                        portalProperties.advisorPortalTokenLength
                    )
                )
                val duration =
                    portal.type.get_default_token_duration(portalProperties)
                duration?.let {
                    token_expire_at = Instant.now().plus(it, ChronoUnit.MINUTES)
                }
            }.patch()

            if (trigger_task_events) {
                trigger_task_events(portal)
            }

            // if the portal has no relation with a project, the related task will be null
            if (task != null) {
                Patch.fromMutator(task) {
                    latest_task_communication_time = portal.create_at
                }.patch()
                // as we've changed task status, here we refresh the task
                portal.task = Task.get(task.id, include = setOf(Task::events.name))
            }

            PortalAdvisor.join(portal, Includes.setOf(PortalAdvisor::link_jit))
        }
    }

    private fun create_advisor_tc(portal: PortalAdvisor) {
        val advisor_tc = portal.advisor_tc ?: return
        advisor_tc.advisor_id = portal.advisor_id.assert_valid_id()
        val res = advisorTcService.create(
            tc_id = advisor_tc.tc_id.assert_valid_id(),
            advisor_tc
        )
        Patch.fromMap(
            advisor_tc.advisor_id, mapOf(
                Advisor::tc_status to Advisor.TcStatus.SENT
            )
        ).patch()
        InvokeUtil.trigger(Event.ADVISOR_DOC_CHANGED(advisor_id = advisor_tc.advisor_id))

        portal.advisor_tc_id = res.id
    }

    private fun create_task_ca(portal: PortalAdvisor) {
        val task_ca = portal.task_ca ?: return

        val ca_type = if (portal.type == PortalType.ADVISOR_PRE_CALL)
            TaskCaType.PRE_CALL
        else
            TaskCaType.POST_CALL
        task_ca.task_id = portal.task_id!!
        task_ca.type = ca_type
        TaskCa.save(task_ca)

        portal.task_ca_id = task_ca.id
    }

    private fun create_sq_instance(portal: PortalAdvisor) {
        if (TaskEventType.SQ_SEND !in portal.trigger_events) return
        val sq_branch_id = portal.sq_branch_id ?: return
        val sq = InquiryInstance().apply {
            branch_id = sq_branch_id
            create_type = InquiryInstance.CreateType.BEFORE_TASK
            source_id = portal.task_id!!
            receiver_id = portal.advisor_id
        }
        sq.save()
        portal.sq_id = sq.id
    }

    /**
     * comments of this requirement
     * https://www.notion.so/capvision/Projects-Bulk-adding-of-screener-workflow-18b23199f41a80e5b9f2eb4d0965cbc6?d=19123199f41a80948a34001cb306cc3f
     */
    private fun create_one_off_sq_instance(
        portal: PortalAdvisor
    ) {
        if (TaskEventType.SQ_SEND !in portal.trigger_events) return
        val one_off_sq_instance = portal.sq_id?.let {
            InquiryInstance.find(it)
        } ?: biz_error("one off sq instance not found")
        // copy the old instance since we need a new instance for advisor to response and the old one as a history record
        val new_sq_instance = InquiryInstance.save(
            CoreJsonUtil.parse<InquiryInstance>(CoreJsonUtil.stringify(one_off_sq_instance.apply {
                this.status = InquiryInstance.Status.ASSIGNED
            }))
        )
        // the rewritten save method avoided the one off sq id
        Patch.fromMutator(new_sq_instance) {
            this.qa_list_snapshot = one_off_sq_instance.qa_list_snapshot
        }.patchThenGet()
        //we remove the one off sq of the old instance
        val filtered_qa_list = one_off_sq_instance.qa_list_snapshot.filter {
            it.answer != null
        }.sortedBy { it.sort_order }
        filtered_qa_list.forEachIndexed { index, inquiryQuestion ->
            inquiryQuestion.sort_order = index
            inquiryQuestion.display_order = index + 1
        }
        val should_remove_qa_ids = one_off_sq_instance.qa_list_snapshot.filter {
            it.answer == null
        }.map {
            it.id
        }
        val new_advisor_screen_question_ids = one_off_sq_instance.advisor_screening_question_ids?.let {
            it.subtract(should_remove_qa_ids).toSet()
        }
        Patch.fromMutator(one_off_sq_instance) {
            this.qa_list_snapshot = filtered_qa_list
            this.advisor_screening_question_ids = new_advisor_screen_question_ids?.toList()
        }.patchThenGet()
        portal.sq_id = new_sq_instance.id
    }

    private fun trigger_task_events(portal: PortalAdvisor) {
        val task = portal.task ?: return
        when (portal.type) {
            PortalType.ADVISOR_PRE_CALL -> {
                taskEventService.trigger_events(task.id, portal.trigger_events.map {
                    TaskEvent().apply {
                        this.task_id = task.id
                        this.type = it
                        // set task event payload
                        when (it) {
                            TaskEventType.CA_SEND -> {
                                this.payload_id = portal.task_ca_id
                            }

                            TaskEventType.SQ_SEND -> {
                                this.payload_id = portal.sq_id
                            }

                            else -> {
                            }
                        }
                    }
                })
            }

            PortalType.ADVISOR_POST_CALL -> {
                taskEventService.trigger_events(task.id, portal.trigger_events.map {
                    TaskEvent().apply {
                        this.task_id = task.id
                        this.type = it
                        // set task event payload
                        when (it) {
                            TaskEventType.POST_CA_SEND -> {
                                this.payload_id = portal.task_ca_id
                            }

                            else -> {
                            }
                        }
                    }
                })
            }

            else -> throw BusinessException("illegal portal type")
        }
    }
    //endregion

    /**
     * replace the token placeholder in email.content with portal.token.
     */
    private fun get_email(
        portal: PortalAdvisor,
        email: EmailRequestPojo,
    ): EmailRequestPojo {
        email.content = portal.resolve_token(email.content)
        return email
    }

}
