package com.cpvsn.rm.core.features.portal.advisor

import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email_template.constant.EmailTemplateBuiltInTag
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.task.ca.TaskCaMethod

data class PortalAdvisorRequest(
        var portal: PortalAdvisor,
        var email: EmailRequestPojo? = null,
        var email_template_tags: Set<String>? = null
) {

    private val email_template_built_in_tags = email_template_tags?.mapNotNull {
        try {
            EmailTemplateBuiltInTag.valueOf(it)
        } catch (e: IllegalArgumentException) {
            null
        }
    }

    private fun compute_expect_steps_from_tags(tags: Collection<EmailTemplateBuiltInTag>): Set<PortalAdvisor.Step> {
        return when (portal.type) {
            PortalType.ADVISOR_PRE_CALL -> {
                tags.mapNotNull {
                    when (it) {
                        EmailTemplateBuiltInTag.TC -> PortalAdvisor.Step.TC
                        EmailTemplateBuiltInTag.CA -> PortalAdvisor.Step.CA.takeIf { EmailTemplateBuiltInTag.CA_LINK in tags }
                        EmailTemplateBuiltInTag.SQ -> PortalAdvisor.Step.SQ
                        EmailTemplateBuiltInTag.AG -> PortalAdvisor.Step.AG
                        else -> null
                    }
                }.toSet()
            }
            PortalType.ADVISOR_POST_CALL -> {
                tags.mapNotNull {
                    when (it) {
                        EmailTemplateBuiltInTag.CA -> PortalAdvisor.Step.POST_CA.takeIf { EmailTemplateBuiltInTag.CA_LINK in tags }
                        EmailTemplateBuiltInTag.PAYMENT_CONFIRM -> PortalAdvisor.Step.PAYMENT_CONFIRM
                        EmailTemplateBuiltInTag.ADVISOR_FEEDBACK -> PortalAdvisor.Step.FEEDBACK
                        else -> null
                    }
                }.toSet()
            }
            else -> emptySet()
        }
    }

    init {
        email_template_built_in_tags?.let {
            portal.expect_steps = compute_expect_steps_from_tags(it)
        }

        // if tags contains ca embedding, we set task_ca.method to TaskCaMethod.EMAIL
        email_template_built_in_tags?.let { tags ->
            portal.task_ca?.let { task_ca ->
                task_ca.method = when {
                    EmailTemplateBuiltInTag.CA_EXTERNAL in tags -> {
                        TaskCaMethod.EXTERNAL
                    }
                    EmailTemplateBuiltInTag.CA_EMBEDDING in tags -> {
                        TaskCaMethod.EMAIL
                    }
                    else -> TaskCaMethod.PORTAL
                }
            }
        }
    }

}
