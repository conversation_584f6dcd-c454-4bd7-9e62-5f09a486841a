package com.cpvsn.rm.core.features.portal.clientcontact

import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.rm.core.config.CpvsnConfig
import com.cpvsn.rm.core.config.Webpage
import com.cpvsn.rm.core.extensions.assert_exist
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.auth.portal.portal_internal_user
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.constant.EmailTemplateBuiltInTag
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModelIds
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.features.misc.entity_update_log.EntityUpdateLog
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.misc.schedule.ScheduleService
import com.cpvsn.rm.core.features.outsource.OutsourceService
import com.cpvsn.rm.core.features.outsource.pojo.requests.SelectTasksRequest
import com.cpvsn.rm.core.features.portal.clientcontact.pojo.ClientPortalAdjustRankRequest
import com.cpvsn.rm.core.features.portal.clientcontact.pojo.ContactScheduleRequest
import com.cpvsn.rm.core.features.portal.clientcontact.pojo.DropDownData
import com.cpvsn.rm.core.features.portal.clientcontact.pojo.TabsAggregation
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectMember
import com.cpvsn.rm.core.features.project.real_time.ProjectRealTimeNotifyRecord
import com.cpvsn.rm.core.features.project.real_time.ProjectRealTimeNotifyService
import com.cpvsn.rm.core.features.search.ElasticSearchHelper
import com.cpvsn.rm.core.features.search.EsSearchService
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskRepository
import com.cpvsn.rm.core.features.task.client_decline.TaskClientDeclineService
import com.cpvsn.rm.core.features.task.client_selection.ClientSelectionTagRef
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.util.PageUtil
import com.cpvsn.rm.core.util.biz_error
import com.cpvsn.rm.core.util.biz_require
import com.cpvsn.rm.core.util.global_executor
import com.cpvsn.web.auth.AuthContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.Instant
import java.time.format.DateTimeFormatter

@Service
class AdvisorRecomendationService {

    //region @
    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var esSearchService: EsSearchService

    @Autowired
    private lateinit var elasticSearchHelper: ElasticSearchHelper

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var advisorEsIndex: CpvsnConfig.IndexItem

    @Autowired
    private lateinit var scheduleService: ScheduleService

    @Autowired
    private lateinit var taskEventService: TaskEventService

    @Autowired
    private lateinit var slackService: SlackService

    @Autowired
    private lateinit var taskRepository: TaskRepository

    @Autowired
    private lateinit var taskClientDeclineService: TaskClientDeclineService

    @Autowired
    private lateinit var outsourceService: OutsourceService

    @Autowired
    private lateinit var projectRealTimeNotifyService: ProjectRealTimeNotifyService
    //endregion

    companion object {

        val defaultSort = Sort.by(
            Sort.Order(Task::client_portal_rank.name, Sort.Direction.ASC),
            Sort.Order("latest_contact_send.create_at", Sort.Direction.DESC),
            Sort.Order(Task::angle_id.name, Sort.Direction.ASC),
            Sort.Order(Task::rank.name, Sort.Direction.ASC)
        )

        fun Task.Query.applyBasicFilter(): Task.Query {
            return this.copy(
                or = this.or ?: listOf(
                    Task.Query(
                        client_publish_status = TaskStatus.ClientPublish.PUBLISHED
                    ),
                    Task.Query(
                        client_publish_status = TaskStatus.ClientPublish.INITIAL,
                        general_status_ne = TaskStatus.General.INITIAL
                    )
                ),
                advisor = (this.advisor ?: Advisor.Query()).copy(
                    gdpr_delete_at_is_null = true,
                    or = listOf(
                        Advisor.Query(
                            status_not_in = setOf(Advisor.Status.BLACKLIST),
                        ),
                        Advisor.Query(
                            status_is_null = true,
                        )
                    )
                )
            )
        }
    }

    fun retrieve_context_project_id(
        explicit_project_id: Int? = null,
    ): Int {
        val ticket = ClientPortalContext.current()

        if (!explicit_project_id.is_valid_id) return ticket.project_id

        val explicit_project = Project.get(explicit_project_id!!)
        biz_require(explicit_project.client_id == ticket.client_id) {
            "Illegal request"
        }
        return explicit_project_id
    }

    @Transactional
    fun get_drop_down_data(
        project_id: Int,
    ): DropDownData {
        val tasks = Task.findAll(
            Task.Query(project_id = project_id).applyBasicFilter(),
            Includes.setOf(
                Task::advisor dot Advisor::location,
                Task::angle,
                Task::client_selection_tag_refs dot ClientSelectionTagRef::tag
            )
        )
        val data = DropDownData(
            angles = tasks.mapNotNull { it.angle }
                .distinctBy { it.id }
                .sortedBy { it.rank },
            geographies = tasks.mapNotNull { it.advisor?.location }
                .distinctBy { it.id }
                .sortedByDescending { it.level },
            tags = tasks.flatMap { it.client_selection_tag_refs?.mapNotNull { ref -> ref.tag }.orEmpty() }
                .distinctBy { it.id }
                .sortedBy { it.name }
        )
        return data
    }

    @Transactional
    fun tabs_aggregation(
        project_id: Int,
    ): TabsAggregation {
        val now = Instant.now()
        val tasks = Task.findAll(Task.Query(project_id = project_id).applyBasicFilter())
        val res = TabsAggregation(
            count_all = tasks.count {
                it.client_portal_status != TaskStatus.ClientPortal.NOT_INTERESTED
            },
            count_for_review = tasks.count {
                it.general_status in setOf(TaskStatus.General.RECOMMENDED, TaskStatus.General.SELECTED)
                        && it.client_portal_status != TaskStatus.ClientPortal.NOT_INTERESTED
            },
            count_scheduled = tasks.count {
                it.general_status in setOf(TaskStatus.General.SCHEDULED, TaskStatus.General.ARRANGED)
                        && it.end_time?.isAfter(now) == true
            },
            count_completed = tasks.count {
                it.general_status == TaskStatus.General.COMPLETED
                        || it.end_time?.isBefore(now) == true
            },
            count_declined = tasks.count {
                it.client_portal_status == TaskStatus.ClientPortal.NOT_INTERESTED
            },
        )
        return res
    }

    @Transactional
    fun contact_schedule(
        request: ContactScheduleRequest,
    ): Task {
        val context = ClientPortalContext.current()
        val portal_internal_user = AuthContext.portal_internal_user

        val task = Task.get(
            request.task_id,
            Includes.setOf(
                Task::contact_schedules,
                Task::task_outsource_info,
            )
        )
        biz_require(task.project_id == context.project_id) { "Permission denied." }

        // figure out client contact by input email address
        val input_email = request.ranges.mapNotNull { it.email?.takeIf { s -> s.isNotBlank() } }.firstOrNull()

        val client_contact = input_email?.let {
            ClientContact.firstOrNull(
                ClientContact.Query(
                    client_id = task.client_id,
                    contact_infos = ContactInfo.Query(
                        type = ContactInfo.Type.EMAIL,
                        value = it
                    )
                )
            )
        }

        // save (or trigger TaskEvent)
        task.contact_schedules.orEmpty().forEach {
            Schedule.delete(it)
        }
        request.ranges.forEach {
            it.task_id = request.task_id
            it.project_id = task.project_id
            it.advisor_id = task.advisor_id
            it.client_contact_id = client_contact?.id
            it.pm_id = portal_internal_user?.id
            it.creator_type = Schedule.Role.CLIENT_CONTACT
        }
        scheduleService.create_contact_schedule(
            contact_id = client_contact?.id,
            request.ranges
        )

        Patch.fromMutator(task) {
            this.client_portal_status = TaskStatus.ClientPortal.SELECTED
        }.patch()
        taskClientDeclineService.invalidate_decline_of_task(task.id)

        projectRealTimeNotifyService.notify_project_activity(
            project_id = task.project_id,
            task_id = task.id,
            advisor_id = task.advisor_id.assert_exist(),
            type = ProjectRealTimeNotifyRecord.Type.CLIENT_SELECT,
        )
        taskEventService.trigger_event(TaskEvent().apply {
            this.task_id = task.id
            this.type = TaskEventType.CONTACT_APPROVE
        })
        if (task.task_outsource_info?.outsource_advisor_to == Region.current) {
            outsourceService.outsource_tasks_select(
                SelectTasksRequest(
                    project_id = task.project_id,
                    task_ids = setOf(task.id)
                )
            )
        }
        taskEventService.trigger_event(TaskEvent().apply {
            this.task_id = task.id
            this.type = TaskEventType.CONTACT_SCHEDULE_RESPOND
        })

        // notification email
        val res = Task.get(request.task_id, Includes.setOf(Task::contact_schedules))
        global_executor.execute {
            if (!task.ban_capvision_com_emails()) {
                notify_contact_schedule(res, res.contact_schedules.orEmpty())
            }
        }

        // record log
        EntityUpdateLog.byEntityUpdate(task, res, range = setOf(Task::contact_schedules))
            ?.let {
                if (context.is_internal) {
                    it.topic = EntityUpdateLog.Topic.USER_EDIT_CONTACT_SCHEDULE.name
                    it.create_by_id = AuthContext.portal_internal_user!!.id
                } else {
                    it.topic = EntityUpdateLog.Topic.CLIENT_EDIT_CONTACT_SCHEDULE.name
                }
                it.save()
            }

        slackService.async_send_client_selection_message(res)
        return res
    }

    @Transactional
    fun notify_contact_schedule(
        task: Task,
        schedules: List<Schedule>,
    ) {
        val schedule = schedules.first()
        Task.join(
            task, Includes.setOf(
                Task::project dot Project::client,
                Task::project dot Project::manager,
                Task::project dot Project::members dot ProjectMember::user
            )
        )

        val client_contact = schedule.client_contact_id?.let { ClientContact.get(it) }
        val client_by_email = client_contact?.let {
            Client.get(
                it.client_id, Includes.setOf(
                    Client::preference
                )
            )
        }

        val email_data = PlaceholderBasedModelIds(
            client_id = task.project!!.client_id,
            contact_id = client_contact?.id,
            project_id = task.project_id,
            task_id = task.id,
            advisor_id = task.advisor_id,
            user_id = task.project!!.manager?.uid
        ).fetch_data()
        val notify_team_email = placeholderBasedEmailTemplateService.process_first_by_data(
            query = EmailTemplate.Query(
                is_draft = false,
                content_type = EmailContentType.NOTIFY_CONTACT_SELECTED,
                tags_contains_all = setOf(EmailTemplateBuiltInTag.TO_INTERNAL_USER.name),
                is_system_template = true
            ),
            email_data
        ).to_email_request()

        val notify_creator_email = placeholderBasedEmailTemplateService.process_first_by_data(
            query = EmailTemplate.Query(
                is_draft = false,
                content_type = EmailContentType.NOTIFY_CONTACT_SELECTED,
                tags_contains_all = setOf(EmailTemplateBuiltInTag.EXTERNAL_USER_INVOLVED.name),
                is_system_template = true
            ),
            email_data
        ).to_email_request()

        /*
         这些placeholder不具有普适性 且易混淆，不放在EmailTemplatePlaceholder里
         */
        @Suppress("IfThenToElvis")
        fun replace_placeholders(
            template: String,
            none_str: String = "None provided, please follow up with the Clients on this project",
        ): String {
            return template
                .replace(
                    "{{ADVISOR_NAME_V1}}",
                    if (client_by_email == null
                        || client_by_email.status != Client.Status.EXECUTE
                        || client_by_email.compliance_status != Client.ComplianceStatus.APPROVED
                        || client_by_email.preference?.blind_expert_names_in_to_client_and_portal == true
                        || task.project?.blind_expert_profiles == true
                    ) {
                        "${task.display_id ?: ""} Advisor"
                    } else {
                        email_data.advisor?.full_name ?: ""
                    }
                )
                .replace(
                    "{{CONTACT_NAME_V1}}",
                    if (client_contact != null) {
                        client_contact.name
                    } else {
                        "A client with email address ${schedule.email}"
                    }
                )
                .replace(
                    "{{CONTACT_NAME_V2}}",
                    if (client_contact != null) {
                        "<a href=\"${Webpage.ClientContactProfile(client_contact.id).url}\">${client_contact.name}</a>"
                    } else {
                        "A client with email address ${schedule.email}"
                    }
                )
                /*
                .replace("{{CONTACT_PAGE_URL_V1}}",
                        client_contact?.let { Webpage.ClientContactProfile(it.id).url }
                            ?: ""
                )
                */
                .replace("{{DURATION_IN_MINUTES}}",
                    schedule.expected_duration_in_minutes?.let {
                        "$it minutes call"
                    } ?: none_str
                )
                .replace("{{TIME_SLOT}}",
                    // 未选择时间段则只有一条schedule (start_time=null)
                    // 选择多个时间段对应多条schedule
                    if (schedules.size == 1 && schedule.start_time == null) {
                        none_str
                    } else {
                        schedules.joinToString(" | ") { get_timeslot(it) }
                    }
                )
                .replace("{{NOTE_ENTRY}}",
                    schedule.comment?.takeIf { it.isNotBlank() } ?: none_str
                )
                .replace(
                    "{{TIME_ZONE_SELECTION}}",
                    schedule.zone_id_string ?: none_str
                )
        }

        notify_team_email.subject = replace_placeholders(notify_team_email.subject)
        notify_team_email.content = replace_placeholders(notify_team_email.content)
        emailService.send(notify_team_email)

        notify_creator_email.to_list = schedules.mapNotNull { it.email?.takeIf { s -> s.isNotBlank() } }.distinct()
        notify_creator_email.bcc_list = task.project!!.members.orEmpty().mapNotNull { it.user?.email }.distinct()
        notify_creator_email.content = replace_placeholders(notify_creator_email.content, none_str = "")
        emailService.send(notify_creator_email)
    }

    fun get_timeslot(schedule: Schedule): String {
        if (schedule.start_time == null) return ""
        val formats = listOf(
            "EEEE, MMMM dd",
            "h:mm",
            "a"
        )
        val starts = formats.map {
            DateTimeFormatter.ofPattern(it).withZone(schedule.zoneId).format(schedule.start_time)
        }
        val ends = formats.map {
            DateTimeFormatter.ofPattern(it).withZone(schedule.zoneId).format(schedule.end_time)
        }
        val res = "${starts[0]} from ${starts[1]} ${starts[2]}-${ends[1]} ${ends[2]} (${schedule.zone_id_string})"
        return res
    }

    private fun Task.hitKeyword(keyword: String): Boolean {
        val text = listOfNotNull(
            this.advisor!!.full_name,
            this.advisor!!.background,
            *this.advisor!!.jobs!!.map { it.position }.toTypedArray(),
            *this.advisor!!.jobs!!.flatMap {
                listOfNotNull(it.company?.name, it.company?.chinese_name)
            }.toTypedArray(),
            this.sq_instances!!.map { it.qa_list_snapshot_json }
        ).joinToString(" | ")
        return text.contains(keyword)
    }

    @Transactional
    fun search_with_code_filtering(
        query: Task.Query,
        queryString: String,
        pageRequest: PageRequest,
        extra: Includes?,
    ): Page<Task> {
        val basicExtra = Includes.setOf(
            Task::advisor dot Advisor::jobs dot AdvisorJob::company,
            Task::sq_instances
        )
        var tasks = Task.listAll(
            query = query,
            sort = pageRequest.sort ?: Sort.empty(),
            include = basicExtra
        )

        /*
         *  The query condition can be combination of AND/OR/NOT conditions,
         *  which is troublesome to implement using crud-kit / using query_string in ElasticSearch
         *  so we query records out and process in code
         */
        /*
         *  "dog" AND "food" NOT "Pepsi"
         */
        tasks = tasks.filter {
            it.hitKeyword(queryString)
        }

        val res = PageUtil.page(tasks, pageRequest.page, pageRequest.size)
        Task.join(res.items, extra.orEmpty().minus(basicExtra))
        return res
    }

    @Transactional
    @Synchronized
    fun adjust_client_portal_rank(
        request: ClientPortalAdjustRankRequest,
    ) {
        val t = Task.get(request.task_id)
        val portal_tasks = Task.listAll(
            query = Task.Query(project_id = t.project_id).applyBasicFilter(),
            sort = request.sorter
        ).toMutableList()

        portal_tasks.removeIf { it.id == request.task_id }

        val ahead_index = request.ahead_task_id?.let { it ->
            portal_tasks.indexOfFirst { t -> t.id == it }
        }
        val following_index = request.following_task_id?.let { it ->
            portal_tasks.indexOfFirst { t -> t.id == it }
        }
        when {
            // 放到了两个task之间
            ahead_index != null && following_index != null -> portal_tasks.add(following_index, t)
            // 放到了底部
            ahead_index != null && following_index == null -> portal_tasks.add(ahead_index + 1, t)
            // 放到了顶部
            ahead_index == null && following_index != null -> portal_tasks.add(following_index, t)
            ahead_index == null && following_index == null -> biz_error("Reorder failed, please refresh and retry.")
        }

        val updates = mutableListOf<Task>()
        portal_tasks.forEachIndexed { index, task ->
            if (task.client_portal_rank != index) {
                task.client_portal_rank = index
                updates.add(task)
            }
        }
        taskRepository.batchPatch(
            entities = updates,
            fields = setOf(Task::client_portal_rank.name)
        )
    }
}
