package com.cpvsn.rm.core.features.portal.clientcontact

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.svc.spring.CoreAppContextHolder
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.query.Criteria

import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.config.PortalProperties
import com.cpvsn.rm.core.features.portal.IPortal
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.Task
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import org.springframework.beans.factory.getBean
import java.time.Instant

class PortalClientContact : RmEntity(), IPortal {
    companion object : RmCompanion<PortalClientContact>()

    @Column
    override var type: PortalType = PortalType.CONTACT_PRE_CALL

    @Column
    @get:JsonIgnore
    override var token: String = ""

    @Column
    override var token_expire_at: Instant? = null

    @Column
    var create_by_id: Int = 0

    @Column
    var project_id: Int = 0

    @Column
    override var disable: Boolean = false

    @Column
    @JsonIgnore
    var task_ids: String = ""

    /**
     * if type is CONTACT_FEEDBACK
     * task_id_set will have only one element
     */
    val feedback_task_id: Int?
        get() {
            return if (type == PortalType.CONTACT_POST_CALL) {
                task_id_set.firstOrNull()
            } else {
                null
            }
        }

    @Column
    @JsonIgnore
    var trigger_events_string: String = ""

    @Column
    var is_public_portal: Boolean = false

    //region +
    var project: Project? = null

    var tasks: List<Task>? = null
    //endregion

    //region $
    @get:JsonProperty(access = JsonProperty.Access.WRITE_ONLY)
    var trigger_events: Set<TaskEventType>
        get() = trigger_events_string.split(",")
                .filter { it.isNotEmpty() }
                .mapNotNull {
                    try {
                        TaskEventType.valueOf(it)
                    } catch (e: IllegalArgumentException) {
                        null
                    }
                }
                .toSet()
        set(value) {
            trigger_events_string = value
                    .joinToString(",") { it.name }
        }

    var task_id_set: Set<Int>
        get() = task_ids.split(",")
                .mapNotNull { it.toIntOrNull() }
                .toSet()
        set(value) {
            task_ids = value.joinToString(",") { it.toString() }
        }

    val show_advisor_picker
        get() = type == PortalType.CONTACT_PRE_CALL
                && TaskEventType.CONTACT_SEND in trigger_events

    val show_availability_grid
        get() = type == PortalType.CONTACT_PRE_CALL
                && TaskEventType.CONTACT_SCHEDULE_SEND in trigger_events

    val public_token: String?
        get() = token.takeIf { is_public_portal }
    val public_link: String?
        get() {
            return public_token?.let {
                resolve_token(CoreAppContextHolder.context.getBean<PortalProperties>().contactPreCallUrl)
            }
        }
    //endregion

    class Query(
            @Criteria.Eq
            val id: Int? = null,
            @Criteria.IdsIn
            val ids: Set<Int>? = null,
            @Criteria.Eq
            val token: String? = null,

            @Criteria.Eq
            val project_id: Int? = null,
            @Criteria.Eq
            val task_id: Int? = null,
            @Criteria.IdsIn
            val task_ids: Set<Int>? = null,
    ) : BaseQuery<PortalClientContact>()
}
