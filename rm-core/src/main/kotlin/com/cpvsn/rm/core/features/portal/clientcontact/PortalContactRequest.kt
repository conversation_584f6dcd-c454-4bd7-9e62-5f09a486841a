package com.cpvsn.rm.core.features.portal.clientcontact

import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email_template.constant.EmailTemplateBuiltInTag
import com.cpvsn.rm.core.features.task.event.TaskEventType

class PortalContactRequest(
        var portal: PortalClientContact,
        var email: EmailRequestPojo? = null,
        var email_template_tags: Set<EmailTemplateBuiltInTag>? = null
) {
    // compute trigger_events from template_tags
    private val trigger_events_from_tags
        get() = email_template_tags?.mapNotNull {
            when (it) {
                EmailTemplateBuiltInTag.AG -> TaskEventType.CONTACT_SCHEDULE_SEND
                EmailTemplateBuiltInTag.ADVISOR_PICKER -> TaskEventType.CONTACT_SEND
                EmailTemplateBuiltInTag.FEEDBACK -> TaskEventType.CONTACT_FEEDBACK_SEND
                else -> null
            }
        }?.toSet()

    init {
        trigger_events_from_tags?.let {
            portal.trigger_events = it
        }
    }
}
