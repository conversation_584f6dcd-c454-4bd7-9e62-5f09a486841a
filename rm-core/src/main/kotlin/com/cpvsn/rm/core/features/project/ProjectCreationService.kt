package com.cpvsn.rm.core.features.project

import com.cpvsn.core.svc.spring.CoreAppContextHolder
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.rm.core.annotation.RequireEsSync
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntryService
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.inquiry.InquiryService
import com.cpvsn.rm.core.features.inquiry.model.Inquiry
import com.cpvsn.rm.core.features.inquiry.model.InquiryBranch
import com.cpvsn.rm.core.features.inquiry.model.InquiryQuestion
import com.cpvsn.rm.core.features.search.EsIndex
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.task.angle.TaskAngle
import com.cpvsn.rm.core.features.task.angle.TaskAngleMember
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import com.cpvsn.rm.core.util.biz_require
import com.google.api.services.drive.Drive
import com.google.api.services.drive.model.File
import com.google.api.services.sheets.v4.Sheets
import com.google.api.services.sheets.v4.model.BatchUpdateValuesRequest
import com.google.api.services.sheets.v4.model.BatchUpdateValuesResponse
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Async
import org.springframework.scheduling.annotation.AsyncResult
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import java.util.concurrent.Future

@Service
class ProjectCreationService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var appConfigEntryService: AppConfigEntryService

    @Autowired
    private lateinit var slackService: SlackService

    @Autowired
    private lateinit var clientGoogleSheetService: ClientGoogleSheetService

    @Autowired
    private lateinit var inquiryService: InquiryService

    @Autowired(required = false)
    private var drive: Drive? = null

    @Autowired(required = false)
    private var sheets: Sheets? = null

    // for spring proxy
    // do not remove this property unless you are fully aware of its effects
    private val self: ProjectCreationService by lazy {
        CoreAppContextHolder.getBean()
    }

    @RequireEsSync(es_index = EsIndex.PROJECT)
    @Transactional
    fun save_cascade(entity: Project): Project {

        if (entity.angles == null) entity.angles = get_default_angles(entity)

        val res = Project.save(
            entity, cascades = setOf(
                Cascade.oneToMany(Project::notes),
                Cascade.oneToMany(Project::tags),
                Cascade.oneToMany(Project::tickers),
                Cascade.oneToMany(
                    Project::angles, nestedCascades = setOf(
                        Cascade.oneToMany(TaskAngle::members),
                    )
                ),
                Cascade.oneToMany(
                    Project::project_client_contacts,
                    callback = Cascade.Callback.prePersist { e, _ ->
                        // update client_contact status to active
                        Patch.fromMap(
                            e.client_contact_id, mapOf(
                                ClientContact::status to ClientContact.Status.ACTIVE
                            )
                        ).patch()
                    }),
                Cascade.oneToMany(Project::members),
//                Cascade.oneToOne(Project::sq, nestedCascades = setOf(
//                        Cascade.oneToMany(Inquiry::branches)
//                )),
                Cascade.oneToOne(Project::bcg_hub_project),
                Cascade.oneToMany(Project::lead_groups)
            )
        )
        if (entity.angles?.any { it.inquiry_branch != null } == true) {
            val inquiry = Project.get(res.id, Includes.setOf(Project::sq)).sq ?: let {
                val inquiry = inquiryService.save_cascade(Inquiry().apply {
                    this.creator_type = Inquiry.CreatorType.KM
                    this.name = entity.name.orEmpty()
                    this.client_id = entity.client_id
                    this.type = Inquiry.Type.SCREENING
                })
                Patch.fromMutator(res) {
                    this.sq_id = inquiry.id
                }.patch()
                inquiry
            }
            // we find the saved angle by name
            val angle_name_map = res.angles?.associateBy { it.name }
            // here we need to auto create sq
            entity.angles?.map { cur_angle ->
                // set branches related saved angles
                val saved_branch = cur_angle.inquiry_branch.apply {
                    this?.inquiry_id = inquiry.id
                }?.let {
                    // since different inquiry branch can't have the same title, we first try to find the same title in the same inquiry when saving for multiple angles
                    InquiryBranch.firstOrNull(
                        query = InquiryBranch.Query(
                            inquiry_id = inquiry.id,
                            title = it.title,
                            inquiry_type = Inquiry.Type.SCREENING,
                        )
                    ) ?: InquiryBranch.save(it)
                } ?: return@map
                angle_name_map?.get(cur_angle.name)?.let {
                    Patch.fromMutator(it) {
                        this.inquiry_branch_id = saved_branch.id
                    }.patch()
                }
                cur_angle.inquiry_branch
            }.orEmpty()
        }
        InvokeUtil.trigger(Event.PROJECT_CREATED(project_id = entity.id))
        return res
    }

    /**
     * https://www.notion.so/capvision/New-Project-Create-google-sheet-7ffb682d357247c78528cb19d5f0b7f3
     */
    @Async
    fun create_google_sheet_async(project: Project): Future<String?> {
        // currently @Async method ignore all uncaught exceptions
        // so we have to handle exceptions here
        val res = try {
            create_google_sheet(project)
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
        return AsyncResult(res)
    }

    fun create_google_sheet(project: Project): String? {
        if (project.sub_type == Project.SubType.Survey) return null
        if (project.google_sheet_id != null) return null
        val driveService = requireNotNull(drive)

        val template_id = appConfigEntryService
            .find(AppConfigKey.PROJECT_GOOGLE_SHEET_TEMPLATE_ID)
            ?.value?.takeUnless { it.isEmpty() } ?: return null
        val folder_id = appConfigEntryService
            .find(AppConfigKey.PROJECT_GOOGLE_SHEET_FOLDER_ID)
            ?.value?.takeUnless { it.isEmpty() } ?: return null

        val file = File()
        file.name = project.name
        file.mimeType = "application/vnd.google-apps.spreadsheet"   // sheet
        file.parents = listOf(folder_id)                            // put in specified folder

        val res = driveService.Files().copy(template_id, file)
            .setFields("*")
            .execute()

//        val permission = Permission()
//        permission.setRole("writer")
//        permission.setType("user")
//        permission.setEmailAddress("<EMAIL>")
//        driveService.Permissions().create(res.id, permission)
//            //.setTransferOwnership(true)
//            .execute()

        Patch.fromMutator(project) {
            google_sheet_id = res.id
        }.patch()

        slackService.async_pin_google_sheet_message(project.id)
        return res.id
    }

    fun create_client_google_sheet(project: Project) {
        clientGoogleSheetService.create_client_google_sheet(project)
    }

    fun fill_client_google_sheet_data(project: Project) {
        clientGoogleSheetService.fill_client_google_sheet_data(project)
    }

    /**
     * Updates the "Status" columns on the "Screening" page to replace "Chasing" entries with empty strings.
     * This is useful for managers to tell their subordinates to call experts back who didn't pick up the phone.
     * This sadly can't be done with Apps Script because the script cannot be run in sheets created by service accounts.
     * @param project: [Project] 更新这个project的google sheet. Takes a project object and updates that project's google sheet.
     * @return [BatchUpdateValuesResponse] that summarizes how many rows, columns, and cells were updated.
     */
    fun clear_chasing(project: Project): BatchUpdateValuesResponse? {
        if (project.google_sheet_id == null) return null
        val sheetsService = requireNotNull(sheets)
        val sheet_id = project.google_sheet_id
        val ranges_with_chasing = listOf(
            "Screening!D:D",
            "Screening!H:H",
            "Screening!L:L",
            "Screening!P:P",
            "Screening!T:T"
        ) //the list of ranges that have "Chasing" that we want to clear
        //https://developers.google.com/sheets/api/reference/rest/v4/spreadsheets.values/batchGet
        val values = sheetsService.spreadsheets().values().batchGet(sheet_id).setRanges(ranges_with_chasing).execute()
        //get rid of "Checking"
        values.valueRanges.map {
            biz_require(it["values"] is List<*>)
            val valuesList =
                it["values"] as List<List<Any?>> //This comes from the Google Sheets API and is a list of lists of strings/ints
            it["values"] = valuesList.map { value ->
                if (value.isNotEmpty()) {
                    if (value[0] == "Chasing") {
                        listOf("")
                    } else {
                        value
                    }
                } else {
                    value
                }
            }
        }
        val batch_update = BatchUpdateValuesRequest()
            .setValueInputOption("RAW")
            .setData(values.valueRanges)
        //https://developers.google.com/sheets/api/reference/rest/v4/spreadsheets.values/batchUpdate
        val res = sheetsService.spreadsheets().values().batchUpdate(sheet_id, batch_update).execute()
        return res
    }

    /**
     * Find default angles if none are provided. Uses [DefaultProjectAngle] table and [project] properties to determine whether default angles apply and, if so, which existing angle to copy to the new project.
     * If a `DefaultProjectAngle` property (eg. `client_id`) is null, that property does not affect the search
     * @param project the project to which to add default angles
     */
    fun get_default_angles(project: Project): List<TaskAngle> {
        if (project.client == null) {
            Project.join(project, setOf(Project::client.name, Project::manager.name, Project::investment_target_companies.name))
        }
        val default_project_angles = DefaultProjectAngle.findAll(
            include = Includes.setOf(DefaultProjectAngle::angle dot TaskAngle::inquiry_branch dot InquiryBranch::questions)
        ).asSequence().filter {
            ( project.client_id == null || it.client_ids.isEmpty() || project.client_id in it.client_ids )
                    && (project.client_id == null || project.client_id!! !in it.exclude_client_ids)
                    && (it.project_sub_type == project.sub_type || it.project_sub_type == null)
                    && (it.project_case_type == project.case_type || it.project_case_type == null)
                    && (it.client_type == project.client?.type || it.client_type == null)
        }
        val angles = default_project_angles.mapNotNull { it.angle }.distinctBy { it.id }.toList()
        // Ensure angles have unique names
        angles.groupBy { it.name }.flatMap { (_, angles_with_same_name) ->
            angles_with_same_name.mapIndexed { index, angle ->
                if (index == 0) angle
                else angle.apply { name = "${angle.name} ${index + 1}" }
            }
        }
        // Ensure inquiry branches have unique names
        angles.map { it.inquiry_branch }.groupBy { it?.title }.flatMap { (_, branches_with_same_name) ->
            branches_with_same_name.mapIndexed { index, branch ->
                if (index != 0) branch?.apply { title = "${branch.title} ${index + 1}" }
                branch?.inquiry_id = 0
            }
        }
        // Dynamically update the angle topic and screening questions to include the real project name and target company
        angles.forEach { angle ->
            if (!project.name.isNullOrBlank()) angle.topic = angle.topic?.replace("[PROJECT NAME]", project.name ?: "[PROJECT NAME]")
            if (!project.investment_target_company_names.isNullOrBlank()) angle.topic = angle.topic?.replace("[TARGET COMPANY]", project.investment_target_company_names ?: "[TARGET COMPANY]")
            angle.inquiry_branch?.questions?.forEach { question ->
                if (!project.name.isNullOrBlank()) question.title = question.title.replace("[PROJECT NAME]", project.name ?: "[PROJECT NAME]")
                if (!project.investment_target_company_names.isNullOrBlank()) question.title = question.title.replace("[TARGET COMPANY]", project.investment_target_company_names ?: "[TARGET COMPANY]")
            }
            angle.inquiry_branch?.let { branch ->
                    branch.name = branch.name.replace("[PROJECT NAME]", project.name ?: "[PROJECT NAME]")
            }

            // Add the using call consent SQ if needed
            val add_recording_question_client_ids = appConfigEntryService.find(AppConfigKey.USING_CALL_CONSENT_SQ_CLIENTS)?.value?.split(",")?.map { it.trim().toInt() }
            val shouldAddRecordingQuestion = project.client_id != null &&
                    add_recording_question_client_ids?.contains(project.client_id) == true &&
                    angle.inquiry_branch?.questions?.any {
                        InquiryQuestion.Tag.CALL_RECORDING_CONSENT in it.tags
                    } != true

            if (shouldAddRecordingQuestion) {
                angle.inquiry_branch?.let { branch ->
                    branch.questions = branch.questions?.plus(InquiryQuestion.create_record_consent_question().apply {
                        this.sort_order = branch.questions!!.size // put the call consent SQ at the end of the SQ list
                    })
                }
            }
            // angle_members should only include the project manager
            val angle_member = project.members?.find{it.role == ProjectMember.Role.PROJECT_MANAGER}?.uid?.let { uid ->
                TaskAngleMember().apply {
                    this.user_id = uid
                    this.angle_id = angle.id
                }
            }
            angle.members = angle_member?.let { listOf(it) }
            // Ensure calendar invite to email list is not copied from default angle - we want this to be blank initially
            angle.calendar_invite_to_email_list = null
        }

        return angles
    }
}
