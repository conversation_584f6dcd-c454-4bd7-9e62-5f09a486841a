package com.cpvsn.rm.core.features.project

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.annotation.TableDefinition
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.Event
import java.time.Instant

@TableDefinition(
    indices = [
        TableDefinition.IndexDefinition(columns = ["project_id"])
    ]
)
class ProjectEvent : RmEntity(), UserAuditing, Event {
    companion object : RmCompanion<ProjectEvent>()

    @Column
    var project_id: Int = 0

    @Column
    var type: Type = Type.CLOSE

    @Column
    var description: String = ""

    @Column
    override var create_by_id: Int = 0

    override var update_by_id: Int = 0

    @Relation
    var create_by: User? = null

    @Relation
    var project: Project? = null

    enum class Type(
        val affect: (Project) -> Unit = {}
    ) {
        CLOSE({ it.status = Project.Status.CLOSED }),
    }

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Eq
        val project_id: Int? = null,
        @Criteria.IdsIn
        val project_ids: Set<Int>? = null,

        @Criteria.Eq
        val type: Type? = null,
        @Criteria.In
        val type_in: Set<Type>? = null,
        @Criteria.Eq
        val create_by_id: Int? = null,
        @Criteria.IdsIn
        val create_by_ids: Set<Int>? = null,

        @Criteria.Gte
        val create_at_gte: Instant? = null,
        @Criteria.Lte
        val create_at_lte: Instant? = null,

        @Criteria.Join
        val create_by: User.Query? = null,
    ) : BaseQuery<ProjectEvent>()
}
