package com.cpvsn.rm.core.features.project

import com.cpvsn.core.model.BusinessException
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.util.InvokeUtil
import com.cpvsn.rm.core.util.debug
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Suppress("UNUSED_PARAMETER")
@Service
class ProjectEventService {
    companion object {
        private val logger = LoggerFactory.getLogger(ProjectEventService::class.java)
    }

    /**
     * return null if an event is ignored
     */
    @Transactional
    fun trigger_event(event: ProjectEvent): ProjectEvent? {
        val project = Project.get(event.project_id)
        return trigger_event(project, event)
    }

    @Transactional
    fun trigger_events(
        project_id: Int,
        events: List<ProjectEvent>
    ): List<ProjectEvent> {
        if (events.isEmpty()) return emptyList()
        val project = Project.get(project_id)
        return events.mapNotNull {
            trigger_event(project, it)
        }
    }

    private fun trigger_event(
        project: Project,
        event: ProjectEvent,
    ): ProjectEvent? {
        if (should_ignore(project, event.type)) return null
        validate(project, event.type)

        val patch = Patch.fromMutator(project) {
            event.type.affect(project)
        }
        logger.debug { "about to change project with patch $patch" }
        Project.patch(patch)

        logger.debug { "trigger project event, project id = ${project.id}, event type = ${event.type}" }
        InvokeUtil.trigger(event)

        return ProjectEvent.save(event)
    }

    /**
     * if this method returns true
     * the event should not be persisted in db, and the project status should not be changed
     * we should simply act just like nothing happen
     */
    private fun should_ignore(
        project: Project,
        event_type: ProjectEvent.Type
    ): Boolean {
        return false
    }

    /**
     * decides if an event is allowed to happen
     */
    private fun validate(
        project: Project,
        event_type: ProjectEvent.Type
    ) {
        val valid = true
        if (!valid) {
            throw BusinessException("illegal project status")
        }
    }
}
