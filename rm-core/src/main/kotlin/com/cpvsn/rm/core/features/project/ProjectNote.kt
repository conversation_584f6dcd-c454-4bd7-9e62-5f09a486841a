package com.cpvsn.rm.core.features.project

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.user.User


class ProjectNote : RmEntity() {
    companion object : RmCompanion<ProjectNote>()

    @Column
    var project_id: Int = 0

    @Column
    var note: String = ""

    @Column
    var type: Type = Type.RECRUITING

    @Column
    var creator_uid: Int = 0

    //region +
    @Relation(reference = "creator_uid")
    var creator: User? = null

    @Relation
    var project: Project? = null

    val creator_name: String?
        get() = creator?.name
    //endregion

    enum class Type {
        RECRUITING,
        CLIENT
    }

    data class Query(
            @Criteria.IdsIn
            val ids: Set<Int>? = null,
            @Criteria.Eq
            val project_id: Int? = null,
            @Criteria.IdsIn
            val project_ids: Set<Int>? = null,
    ) : BaseQuery<ProjectNote>()
}
