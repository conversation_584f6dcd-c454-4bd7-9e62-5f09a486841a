package com.cpvsn.rm.core.features.project

import com.cpvsn.rm.core.annotation.DocConstant

@DocConstant(displayNameFieldName = "value")
enum class ProjectSettingCommonOption(val value: String) {
    NO("不要"),
    YES("接受"),
    REQUIRE_CLIENT_APPROVAL("需客户审核")
}

typealias GovernmentEmployeesOption = ProjectSettingCommonOption
typealias CurrentListedOption = ProjectSettingCommonOption

@DocConstant(displayNameFieldName = "value")
enum class ProjectSettingExtendedOption(val value: String) {
    NO("不要"),
    YES("接受"),
    REQUIRE_CLIENT_APPROVAL("需客户审核"),

    DIMISSION_6_MONTHS("至少离职6个月"),
    DIMISSION_12_MONTHS("至少离职12个月")
}

typealias FormerListedOption = ProjectSettingExtendedOption
typealias TargetCompanyEmployeesOption = ProjectSettingExtendedOption
