package com.cpvsn.rm.core.features.project

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.misc.Tag

import java.time.Instant

class ProjectTag : RmEntity(), SoftDeletable {

    companion object : RmCompanion<ProjectTag>()

    @Column
    var project_id: Int? = null

    @Column
    var tag_id: Int? = null

    override var delete_at: Instant? = null

    //region +
    
    @Relation
    var tag: Tag? = null

    @Relation
    var project: Project? = null
    //endregion

    data class Query(
            @Criteria.IdsIn
            var ids: Set<Int>? = null,
            @Criteria.Eq
            var project_id: Int? = null,
            @Criteria.IdsIn
            var project_ids: Set<Int>? = null,
    ) : BaseQuery<ProjectTag>()
}
