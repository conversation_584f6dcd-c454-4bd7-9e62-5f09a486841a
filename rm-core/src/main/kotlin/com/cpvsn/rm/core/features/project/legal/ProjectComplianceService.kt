package com.cpvsn.rm.core.features.project.legal

import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.crud.model.Patch
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate

@Service
class ProjectComplianceService {

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Transactional
    fun save_or_patch(
            project_id: Int,
            patch: Patch<ProjectCompliance>,
    ): ProjectCompliance {
        return transactionTemplate.extExecute {
            val existed = ProjectCompliance.firstOrNull(ProjectCompliance.Query(project_id = project_id))
            val res = if (existed == null) {
                ProjectCompliance.save(patch.entity)
            } else {
                patch.entity.id = existed.id
                ProjectCompliance.patchThenGet(patch)
            }

            ProjectComplianceChangeLog {
                project_compliance_id = res.id
                change_fields = patch.fields.toList()
            }.save()

            res
        }
    }
}
