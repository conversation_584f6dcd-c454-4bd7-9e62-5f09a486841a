package com.cpvsn.rm.core.features.record

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import java.time.Instant

class MoreInfoRequestRecord : RmEntity(), SoftDeletable {

    companion object : RmCompanion<MoreInfoRequestRecord>()

    @Column
    var project_id: Int? = null

    @Column
    var task_id: Int? = null

    @Column
    var type: Type = Type.PROJECT

    @Column
    var note: String = ""

    @Column
    var request_by_contact_id: Int? = null

    /**
     *  When a DB user generates a compliance portal copy link on the project page,
     *  user_id is not null, and client_contact_id is null
     */
    @Column
    var request_by_user_id: Int? = null

    @Relation
    var project: Project? = null

    @Relation
    var task: Task? = null

    @Relation(reference = "request_by_contact_id")
    var contact: ClientContact? = null


    enum class Type {
        PROJECT,
        TASK,
    }

    override var delete_at: Instant? = null

    data class Query(
        @Criteria.Eq
        var id: Int? = null,
        @Criteria.IdsIn
        var ids: Set<Int>? = null,
        @Criteria.Eq
        var type: Type? = null,
        @Criteria.Eq
        var project_id: Int? = null,
        @Criteria.Eq
        var task_id: Int? = null,
    ) : BaseQuery<MoreInfoRequestRecord>()
}