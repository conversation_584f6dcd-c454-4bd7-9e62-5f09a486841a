package com.cpvsn.rm.core.features.right

import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.right.model.Role
import com.cpvsn.rm.core.features.right.model.RolePermission
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Service
class RoleRepository : RmBaseRepository<Role>() {

    @Transactional
    fun save_cascade(
            entity: Role,
    ): Role {
        return Role.save(entity,
                cascades = setOf(
                        Cascade.oneToMany(Role::permissions, callback = Cascade.Callback.prePersist { e, parent ->
                            e.role_id = parent.id
                        })
                )
        )
    }

    @Transactional
    fun patch_cascade(
            patch: Patch<Role>,
            include: Set<String>,
    ): Role {
        return Role.patchThenGet(
                patch = patch,
                include = include,
                cascades = setOf(
                        Cascade.oneToMany(Role::permissions, callback = Cascade.Callback.prePersist { e, parent ->
                            e.role_id = parent.id
                        })
                )
        )
    }

    @Transactional
    override fun delete(id: Int) {
        super.delete(id)

        RolePermission.findAll(RolePermission.Query(
                role_id = id
        )).forEach {
            RolePermission.delete(it)
        }
    }
}
