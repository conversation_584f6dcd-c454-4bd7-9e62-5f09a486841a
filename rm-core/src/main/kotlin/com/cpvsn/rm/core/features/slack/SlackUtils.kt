package com.cpvsn.rm.core.features.slack

import com.cpvsn.core.svc.hashid.HashIdUtil
import com.cpvsn.core.util.RandomUtil

object SlackUtils {

    private val regex = """[\da-z_-]+""".toRegex()
    private val alphabet = RandomUtil.CANDIDATE_CHAR_SET.LOWER_LETTERS
        .plus(RandomUtil.CANDIDATE_CHAR_SET.DIGITS)

    private val hashIds = HashIdUtil.instance(
        "SlackUtils",
        6,
        alphabet = alphabet,
    )

    private fun hash(text: String): String {
        return hashIds.encode(text.hashCode())
    }

    fun is_valid_channel_name(channel_name: String): Boolean {
        return regex.matches(channel_name)
    }

    /**
     * @param append_hash append a fixed length hash string so that less likely it will produce duplicated channel name
     */
    fun to_valid_channel_name(
        channel_name: String,
        append_hash: Boolean = false,
    ): String {
        if (is_valid_channel_name(channel_name))
            return channel_name

        // Remove any special characters from the input string
        val cleanedString = channel_name
            .replace(Regex("[^A-Za-z0-9-_ ]"), "")

        // Replace spaces with underscores
        val underscoredString = cleanedString
            .replace(" ", "_")

        val res = if (append_hash) {
            val hash = hash(channel_name)
            "${underscoredString}_$hash"
        } else {
            underscoredString
        }

        // Convert to lowercase and truncate to maximum length of 80 characters
        return res.toLowerCase().take(80)
    }

    fun get_link_text(
        url: String,
        title: String,
    ): String {
        return "<$url|$title>"
    }

}
