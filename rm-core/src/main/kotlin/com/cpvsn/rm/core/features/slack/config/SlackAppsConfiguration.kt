package com.cpvsn.rm.core.features.slack.config

import com.slack.api.bolt.App
import com.slack.api.bolt.AppConfig
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.stereotype.Component
import javax.annotation.PostConstruct

@Component
@EnableConfigurationProperties(SlackConfig::class)
class SlackAppsConfiguration {

    private var SLACK_APP_MAP = mutableMapOf<SlackAppEnum, App>()

    @Autowired
    private lateinit var slackConfig: SlackConfig

    @PostConstruct
    fun postConstruct() {
        slackConfig.apps.forEach {
            val appConfig = AppConfig().toBuilder()
                .singleTeamBotToken(it.slack_bot_token)
                .signingSecret(it.slack_signing_secret)
                .build()
            val app = App(appConfig)
            SLACK_APP_MAP[SlackAppEnum.valueOf(it.app_enum)] = app
        }
    }

    fun getSlackApp(app_enum: SlackAppEnum): App {
        return SLACK_APP_MAP[app_enum]!!
    }
}