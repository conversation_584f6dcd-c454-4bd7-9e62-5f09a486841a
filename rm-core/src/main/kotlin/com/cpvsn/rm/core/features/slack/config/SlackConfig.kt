package com.cpvsn.rm.core.features.slack.config

import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

@Component
@ConfigurationProperties(prefix = "slack")
class SlackConfig {
    class AppConfig {
        var app_enum: String = ""
        var id: String = ""
        var slack_bot_token: String = ""
        var slack_signing_secret: String = ""
        var client_secret: String = ""
    }

    var enable: Boolean = true
    var apps: List<AppConfig> = emptyList()

    fun getAppConfig(app_enum: SlackAppEnum): AppConfig {
        return apps.first { it.app_enum == app_enum.name }
    }
}