package com.cpvsn.rm.core.features.task

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.user.User
import java.time.Instant

/**
 * On the call tracker, sometimes there is specific information that employees, management, and finance want to convey
 * to each other. CallTrackNote allows these stakeholders to leave a trail of notes explaining this information.
 */
class CallTrackNote : RmEntity(), UserAuditing, SoftDeletable {
    companion object : RmCompanion<CallTrackNote>()

    @Column
    var task_id: Int = 0

    @Column
    var note: String = ""

    @Column
    var type: Type = Type.USER

    @Column
    override var create_by_id: Int = 0

    @Column
    override var update_by_id: Int = 0

    @Column(updatable = false, insertable = false)
    override var delete_at: Instant? = null

    @Relation
    var create_by: User? = null

    /**
     * System notes are created when call tracker override values are changed,
     * eg. [Task.call_tracker_client_hours] and [Task.custom_sourced_override]
     * @param message To be saved as the [CallTrackNote.note] value.
     */
    enum class AutomaticNote (
        val message: (user: User, old_value: String, new_value: String) -> String
    ) {
        CUSTOM_SOURCE_OVERRIDE ({
                user, old_value, new_value ->
            "${user.name.takeIf { s -> s.isNotBlank() } ?: user.username} changed the custom sourced status from $old_value to $new_value."
        }),

        CLIENT_HOURS ({
                user, old_value, new_value ->
            "${user.name.takeIf { s -> s.isNotBlank() } ?: user.username} changed the client hours from $old_value to $new_value."
        }),

        LEAD ({
                user, old_value, new_value ->
            "${user.name.takeIf { s -> s.isNotBlank() } ?: user.username} changed the lead from $old_value to $new_value."
        }),

        SUPPORT ({
                user, old_value, new_value ->
            "${user.name.takeIf { s -> s.isNotBlank() } ?: user.username} changed the support from $old_value to $new_value."
        }),
        FAIL_OR_CANCEL ({
                user, old_value, new_value ->
            "${user.name.takeIf { s -> s.isNotBlank() } ?: user.username} changed the task status from $old_value to $new_value. Client Hours have been set to 0."
        }),
        REVERT_FAIL_OR_CANCEL ({
                user, old_value, new_value ->
            "${user.name.takeIf { s -> s.isNotBlank() } ?: user.username} changed the task status from $old_value to $new_value. Client Hours have been reset."
        })
    }

    enum class Type {
        SYSTEM,
        USER,
        BILLING_NOTES
    }

    data class Query (
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,

        @Criteria.Eq
        val task_id: Int? = null,
        @Criteria.IdsIn
        val task_ids: Set<Int>? = null
    ): BaseQuery<CallTrackNote>()
}