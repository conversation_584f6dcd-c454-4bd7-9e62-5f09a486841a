package com.cpvsn.rm.core.features.task

import com.cpvsn.core.base.entity.BaseMapper
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.core.util.extension.BD
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.orm.annotation.*
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.orm.entity.WithOptimisticLock
import com.cpvsn.crud.query.Criteria
import com.cpvsn.crud.query.CustomSorts
import com.cpvsn.crud.tsid.GeneratedTsId
import com.cpvsn.rm.core.base.agg.AggregationMapper
import com.cpvsn.rm.core.base.agg.AggregationQuery
import com.cpvsn.rm.core.base.entity.ColumnDefinitions
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.model.*
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorPhysicianBoardCertification
import com.cpvsn.rm.core.features.advisor.referral.Referral
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.client.Client
import com.cpvsn.rm.core.features.client.blacklist.ClientBlacklist
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.client.legal_preference.ClientComplianceApprovePolicy
import com.cpvsn.rm.core.features.client.legal_preference.chaperone.TaskClientChaperone
import com.cpvsn.rm.core.features.compliance.snapshot.TaskLegalSnapshot
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.decipher.base.DecipherSurveyStatus
import com.cpvsn.rm.core.features.finance.advisor_payment.PaymentTaskInfoSnapshotV1
import com.cpvsn.rm.core.features.finance.advisor_payment.local.LocalAdvisorPayment
import com.cpvsn.rm.core.features.finance.payment.Payable
import com.cpvsn.rm.core.features.finance.payment.PaymentTopic
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.misc.entity_update_log.EntityUpdateLog
import com.cpvsn.rm.core.features.misc.loopup.LoopupNumber
import com.cpvsn.rm.core.features.misc.loopup.LoopupRoom
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.outsource.pojo.TaskOutsourceInfo
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.transaction.ReceivableTransaction
import com.cpvsn.rm.core.features.record.MoreInfoRequestRecord
import com.cpvsn.rm.core.features.search.pojo.AngleExpertCount
import com.cpvsn.rm.core.features.search.pojo.ProjectScreenedScheduledCount
import com.cpvsn.rm.core.features.task.advisor_consent.TaskAdvisorRecordConsent
import com.cpvsn.rm.core.features.task.advisor_profile.TaskAdvisorProfile
import com.cpvsn.rm.core.features.task.angle.TaskAngle
import com.cpvsn.rm.core.features.task.approval.TaskLegalRequest
import com.cpvsn.rm.core.features.task.approval.cap.TaskCapLegalReview
import com.cpvsn.rm.core.features.task.approval.client.TaskClientLegalReview
import com.cpvsn.rm.core.features.task.arrange.TaskArrangement
import com.cpvsn.rm.core.features.task.ca.TaskCa
import com.cpvsn.rm.core.features.task.client_decline.TaskClientDecline
import com.cpvsn.rm.core.features.task.client_selection.ClientSelectionTagRef
import com.cpvsn.rm.core.features.task.constant.*
import com.cpvsn.rm.core.features.task.decline.TaskAdvisorDecline
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.lead_group.LeadGroup
import com.cpvsn.rm.core.features.task.review.TaskContactReview
import com.cpvsn.rm.core.features.task.task_communication.TaskCommunication
import com.cpvsn.rm.core.features.thirdparty.bcg.entity.BcgHubTask
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.CurrencyUtil
import com.fasterxml.jackson.annotation.JsonIgnore
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.time.Instant

@TableDefinition(
    indices = [
        // this is required by com.cpvsn.rm.portal.features.client_legal.CompliancePortalController.list_task API
        TableDefinition.IndexDefinition(
            columns = ["client_id"],
            TableDefinition.IndexType.INDEX
        ),
        TableDefinition.IndexDefinition(
            columns = ["tsid"],
            type = TableDefinition.IndexType.UNIQUE
        ),
    ]
)
class Task : RmEntity(),
    SoftDeletable,
    WithOptimisticLock,
    UserAuditing,
    MaybeHasNdbID,
    HasClient,
    HasTsId,
    Payable,
    EntityCloneable<Task>,
    HasClientContactId<Int?> {

    companion object : RmCompanion<Task>()

    /**
     * we keep this property for backward compatibility
     * it's considerably hard to remove all the reference to this property in front end.
     */
    @Deprecated("legacy property")
    @Column
    var sequence: Int = 0

    @Column
    @GeneratedTsId
    @ColumnDefinition(type = ColumnDefinitions.TSID)
    override var tsid: String? = null

    @Column
    var project_id: Int = 0

    @Column
    var angle_id: Int? = null

    @Column
    var rank: Int = 0

    @Column
    var client_portal_rank: Int? = null

    @Column
    var display_id: String? = null

    /**
     * automatic lock display id after a task is sent to client
     */
    @Column
    var display_id_locked: Boolean = false

    @Column
    var project_type: Project.Type = Project.Type.CONSULTATION

    @Column
    var project_sub_type: Project.SubType = project_type.available_sub_types.first()

    @Column
    var type: TaskType = TaskType.CONSULTATION

    // https://www.notion.so/capvision/Completing-Calls-Activity-Data-Point-0bca3b838cc24cc5884fedd64b2da926
    @Column
    var sub_type: String? = null

    @get:JsonIgnore
    @PatchFields(["sub_type"])
    var sub_type_enum: TaskSubType? by PropDelegates.enum_nullable(this::sub_type)

    @get:JsonIgnore
    @PatchFields(["sub_type"])
    var investmentBankTaskSubType: InvestmentBankTaskSubType? by PropDelegates.enum_nullable(this::sub_type)

    @Column
    var interview_type: InterviewType? = null

    /* advisor related */
    @Column
    var advisor_id: Int? = null

    @Column
    var willingness: TaskWillingnessType? = null

    /* client related */
    @Column
    override var client_id: Int? = null

    // currently we don't use this value (use compliance_approve_policy_jit instead)
    // the compliance_approve_policy_jit property value is subject to client.compliance_approve_policy
    // i.e. we don't need take snapshot for now. but we keep it for potentially future usage.
    @Column
    var compliance_approve_policy: ClientComplianceApprovePolicy? = null

    @Column
    override var client_contact_id: Int? = null

    @Column
    @JsonIgnore
    var client_custom_fields_json: String? = null

    /**
     * see com.cpvsn.rm.core.features.client.custom.ClientCustomFields
     */
    @PatchFields(["client_custom_fields_json"])
    var client_custom_fields: Map<String, Any?>? by PropDelegates.json_nullable(this::client_custom_fields_json)

    /* team related */
    @Column
    override var create_by_id: Int = 0

    override var update_by_id: Int = 0

    @Column
    var lead_uid: Int? = null

    @Column
    var lead_group_id: Int? = null

    @Column
    var support_uid: Int? = null

    @Column
    var start_time: Instant? = null

    @Column
    var end_time: Instant? = null

    @Column
    var request_cap_legal_approve_time: Instant? = null

    //region task-completion-bill (to be separate!)

    @Column
    var complete_time: Instant? = null

    /**
     * As tasks can be moved from projects to projects.
     * this property is no longer accurate.
     * Do not depends on its accuracy!
     */
    @Column
    var is_follow_up: Boolean = false

    @Column
    var client_hours: BigDecimal? = null

    @Column
    var client_duration: BigDecimal? = null

    @Column
    var discount_hours: BigDecimal? = null

    @Column
    var call_tracker_client_hours: BigDecimal? = null

    /**
     * Note that rate and rate_currency can only either be all null
     * or all not null!
     */
    @Column
    var rate: BigDecimal? = null

    @Column
    var rate_currency: String? = null

    @get: JsonIgnore
    @PatchFields(["rate_currency"])
    var rate_currency_enum: BuiltInCurrency? by PropDelegates.enum_nullable(this::rate_currency)

    /**
     * https://www.notion.so/capvision/Project-Client-Tasks-table-changes-aea277d93c0d4cda948d8f54b79fb91a
     * Warning: 这个client rate仅作KM参考用. 不要在收付款中使用
     */
    @Column
    var client_rate_usd: BigDecimal? = null

    @Column
    var senior_rate: BigDecimal? = null

    @Column
    var billing_remarks: String = ""

    @Column
    var billing_notes: String = ""

    @Column
    var has_overseas: Boolean? = null

    @Column
    var has_transcription: Boolean? = null

    @Column
    var has_translation: Boolean? = null

    @Column
    var has_in_person: Boolean? = null

    /**
     * 只当合同为project base合同时
     */
    @Column
    var charge_cash: BigDecimal? = null

    /**
     * https://www.notion.so/capvision/US-DB-Closing-out-calls-doesn-t-allow-for-client-charge-field-to-be-entered-075996a911ea49a8862edb6956e55f29
     * 有些task的计费就是要直接指定，不按合同计算
     */
    @Column
    var specified_charge_cash: BigDecimal? = null

    @Column
    var specified_charge_cash_currency: BuiltInCurrency? = null

    @Column
    var is_marked_completed: Boolean? = null

    @Column
    var marked_client_hour: BigDecimal? = null

    @Column
    var marked_billable_hour: BigDecimal? = null

    //endregion

    @Column
    var prohibit_complete_again: Boolean = false

    @Column(insertable = false, updatable = false)
    override var version: Int = 0

    @Column(updatable = false, insertable = false)
    override var delete_at: Instant? = null

    @Column
    override var ndb_id: Int? = null

    //region arrange
    @Column
    var arrange_bridge_type: BridgeType? = null

    @Column
    var arrange_detail_str: String? = null

    @Column
    var arrange_advisor_bridge_info_str: String? = null

    @Column
    var advisor_loopup_area_id: Int? = null

    @Column
    var contact_loopup_area_ids: String? = null

    @Column
    var arrange_advisor_calendar_location: String? = null

    @Column
    var arrange_contact_calendar_location: String? = null
    //endregion

    @Column
    var has_outreached: Boolean = false

    @Column
    var decipher_status: DecipherSurveyStatus? = null

    /**
     * https://www.notion.so/capvision/3-PRIORITY-Twilio-Compliance-setting-for-transcripts-and-recordings-1c7d544e85d340ddab7ee9afda499218
     * Require compliance review of Recordings and Transcripts before client analyst can access
     */
    @Column
    var asset_portal_link_approved: Boolean? = null

    //region statuses
    @Column
    var general_status: TaskStatus.General = TaskStatus.General.INITIAL

    /**
     * If a call is "failed" or "cancelled", we want to show that in the call tracker, but show the [general_status] elsewhere.
     * If this is not null, it overrides [general_status] in the call tracker
     */
    @Column
    var call_tracker_status: TaskStatus.CallTracker? = null

    @Column
    var ca_status: TaskStatus.CA = TaskStatus.CA.INITIAL

    @Column
    var sq_status: TaskStatus.SQ = TaskStatus.SQ.INITIAL

    @Column
    var advisor_schedule_status: TaskStatus.AdvisorSchedule =
        TaskStatus.AdvisorSchedule.INITIAL

    @Column
    var capvision_compliance_status: TaskStatus.CapvisionCompliance =
        TaskStatus.CapvisionCompliance.INITIAL

    @Column
    var client_compliance_status: TaskStatus.ClientCompliance =
        TaskStatus.ClientCompliance.INITIAL

    @Column
    var client_contact_status: TaskStatus.ClientContact =
        TaskStatus.ClientContact.INITIAL

    @Column
    var client_contact_schedule_status: TaskStatus.ClientContactSchedule =
        TaskStatus.ClientContactSchedule.INITIAL

    @Column
    var client_contact_feedback_status: TaskStatus.ClientContactFeedback =
        TaskStatus.ClientContactFeedback.INITIAL

    @Column
    var advisor_payment_confirm_status: TaskStatus.AdvisorPaymentConfirm =
        TaskStatus.AdvisorPaymentConfirm.INITIAL

    @Column
    var advisor_feedback_survey_status: TaskStatus.AdvisorFeedback =
        TaskStatus.AdvisorFeedback.INITIAL

    @Column
    var post_ca_status: TaskStatus.PostCa = TaskStatus.PostCa.INITIAL

    @Column
    var client_publish_status: TaskStatus.ClientPublish =
        TaskStatus.ClientPublish.INITIAL

    @Column
    var client_portal_status: TaskStatus.ClientPortal =
        TaskStatus.ClientPortal.INITIAL

    @Column
    var scheduling_status: TaskStatus.Scheduling = TaskStatus.Scheduling.INITIAL

    // related to the project subtype 'Investor Call'
    @Relation
    var investor_call: TaskInvestorCall? = null

    //endregion

    @Relation(backReference = "task_id")
    var task_outsource_info: TaskOutsourceInfo? = null

    /**
     * "Sort / View Outreach Message on Leads Tab: Within the Leads tab,
     * can we add a column to view the last message sent to the expert?
     * And have the ability to sort by this column?"
     * -- 2022.4.7
     *
     * this field map to CommunicationRecord.id
     */
    @Column
    var latest_outreach_record_id: Int? = null

    @Column
    override var clone_from_id: Int? = null

    @Column
    var screening_summary: String = ""

    @Column
    var screening_summary_update_time: Instant? = null

    @Column
    var creation_source: TaskCreationSource? = null

    // it means that the task is free for some reason such as 'expert was not a good fit','poor quality' and so on.
    @Column
    var not_charged: Boolean? = null

    @Column
    var not_charged_reason: String? = null

    @Column
    override var payment_topic_id: String? = null

    @Column
    var latest_task_communication_time: Instant? = null

    //https://www.notion.so/capvision/Kora-Capital-US-Compliance-Approval-Date-for-Usage-Report-e2eab3a7101e42d4a74cc6a95f1abef4
    @Column
    var compliance_approval_date: Instant? = null

    @Column
    var custom_sourced_override: Boolean? = null

    var advisor_answered_current_project_sq: Boolean? = null

    override fun to_payment_topic(): PaymentTopic {
        val task = this
        Task.joinOnce(
            task, Includes.setOf(
                Task::project,
                Task::client,
                Task::advisor,
                Task::lead,
                Task::support,
            )
        )
        return PaymentTopic {
            region = Region.current
            task_tsid = task.tsid_or_error
            project_tsid = task.project?.tsid_or_error
            project_name = task.project?.name
            client_tsid = task.client?.tsid_or_error
            client_name = task.client?.name
            task_start_time = task.start_time
            pm_email1 = task.lead?.email
            pm_email2 = task.support?.email
            detail_snapshot = PaymentTaskInfoSnapshotV1
                .take_snapshot(task)
        }
    }

    override fun get_or_create_payment_topic(): PaymentTopic {
        return if (!has_payment_topic) {
            val topic = PaymentTopic.save(to_payment_topic())
            Patch.fromMutator(this) {
                this.payment_topic_id = topic.id
            }.patch()
            this.payment_topic = topic
            this.includedProperties = this.includedProperties.plus(
                Includes.setOf(Referral::payment_topic)
            )
            topic
        } else {
            Task.joinOnce(
                this,
                Includes.setOf(Task::payment_topic)
            )
            requireNotNull(this.payment_topic)
        }
    }

    var db_page_url_jit: String? = null

    //region +

    // precondition: task.advisor joined.
    @get:JsonIgnore
    val rate_usd_approximate: BigDecimal?
        get() = rate?.let {
            CurrencyUtil.to_usd_approximate(it, rate_currency_enum!!)
        } ?: advisor?.let { advisor ->
            advisor.rate?.let {
                CurrencyUtil.to_usd_approximate(
                    it.BD,
                    advisor.rate_currency!!
                )
            }
        }

    @get:JsonIgnore
    val has_completed: Boolean
        get() = general_status == TaskStatus.General.COMPLETED

    @get:JsonIgnore
    val has_arranged: Boolean
        get() = general_status in setOf(
            TaskStatus.General.ARRANGED,
            TaskStatus.General.COMPLETED
        )

    @get:JsonIgnore
    val is_with_advisor: Boolean
        get() = type in setOf(TaskType.CONSULTATION, TaskType.ADVISOR_TASK)

    val tc_status: Advisor.TcStatus?
        get() = advisor?.tc_status

    // RequireExtra: advisor_schedules, contact_schedules
    val contains_matched_schedule: Boolean
        get() = this.let {
            it.advisor_schedules.orEmpty()
                .map { advisorSchedule -> advisorSchedule.to_range() }
                .any { advisorRange ->
                    it.contact_schedules.orEmpty()
                        .map { contactSchedule -> contactSchedule.to_range() }
                        .any { contactRange ->
                            contactRange != null && advisorRange != null && advisorRange.contains(
                                contactRange
                            )
                        }
                }
        }


    /**
     * bad design,
     * should add @JsonIgnore in future
     */
    var compliance_approve_policy_jit: ClientComplianceApprovePolicy? = null
    var compliance_passed_jit: Boolean? = null

    @JsonIgnore
    var require_pre_call_ca_jit: Boolean? = null

    @Relation
    override var clone_from: Task? = null

    @Relation(reference = "create_by_id")
    var create_user: User? = null

    @Relation(reference = "lead_uid")
    var lead: User? = null

    @Relation(reference = "lead_group_id")
    var lead_group: LeadGroup? = null

    @Relation(reference = "support_uid")
    var support: User? = null

    @Relation
    var advisor: Advisor? = null

    @Relation
    var project: Project? = null

    @Relation
    var client: Client? = null

    @Relation
    var client_contact: ClientContact? = null

    @Relation
    var events: List<TaskEvent>? = null

    @Relation(backReference = "task_id")
    var client_contact_reviews: List<TaskContactReview>? = null

    @Relation(backReference = "task_id")
    var client_compliance_reviews: List<TaskClientLegalReview>? = null

    @Relation(backReference = "task_id")
    var capvision_compliance_reviews: List<TaskCapLegalReview>? = null

    /**
     * all schedules related to this task
     */
    @Relation(backReference = "task_id")
    var schedules: List<Schedule>? = null

    /**
     * schedules submitted by project manager
     */
    var schedule: Schedule? = null

    /**
     * schedules submitted by advisor
     */
    var advisor_schedules: List<Schedule>? = null

    var advisor_schedules_with_arranged: List<Schedule>? = null

    var advisor_schedules_after_current: List<Schedule>? = null

    var non_conflicting_advisor_schedules_after_current: List<Schedule>? = null

    /**
     * schedules submitted by contact
     */
    var contact_schedules: List<Schedule>? = null

    /**
     * task COMMON arrangement, which is different from vetting call arrangement
     */
    // "should rename as task.common_arrangement"
    var arrangement: TaskArrangement? = null

    var vetting_call_arrangement: TaskArrangement? = null

    @Relation
    var advisor_payments: List<LocalAdvisorPayment>? = null

    var advisor_normal_payment: LocalAdvisorPayment? = null

    @Relation
    var payment_topic: PaymentTopic? = null

    var latest_submitted_sq: InquiryInstance? = null

    var sq_instances: List<InquiryInstance>? = null

    var latest_sq: InquiryInstance? = null

    /**
     * I think this extra property is error prone.
     * I'm considering add @JsonIgnore or simply remove this property
     * Currently keep for backward compatibility
     */
    var pre_ca_list: List<TaskCa>? = null

    var post_ca_list: List<TaskCa>? = null

    /**
     * I think this extra property is error prone.
     * I'm considering add @JsonIgnore or simply remove this property
     * Currently keep for backward compatibility
     */
    var latest_pre_ca: TaskCa? = null

    var latest_submitted_pre_ca: TaskCa? = null

    var latest_submitted_post_ca: TaskCa? = null

    var bound_contracts: List<Contract>? = null

    @Relation(backReference = "task_id")
    var revenues: List<Revenue>? = null

    var has_billed_jit: Boolean? = null

    /**
     * use Task.rate or TaskAngle.rate or Project.rate
     *
     * it is supposed to be used in sever side only
     * we set
     * rate = default_rate_jit
     * rate_currency = default_rate_currency_jit
     * whenever necessary
     */
    @JsonIgnore
    var default_rate_jit: BigDecimal? = null

    /**
     * similar logic as rate_jit
     *
     * it is supposed to be used in sever side only
     * we set
     * rate = default_rate_jit
     * rate_currency = default_rate_currency_jit
     * whenever necessary
     */
    @JsonIgnore
    var default_rate_currency_jit: String? = null

    var specified_client_rate_jit: BigDecimal? = null

    var specified_client_rate_currency_jit: BuiltInCurrency? = null

    var specified_client_rate_currency_after_multiple: BigDecimal? = null

    /**
     * historically, this property is called email_records
     * a more suitable name would be 'communication_records'
     */
    @Relation(backReference = "task_id")
    var email_records: List<CommunicationRecord>? = null

    /**
     * Takes the [Task.email_records] and [Task.portal_advisor_records] lists, filters out irrelevant and redundant entries,
     * compiles them into one list, and sorts that list.
     * It filters out emails that don't have an advisor_id or that does have a client_contact_id to get rid of client communications.
     * Filters out advisor_records that appear in the email lists' [CommunicationRecord.advisor_portal] field to get rid of duplicates.
     * Set in [com.cpvsn.rm.core.features.task.TaskRepository.handleIncludeProperty] by [com.cpvsn.rm.core.features.task.TaskRepository.get_task_communications]
     */
    var task_communication: List<TaskCommunication>? = null


    @Relation
    var latest_outreach_record: CommunicationRecord? = null

    @Relation
    var advisor_decline: TaskAdvisorDecline? = null

    @Relation(backReference = "task_id")
    var advisor_record_consent: TaskAdvisorRecordConsent? = null

    @Relation(backReference = "task_id")
    var client_decline: TaskClientDecline? = null

    var survey_receivable: ReceivableTransaction? = null

    //region loopup related
    @get:JsonIgnore
    val loopup_number_ids: Set<Int>
        get() {
            val tmp = contact_loopup_area_ids?.split(",")
                ?.filter { it.isNotBlank() }
                ?.map { it.toInt() }
                .orEmpty()
            return listOfNotNull(advisor_loopup_area_id, *tmp.toTypedArray()).toSet()
        }


    var loopup_numbers: List<LoopupNumber>? = null

    var loopup_room: LoopupRoom? = null

    var loopup_dial_in_for_advisor: String? = null

    var loopup_dial_in_for_contact: String? = null

    //endregion

    @Relation
    var angle: TaskAngle? = null

    @Relation(backReference = "task_id")
    var advisor_profile: TaskAdvisorProfile? = null

    var advisor_blocked_status: Set<ClientBlacklist.BlockedType>? = null

    /**
     * 最近申请 Capvision 法务审批的 User
     */
    var latest_request_cap_approval_user: User? = null


    var senior_rate_multiplier: BigDecimal? = null
    var default_client_rate_usd: BigDecimal? = null

    /**
     * https://www.notion.so/capvision/Client-Rate-Client-tasks-and-to-client-value-is-null-86c726d4e26d424ab5a7b731176624ac
     */
    var senior_rate_multiplier_considering_unsigned_tc: BigDecimal? = null
    var default_client_rate_usd_considering_unsigned_tc: BigDecimal? = null

    var given_senior_rate_multiplier: BigDecimal? = null

    /**
     * 发给客户的（to-client邮件，下载的excel， client portal上）
     */
    var standard_rate_multiplier_display_to_client: BigDecimal? = null

    @Relation
    var client_selection_tag_refs: List<ClientSelectionTagRef>? = null

    var client_publish_log: List<EntityUpdateLog>? = null

    var entity_update_logs: List<EntityUpdateLog>? = null

    @Relation(backReference = "task_id")
    var legal_request: TaskLegalRequest? = null
    //endregion

    @Relation
    var more_info_requests: List<MoreInfoRequestRecord>? = null

    var advisor_completed_task_count: Int? = null

    @Relation(backReference = "task_id")
    var task_legal_snapshot: TaskLegalSnapshot? = null

    @Relation(backReference = "task_id")
    var task_client_chaperones: List<TaskClientChaperone>? = null

    @Relation(backReference = "task_id")
    var bcg_hub_task: BcgHubTask? = null

    /**
     * Text blurbs that are displayed on the call tracker page.
     */
    @Relation(backReference = "task_id")
    var call_track_notes: List<CallTrackNote>? = null

    // used to display in project P&L tab
    var p_l_profit: BigDecimal? = null

    @Relation(reference = "advisor_id", backReference = "advisor_id")
    var advisor_physician_board_certifications: List<AdvisorPhysicianBoardCertification>? = null

    val task_outsource_status: TaskOutsourceType
        get() {
            if (task_outsource_info == null) {
                return TaskOutsourceType.NOT_OUTSOURCE
            } else if (task_outsource_info?.outsource_advisor_to == Region.current) {
                return TaskOutsourceType.IMPORTED
            } else if (task_outsource_info?.outsource_advisor_from == Region.current) {
                return if (task_outsource_info?.outsource_advisor_to == null) {
                    TaskOutsourceType.READY_TO_EXPORT
                } else {
                    TaskOutsourceType.EXPORTED
                }
            }
            return TaskOutsourceType.ILLEGAL
        }

    /**
     * Retrieved from Task.List when includes has is_custom_sourced
     * @see com.cpvsn.rm.core.features.advisor.custom_source.AdvisorCustomSourceService.is_custom_sourced_bulk
     */
    var is_custom_sourced: Boolean? = null

    /**
     * these properties especially useful while we migrate
     * sendgrid to another email vendor, after the migration process
     * we should consider remove them.
     *
     * Thanks Chris, Ping; we can work with this for now. However, the one thing we
     * would need is a way to access T&C and unsubscribe links for experts via
     * excel-export from angles/leads. Adding 2 new columns that have both of
     * these as part of the export would help.
     *
     * Thank you Ping - appreciate it. To be sure we are align including some extra info.
     * On survey projects the link we'd need in the export would be the same as
     * currently in the 'copy link' field (screenshot below); which re-directs
     * to T&Cs and then survey link. For Research its simply the T&C link for
     * that individual expert. The unsubscribe link would be the same for both.
     *
     * Sorry 1 last thing that we failed to flag yesterday. Can we add the
     * 'decline project' link to all exports as a seperate column as well?
     *
     * -- Jasper Salas Groth
     */
    @JsonIgnore
    var latest_or_create_survey_portal: Portal? = null

    @JsonIgnore
    var latest_or_create_unsubscribe_outreach_portal: Portal? = null

    @JsonIgnore
    var latest_or_create_decline_portal: Portal? = null

    @Mapper
    @Component
    interface MybatisMapper : BaseMapper<Task>, AggregationMapper {

        @Select(
            """
            SELECT CASE
                WHEN max(t.rank) IS NULL THEN 1
                ELSE max(t.rank) + 1 END
            FROM task t 
            WHERE t.project_id = #{project_id}
            AND t.angle_id is null
            and t.delete_at = '0000-00-00 00:00:00'
        """
        )
        fun autoinc_rank(
            @Param("project_id") project_id: Int,
        ): Int

        @Select(
            """
                <script>
                SELECT project_id, COUNT(sq_status = 'RESPONDED' or null) as screened_count,
                    COUNT(general_status in ('SCHEDULED','ARRANGED','COMPLETED') or null) as scheduled_count
                FROM task 
                WHERE project_id IN 
                    <foreach collection='project_ids' item='item' open='(' separator=',' close=')'>
                        #{item}
                    </foreach>
                    AND angle_id is not null
                    AND delete_at = 0
                    AND (sq_status = 'RESPONDED' OR general_status IN ('SCHEDULED','ARRANGED','COMPLETED'))
                GROUP BY project_id       
                </script>
            """
        )
        fun project_screened_scheduled_count(
            @Param("project_ids") project_ids: Set<Int>,
        ): List<ProjectScreenedScheduledCount>

        @Select(
            """
                <script>
                SELECT angle_id, COUNT(*) AS count
                FROM task
                WHERE project_id IN 
                    <foreach collection='project_ids' item='item' open='(' separator=',' close=')'>
                        #{item}
                    </foreach>
                    AND angle_id is not null
                    AND delete_at = 0
                GROUP BY angle_id    
                </script>    
            """
        )
        fun angle_expert_count(
            @Param("project_ids") project_ids: Set<Int>,
        ): List<AngleExpertCount>

    }

    /**
     * custom sort to implement
     * https://www.notion.so/capvision/Client-Tasks-and-Leads-Opened-a6f7d30179574aa1bb262bebd5b46a62
     */
    @CustomSorts(
        // prefer desc
        CustomSorts.Key(
            name = "outreach_opened",
            expr = "MAX({this.outreach_records.advisor_tracking}.status in ('OPEN','CLICK'))"
        ),
        // prefer desc
        CustomSorts.Key(
            name = "outreach_opened_at",
            expr = "MAX({this.outreach_records.advisor_tracking}.opened_at)"
        ),
        CustomSorts.Key(
            name = "outreach_tracking_status",
            expr = "MAX({this.outreach_records.advisor_tracking}.status)"
        ),
        // prefer desc
        CustomSorts.Key(
            name = "outreach_tracking_update_at",
            expr = "MAX({this.outreach_records.advisor_tracking}.update_at)"
        ),

        // legal request sort
        // todo: remove to
        CustomSorts.Key(
            name = "legal_request_overdue",
            expr = "MAX({this.legal_request}.expect_schedule_date < current_date())"
        ),
        CustomSorts.Key(
            name = "legal_request_is_null",
            expr = "MAX({this.legal_request}.id is null)"
        ),
        CustomSorts.Key(
            name = "legal_request_expect_schedule_date_is_null",
            expr = "MAX({this.legal_request}.expect_schedule_date is null)"
        ),

        CustomSorts.Key(
            name = "conference_start_at",
            expr = "ifnull({this.arrangement.zoom_meeting}.start_time, {this.arrangement.twilio_voice_conference}.expected_start_time)"
        ),
    )
    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.IsNull
        val id_is_null: Boolean? = null,
        @Criteria.NotIn
        val id_not_in: Set<Int>? = null,
        @Criteria.Contains
        val id_contains: String? = null,
        @Criteria.Expr("{this}.id like concat(#{value},'%')")
        val id_starts_with: String? = null,

        @Criteria.Eq
        val ndb_id: Int? = null,
        @Criteria.IdsIn
        val ndb_ids: Set<Int>? = null,

        @Criteria.Eq
        val tsid: String? = null,
        @Criteria.Ne
        val tsid_ne: String? = null,
        @Criteria.In
        val tsid_in: Set<String>? = null,
        @Criteria.Expr("({this}.tsid is null or {this}.tsid = '') = #{value}")
        val tsid_is_null_or_empty: Boolean? = null,

        @Criteria.Eq
        val type: TaskType? = null,
        @Criteria.In
        val type_in: Set<TaskType>? = null,
        @Criteria.Eq
        val sub_type: String? = null,
        @Criteria.In
        val sub_type_in: Set<String>? = null,

        @Criteria.In
        val project_type_in: Set<Project.Type>? = null,
        @Criteria.NotIn
        val project_type_not_in: Set<Project.Type>? = null,

        @Criteria.Eq
        val project_sub_type: Project.SubType? = null,
        @Criteria.In
        val project_sub_type_in: Set<Project.SubType>? = null,

        @Criteria.Eq
        val arrange_bridge_type: BridgeType? = null,
        @Criteria.In
        val arrange_bridge_type_in: Set<BridgeType>? = null,

        @Criteria.Eq
        val interview_type: InterviewType? = null,

        @Criteria.Eq
        val display_id_locked: Boolean? = null,
        @Criteria.Eq
        val display_id: String? = null,

        @Criteria.Expr("({this.advisor}.sourced_project_id = {this}.project_id) = #{value}")
        val is_custom_sourced: Boolean? = null,

        @Criteria.Expr("{this.advisor}.full_name LIKE concat('%',#{value},'%') OR {this.angle}.name LIKE concat('%',#{value},'%') ")
        val keyword: String? = null,

        @Criteria.Eq
        val angle_id: Int? = null,
        @Criteria.Ne
        val angle_id_ne: Int? = null,
        @Criteria.Eq
        val lead_group_id: Int? = null,
        @Criteria.Ne
        val lead_group_id_ne: Int? = null,
        @Criteria.IsNull
        val lead_group_id_is_null: Boolean? = null,
        @Criteria.IdsIn
        val angle_ids: Set<Int>? = null,
        @Criteria.IsNull
        val angle_id_is_null: Boolean? = null,
        @Criteria.Expr("{this.angle}.name in ({value})")
        val angle_names: Set<String>? = null,

        @Criteria.IsNull
        val client_rate_usd_is_null: Boolean? = null,

        @Criteria.Gt
        val rank_gt: Int? = null,

        @Criteria.Gte("start_time")
        val start_time_gte: Instant? = null,
        @Criteria.Lte("start_time")
        val start_time_lte: Instant? = null,
        @Criteria.Gte("end_time")
        val end_time_gte: Instant? = null,
        @Criteria.Lte("end_time")
        val end_time_lte: Instant? = null,

        @Criteria.Eq
        val project_id: Int? = null,
        @Criteria.IdsIn
        val project_ids: Set<Int>? = null,

        @Criteria.Eq
        val client_id: Int? = null,
        @Criteria.IdsIn
        val client_ids: Set<Int>? = null,

        @Criteria.Eq
        val advisor_id: Int? = null,
        @Criteria.IdsIn
        val advisor_ids: Set<Int>? = null,

        @Criteria.Eq(columnName = "support_uid")
        val support_id: Int? = null,
        @Criteria.IdsIn(columnName = "support_uid")
        val support_ids: Set<Int>? = null,
        @Criteria.Eq(columnName = "lead_uid")
        val lead_id: Int? = null,
        @Criteria.IdsIn(columnName = "lead_uid")
        val lead_ids: Set<Int>? = null,
        @Criteria.Expr("{this}.lead_uid = #{value} or {this}.support_uid = #{value}")
        val lead_or_support_id: Int? = null,

        @Criteria.Expr(expr = "({this}.screening_summary = '') = #{value}")
        val screening_summary_is_blank: Boolean? = null,

        @Criteria.Eq
        var client_contact_id: Int? = null,
        @Criteria.IdsIn
        var client_contact_ids: Set<Int>? = null,

        @Criteria.Eq
        var create_by_id: Int? = null,
        @Criteria.IdsIn
        var create_by_ids: Set<Int>? = null,
        @Criteria.Eq
        var update_by_id: Int? = null,

        @Criteria.Gte
        val create_at_gte: Instant? = null,
        @Criteria.Lte
        val create_at_lte: Instant? = null,

        @Criteria.Gte
        val complete_time_gte: Instant? = null,
        @Criteria.Lte
        val complete_time_lte: Instant? = null,

        @Criteria.Eq
        val general_status: TaskStatus.General? = null,
        @Criteria.Ne
        val general_status_ne: TaskStatus.General? = null,
        @Criteria.In
        val general_status_in: Set<TaskStatus.General>? = null,
        @Criteria.NotIn
        val general_status_not_in: Set<TaskStatus.General>? = null,

        @Criteria.Eq
        val call_tracker_status: TaskStatus.CallTracker? = null,
        @Criteria.In
        val call_tracker_status_in: Set<TaskStatus.CallTracker>? = null,
        @Criteria.IsNull
        val call_tracker_status_is_null: Boolean? = null,

        @Criteria.Eq
        val ca_status: TaskStatus.CA? = null,
        @Criteria.Eq
        val sq_status: TaskStatus.SQ? = null,
        @Criteria.Ne
        val sq_status_ne: TaskStatus.SQ? = null,
        @Criteria.In
        val sq_status_in: Set<TaskStatus.SQ>? = null,
        @Criteria.Eq
        val advisor_schedule_status: TaskStatus.AdvisorSchedule? = null,

        @Criteria.Eq
        val capvision_compliance_status: TaskStatus.CapvisionCompliance? = null,
        @Criteria.In
        val capvision_compliance_status_in: Set<TaskStatus.CapvisionCompliance>? = null,
        @Criteria.NotIn
        val capvision_compliance_status_not_in: Set<TaskStatus.CapvisionCompliance>? = null,

        @Criteria.Eq
        val client_compliance_status: TaskStatus.ClientCompliance? = null,
        @Criteria.In
        val client_compliance_status_in: Set<TaskStatus.ClientCompliance>? = null,
        @Criteria.NotIn
        val client_compliance_status_not_in: Set<TaskStatus.ClientCompliance>? = null,

        @Criteria.Eq
        val client_contact_status: TaskStatus.ClientContact? = null,
        @Criteria.In
        val client_contact_status_in: Set<TaskStatus.ClientContact>? = null,

        @Criteria.Eq
        val client_contact_schedule_status: TaskStatus.ClientContactSchedule? = null,
        @Criteria.In
        val client_contact_schedule_status_ne: TaskStatus.ClientContact? = null,

        @Criteria.Eq
        val client_contact_feedback_status: TaskStatus.ClientContactFeedback? = null,

        @Criteria.Eq
        val client_publish_status: TaskStatus.ClientPublish? = null,
        @Criteria.Ne
        val client_publish_status_ne: TaskStatus.ClientPublish? = null,

        @Criteria.Eq
        val client_portal_status: TaskStatus.ClientPortal? = null,
        @Criteria.Ne
        val client_portal_status_ne: TaskStatus.ClientPortal? = null,
        @Criteria.In
        val client_portal_status_in: Set<TaskStatus.ClientPortal>? = null,

        @Criteria.Gte
        val client_hours_gte: BigDecimal? = null,
        @Criteria.Lte
        val client_hours_lte: BigDecimal? = null,

        @Criteria.Eq
        val scheduling_status: TaskStatus.Scheduling? = null,
        @Criteria.Ne
        val scheduling_status_ne: TaskStatus.Scheduling? = null,
        @Criteria.In
        val scheduling_status_in: Set<TaskStatus.Scheduling>? = null,

        @Criteria.Eq
        val creation_source: TaskCreationSource? = null,

        @Criteria.Eq
        val asset_portal_link_approved: Boolean? = null,

        @Criteria.Eq
        val willingness: String? = null,

        @Criteria.Eq
        val decipher_status: DecipherSurveyStatus? = null,
        @Criteria.In
        val decipher_status_in: Set<DecipherSurveyStatus>? = null,

        @Criteria.IsNull(columnName = "client_custom_fields_json")
        val client_custom_fields_json_is_null: Boolean? = null,

        @Criteria.Gte
        val latest_task_communication_time_gte: Instant? = null,
        @Criteria.Lte
        val latest_task_communication_time_lte: Instant? = null,

        @Criteria.Join
        val client: Client.Query? = null,
        @Criteria.Join
        val advisor: Advisor.Query? = null,
        @Criteria.Join
        val advisor_profile: TaskAdvisorProfile.Query? = null,
        @Criteria.Join(on = "{that}.task_id = {this}.id and {that}.creator_type = 'PM'")
        val schedule: Schedule.Query? = null,
        @Criteria.Join(on = "{that}.task_id = {this}.id and {that}.creator_type = 'CLIENT_CONTACT'")
        val contact_schedule: Schedule.Query? = null,

        /**
         * when we search available slots
         * we can use all slots provided by advisors, not limit to this project
         */
        @Criteria.Join(
            on = """
                {this}.advisor_id = {that}.advisor_id 
                and {that}.creator_type = 'ADVISOR'
                and {that}.start_time > NOW()
                """
        )
        val advisor_available_slots: Schedule.Query? = null,
        /**
         * when we search expired slots
         * we only need to show slots relevant to this project.
         */
        @Criteria.Join(
            on = """
                {this}.advisor_id = {that}.advisor_id 
                and {this}.project_id = {that}.project_id 
                and {that}.creator_type = 'ADVISOR' 
                and {that}.start_time <= NOW()"""
        )
        val advisor_expired_slots: Schedule.Query? = null,

        @Criteria.Join
        val create_user: User.Query? = null,
        @Criteria.Join
        val support: User.Query? = null,
        @Criteria.Join
        val project: Project.Query? = null,
        @Criteria.Join
        val angle: TaskAngle.Query? = null,
        @Criteria.Join
        val events: TaskEvent.Query? = null,

        @Criteria.Join(on = "{this}.id={that}.task_id and {that}.type='CONTACT_SEND'")
        val sent_to_contact_events: TaskEvent.Query? = null,
        @Criteria.Join(on = "{this}.id={that}.task_id and {that}.type='CONTACT_APPROVE'")
        val contact_selected_events: TaskEvent.Query? = null,

        @Criteria.Join
        val client_selection_tag_refs: ClientSelectionTagRef.Query? = null,
        @Criteria.Join(on = "{that}.task_id = {this}.id and {that}.email_content_type = 'PROJECT_OUTREACH'")
        val outreach_records: CommunicationRecord.Query? = null,
        @Criteria.Join
        val latest_outreach_record: CommunicationRecord.Query? = null,
        @Criteria.Join
        val advisor_decline: TaskAdvisorDecline.Query? = null,
        @Criteria.Join
        val advisor_record_consent: TaskAdvisorRecordConsent.Query? = null,
        @Criteria.Join(on = "{that}.task_id = {this}.id and {that}.type = 'COMMON'")
        val arrangement: TaskArrangement.Query? = null,
        @Criteria.Join(on = "{that}.task_id = {this}.id and {that}.type = 'VETTING_CALL'")
        val vetting_call_arrangement: TaskArrangement.Query? = null,

        @Criteria.Join
        val legal_request: TaskLegalRequest.Query? = null,

        /**
         * 使用一些计算出来的值作为查询的过滤条件时，
         * 比如：查询task列表时，在常见的Task.Query包含的一系列条件外，加入条件"距离该User上次的task不超过2个月"
         *      查询client列表时，条件"最近三个月用量超过30 hours"
         * 要么：可以在sql中算到该值，则可以考虑写入sql
         * 要么：sql中忽略该条件、分页参数，查询完成后通过代码进一步过滤并处理分页
         */
        @Criteria.Join(
            on = """
                {this}.id = {that}.task_id
                AND {that}.type = 'CAPVISION_COMPLIANCE_SEND'
                AND {that}.create_at = (
                        SELECT MAX(_te0.create_at) 
                        from task_event _te0 
                        where _te0.task_id = {this}.id and _te0.type = 'CAPVISION_COMPLIANCE_SEND'
                      )
        """
        )
        val latest_cap_compliance_send: TaskEvent.Query? = null,

        @Criteria.Join(
            on = """
                {this}.id = {that}.task_id
                AND {that}.type = 'CONTACT_SEND'
                AND {that}.create_at = (
                        SELECT MAX(_task_event_1.create_at) 
                        from task_event _task_event_1 
                        where _task_event_1.task_id = {this}.id and _task_event_1.type = 'CONTACT_SEND'
                      )
        """
        )
        val latest_contact_send: TaskEvent.Query? = null,

        @Criteria.Join(
            on = """
                {this}.id = {that}.task_id
                AND {that}.type = 'PRE_CALL'
                AND {that}.revoke_time is null
                AND {that}.submit_time = (
                        SELECT MAX(_task_ca.submit_time) 
                        from task_ca _task_ca 
                        where _task_ca.task_id = {this}.id and _task_ca.type = 'PRE_CALL' and _task_ca.submit_time is not null
                      )
        """
        )
        val latest_submitted_pre_ca: TaskCa.Query? = null,
        @Criteria.Join
        val investor_call: TaskInvestorCall.Query? = null,
        override val includeSoftDeletedRecords: Boolean = false,
        override val aggregation: Set<String>? = null,
        @Criteria.Or
        val or: List<Query>? = null,
        @Criteria.And
        val and: List<Query>? = null,
        @Criteria.Join
        var more_info_requests: MoreInfoRequestRecord.Query? = null,
        @Criteria.Join(
            on = """
                {this}.id = {that}.source_id
                AND {that}.create_type = 'BEFORE_TASK'
                AND {that}.inquiry_type = 'SCREENING'
        """
        )
        var sq_instance: InquiryInstance.Query? = null,
    ) : BaseQuery<Task>(), AggregationQuery
}
