package com.cpvsn.rm.core.features.task

import com.cpvsn.rm.core.base.pojo.PageSearch
import com.cpvsn.rm.core.features.task.pojo.TaskComplianceStatusCountResult
import org.apache.ibatis.annotations.Mapper
import org.springframework.stereotype.Component

@Mapper
@Component
interface TaskMapper {
    fun task_count_by_client_and_capvision_compliance_status(
        query: PageSearch
    ): List<TaskComplianceStatusCountResult>
}