package com.cpvsn.rm.core.features.task

import com.cpvsn.bridge.sdk.router.RouterApiClients
import com.cpvsn.bridge.sdk.shared.CompleteTask
import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.extension.BD
import com.cpvsn.core.util.extension.assert_exist
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.crud.spring.util.ext.explicitFields
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.extensions.contains_any
import com.cpvsn.rm.core.extensions.user_id
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.AdvisorService
import com.cpvsn.rm.core.features.advisor.bank_account.AdvisorBankAccount
import com.cpvsn.rm.core.features.advisor.w9.AdvisorW9FormService
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.bridge.Region.Companion.to_outsource_region
import com.cpvsn.rm.core.features.client.ClientPreference
import com.cpvsn.rm.core.features.contract.ContractCalcService
import com.cpvsn.rm.core.features.contract.ContractService
import com.cpvsn.rm.core.features.contract.PayWay
import com.cpvsn.rm.core.features.contract.entity.Contract
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.finance.advisor_payment.AdvisorPaymentContext
import com.cpvsn.rm.core.features.finance.advisor_payment.local.LocalAdvisorPaymentConfirmService
import com.cpvsn.rm.core.features.finance.advisor_payment.local.LocalAdvisorPaymentCreationService
import com.cpvsn.rm.core.features.finance.payment.PaymentFormService
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.finance.revenue.RevenueService
import com.cpvsn.rm.core.features.inquiry.InquiryInstanceService
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecordService
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.misc.loopup.LoopUpService
import com.cpvsn.rm.core.features.misc.loopup.LoopupRoom
import com.cpvsn.rm.core.features.misc.schedule.CalendarService
import com.cpvsn.rm.core.features.misc.schedule.ScheduleRequest
import com.cpvsn.rm.core.features.misc.schedule.ScheduleService
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.clientcontact.PortalClientContactService
import com.cpvsn.rm.core.features.portal.clientcontact.PortalContactRequest
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.transaction.ReceivableTransaction
import com.cpvsn.rm.core.features.task.arrange.TaskTwilioConferenceService
import com.cpvsn.rm.core.features.task.client_decline.TaskClientDecline
import com.cpvsn.rm.core.features.task.client_decline.TaskClientDeclineService
import com.cpvsn.rm.core.features.task.constant.TaskOutsourceType
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.task.constant.TaskType
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.pojo.TaskCompleteRequest
import com.cpvsn.rm.core.features.task.pojo.TaskSchedulingRequest
import com.cpvsn.rm.core.features.task.pojo.UpdateClientPortalStatusRequest
import com.cpvsn.rm.core.features.thirdparty.bcg.BcgHubHelper
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.BcgExpertHubEvent
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.interop.ShareTasksStatusRequest
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.features.user.UserService
import com.cpvsn.rm.core.util.*
import com.cpvsn.web.auth.AuthContext
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.support.TransactionTemplate
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Instant
import java.time.temporal.ChronoUnit

@Service
class TaskService {

    //region @
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var inquiryInstanceService: InquiryInstanceService

    @Autowired
    private lateinit var scheduleService: ScheduleService

    @Autowired
    private lateinit var contractService: ContractService

    @Autowired
    private lateinit var contractCalcService: ContractCalcService

    @Autowired
    private lateinit var advisorService: AdvisorService

    @Autowired
    private lateinit var calendarService: CalendarService

    @Autowired
    private lateinit var taskEmailRecordService: CommunicationRecordService

    @Autowired
    private lateinit var taskToAdvisorService: TaskToAdvisorService

    @Autowired
    private lateinit var taskEventService: TaskEventService

    @Autowired
    private lateinit var portalClientContactService: PortalClientContactService

    @Autowired
    private lateinit var loopUpService: LoopUpService

    @Autowired
    private lateinit var localAdvisorPaymentCreationService: LocalAdvisorPaymentCreationService

    @Autowired
    private lateinit var localAdvisorPaymentConfirmService: LocalAdvisorPaymentConfirmService

    @Autowired
    private lateinit var taskPaymentService: TaskPaymentService

    @Autowired
    private lateinit var paymentFormService: PaymentFormService

    @Autowired
    private lateinit var advisorW9FormService: AdvisorW9FormService

    @Autowired
    private lateinit var taskDisplayIdService: TaskDisplayIdService

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var taskClientDeclineService: TaskClientDeclineService

    // for declarative transaction management
    // do not remove this property unless you are fully aware of its effects
    @Autowired
    private lateinit var self: TaskService

    @Autowired
    private lateinit var taskTwilioConferenceService: TaskTwilioConferenceService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var taskRepository: TaskRepository

    @Autowired
    private lateinit var userService: UserService

    @Autowired
    private lateinit var routerApiClient: RouterApiClients

    @Autowired
    private lateinit var bcgHubHelper: BcgHubHelper

    @Autowired
    private lateinit var revenueService: RevenueService
    //endregion

    fun remove_batch(ids: Set<Int>, reorder_affected_angles: Boolean = true) {
        transactionTemplate.extExecute {
            val tasks = Task.findAll(Task.Query(ids = ids))
            tasks.forEach { task ->
                biz_require(task.client_contact_status == TaskStatus.ClientContact.INITIAL) {
                    "cannot delete task with id (${task.id}), because it has been sent to client contact"
                }
                biz_require(task.client_compliance_status == TaskStatus.ClientCompliance.INITIAL) {
                    "cannot delete task with id (${task.id}), because it has been sent to client compliance"
                }
                biz_require(!task.has_completed) {
                    "task cannot be removed after completed"
                }
                if (task.has_arranged)
                    loopUpService.release_room(LoopupRoom.Query(task_id = task.id))

                Task.delete(task)
            }

            if (reorder_affected_angles) {
                val affected_angle_ids = tasks.ids(Task::angle_id)
                    .filter { it.is_valid_id }
                taskDisplayIdService.reorder_angles_task_rank(affected_angle_ids)
            }
        }
    }

    fun remove(id: Int) {
        remove_batch(setOf(id))
    }

    @Transactional
    fun patch_cascade(
        patch: Patch<Task>,
        include: Set<String>,
        cascades: Set<Cascade<Task, *>> = setOf(
            Cascade.oneToOne(
                Task::investor_call,
            ),
            Cascade.oneToMany(
                Task::task_client_chaperones,
                callback = Cascade.Callback.prePersist { entity, parent ->
                    entity.task_id = parent.id
                }
            )
        ),
    ): Task {
        val fields = patch.fields.toMutableSet()
        /**
         * If the patch changes the client hours or custom sourced status, we automatically create a call tracker note
         * that describes the old & new values and updating user.
         */
        if (patch.fields.contains_any(
                Task::custom_sourced_override.name,
                Task::call_tracker_client_hours.name,
                Task::lead_uid.name,
                Task::support_uid.name,
                Task::call_tracker_status.name
            ) &&
            patch.entity.id != 0
        ) {
            val task = Task.get(patch.entity.id, include = setOf(Task::is_custom_sourced.name))
            /**
             * If a call is canceled or fails, the user will set the call_tracker_status to FAILED or CANCELED.
             * In that case, we want to log it and set Client Hours to 0, since they won't count anymore.
             * If a call was marked canceled or failed but that mark is removed, then we want to log it and set Client Hours to what they were before by setting `call_tracker_client_hours` to null.
             * Setting `call_tracker_client_hours` to null means that Client Hours in the call tracker will follow the normal logic.
             */
            if (patch.fields.contains(Task::call_tracker_status.name) && task.call_tracker_status != patch.entity.call_tracker_status) {
                if (task.call_tracker_status == null) {
                    CallTrackNote().apply {
                        this.task_id = patch.entity.id
                        this.note = CallTrackNote.AutomaticNote.FAIL_OR_CANCEL.message(
                            User.get(AuthContext.user_id),
                            task.general_status.name,
                            patch.entity.call_tracker_status.toString()
                        )
                        this.type = CallTrackNote.Type.SYSTEM
                    }.save()
                    patch.entity.call_tracker_client_hours = BigDecimal.ZERO
                    fields.add(Task::call_tracker_client_hours.name)
                } else if (patch.entity.call_tracker_status == null) {
                    CallTrackNote().apply {
                        this.task_id = patch.entity.id
                        this.note = CallTrackNote.AutomaticNote.REVERT_FAIL_OR_CANCEL.message(
                            User.get(AuthContext.user_id),
                            task.call_tracker_status.toString(),
                            task.general_status.name
                        )
                        this.type = CallTrackNote.Type.SYSTEM
                    }.save()
                    patch.entity.call_tracker_client_hours = null
                    fields.add(Task::call_tracker_client_hours.name)
                }
            }

            if (task.custom_sourced_override != patch.entity.custom_sourced_override &&
                patch.fields.contains(Task::custom_sourced_override.name)
            ) {
                CallTrackNote().apply {
                    this.task_id = patch.entity.id
                    this.note = CallTrackNote.AutomaticNote.CUSTOM_SOURCE_OVERRIDE.message(
                        User.get(AuthContext.user_id),
                        task.is_custom_sourced.toString(),
                        patch.entity.custom_sourced_override.toString()
                    )
                    this.type = CallTrackNote.Type.SYSTEM
                }.save()
            }

            if (task.call_tracker_client_hours != patch.entity.call_tracker_client_hours &&
                patch.fields.contains(Task::call_tracker_client_hours.name)
            ) {
                CallTrackNote().apply {
                    this.task_id = patch.entity.id
                    this.note = CallTrackNote.AutomaticNote.CLIENT_HOURS.message(
                        User.get(AuthContext.user_id),
                        task.call_tracker_client_hours?.stripTrailingZeros()?.toString()
                            ?: task.marked_client_hour?.stripTrailingZeros()?.toString()
                            ?: task.client_hours?.stripTrailingZeros().toString(),
                        patch.entity.call_tracker_client_hours.toString()
                    )
                    this.type = CallTrackNote.Type.SYSTEM
                }.save()
            }

            if (task.lead_uid != patch.entity.lead_uid &&
                patch.fields.contains(Task::lead_uid.name)
            ) {
                val new_lead = patch.entity.lead ?: patch.entity.lead_uid?.let { User.get(it) }
                val old_lead = task.lead ?: task.lead_uid?.let { User.get(it) }
                CallTrackNote().apply {
                    this.task_id = patch.entity.id
                    this.note = CallTrackNote.AutomaticNote.LEAD.message(
                        User.get(AuthContext.user_id),
                        old_lead?.name ?: "null",
                        new_lead?.name ?: "null"
                    )
                    this.type = CallTrackNote.Type.SYSTEM
                }.save()
            }

            if (task.support_uid != patch.entity.support_uid &&
                patch.fields.contains(Task::support_uid.name)
            ) {
                val new_support = patch.entity.support ?: patch.entity.support_uid?.let { User.get(it) }
                val old_support = task.support ?: task.support_uid?.let { User.get(it) }
                CallTrackNote().apply {
                    this.task_id = patch.entity.id
                    this.note = CallTrackNote.AutomaticNote.SUPPORT.message(
                        User.get(AuthContext.user_id),
                        old_support?.name ?: "null",
                        new_support?.name ?: "null"
                    )
                    this.type = CallTrackNote.Type.SYSTEM
                }.save()
            }
        }
        return Task.patchThenGet(
            patch.explicitFields(fields),
            cascades = cascades,
            include = include,
        )
    }

    /**
     * 基于专家当前tc + 客户当前有效合同
     * 限同一client的tasks
     */
    @Transactional
    fun fill_client_rate_usd(task_ids: List<Int>): List<Task> {
        val map = Task.findAll(
            Task.Query(ids = task_ids.toSet()), Includes.setOf(
                Task::advisor,
                Task::default_client_rate_usd
            )
        ).associateBy { it.id }
        val tasks = task_ids.map {
            val task = map[it]!!
            if (task.client_rate_usd == null && task.default_client_rate_usd != null) {
                task.client_rate_usd = task.default_client_rate_usd
                Task.update(task)
            }
            task
        }
        return tasks
    }

    @Deprecated(message = "use advisor recommendation instead")
    @Transactional
    fun email_to_contact(
        request: PortalContactRequest
    ): List<Task> {
        val portal = portalClientContactService.create(request.portal, request.email)
        return Task.findAll(Task.Query(ids = portal.task_id_set))
    }

    //region advisor operations
    /**
     * confirm payment
     * in fact, as we always use portal to confirm payment
     * this method it not used anymore
     */
    @Transactional
    fun confirm_payment_legacy(
        id: Int,
        payment_id: Int,
        bank_account: AdvisorBankAccount,
    ): Task {
        val task = Task.get(id, Includes.setOf(Task::advisor_payments))
        localAdvisorPaymentConfirmService.advisor_confirm_legacy(payment_id, bank_account)
        // trigger event
        taskEventService.trigger_event(TaskEvent().apply {
            task_id = id
            type = TaskEventType.ADVISOR_PAYMENT_CONFIRM_RESPOND
        })
        return task
    }
    //endregion


    @Transactional
    fun schedule_for_advisor(
        task_id: Int,
        request: ScheduleRequest,
    ): Task {
        val task = Task.get(task_id)
        request.ranges.forEach {
            it.project_id = task.project_id
            it.task_id = task.id
            it.advisor_id = task.advisor_id
        }
        scheduleService.create_advisor_schedule(
            task.advisor_id.assert_valid_id(),
            task_id,
            request.ranges
        )

        return Task.get(task_id, Includes.setOf(Task::advisor_schedules))
    }

    @Transactional
    fun scheduling(
        task_id: Int,
        request: TaskSchedulingRequest,
    ): Task {
        val task = Task.get(task_id)
        Patch.fromMutator(task) {
            scheduling_status = request.scheduling_status
        }.patch()
        if (request.scheduling_status == TaskStatus.Scheduling.IN_PROGRESS) {
            taskEventService.trigger_event(TaskEvent().apply {
                this.task_id = task_id
                this.type = TaskEventType.SCHEDULING_PROCESS
            })
        }
        return Task.get(task_id)
    }

    //refer to : TaskClientDeclineService.invalidate_decline
    @Transactional
    fun update_client_portal_status(
        task_id: Int,
        request: UpdateClientPortalStatusRequest,
    ): Task {
        when (request.client_portal_status) {
            TaskStatus.ClientPortal.NOT_INTERESTED -> {
                request.client_decline!!.task_id = task_id
                taskClientDeclineService.decline(
                    request.client_decline,
                    send_notification = false
                )
            }

            else -> {
                val exist_decline =
                    TaskClientDecline.firstOrNull(TaskClientDecline.Query(task_id = task_id))
                exist_decline?.let {
                    taskClientDeclineService.invalidate_decline(it.id)
                }
                Patch.fromMap(
                    task_id, mapOf(
                        Task::client_portal_status to request.client_portal_status
                    )
                ).patch()

                if (request.client_portal_status == TaskStatus.ClientPortal.SELECTED) {
                    Patch.fromMap(
                        task_id, mapOf(
                            Task::general_status to TaskStatus.General.SELECTED
                        )
                    ).patch()
                }
            }
        }
        return Task.get(
            task_id, Includes.setOf(
                Task::client_decline dot TaskClientDecline::create_by
            )
        )
    }

    fun preview_task_complete_revenue(task_id: Int, request: TaskCompleteRequest): Revenue {
        return revenueService.task_revenue_preview(task_id, request)
    }

    @Transactional
    fun complete(
        id: Int,
        request: TaskCompleteRequest
    ): Task {
        var task = Task.get(id, Includes.setOf(Task::has_billed_jit, Task::task_outsource_info))
        val has_billed = task.has_billed_jit!!

        /**
         * The dropdown menu for `task::not_charged_reason` reasons isn't associated with an enum. These are the reasons in the front-end dropdown menu that are associated with a failed call.
         */
        val task_failed_reasons = listOf("Expert was not a good fit", "Technical issues", "Poor quality")

        /**
         * If a call is not billed to the client for reasons other than it being a promotion or special discount, we consider that call "failed". We automatically mark that call "failed" in the call tracker.
         */
        val is_failed =
            (request.task?.entity?.not_charged == true && request.task.entity.not_charged_reason in task_failed_reasons)

        val now = Instant.now()

        @Suppress("UNUSED_VARIABLE")
        val is_re_complete = task.general_status == TaskStatus.General.COMPLETED

        if (task.ndb_id != null && task.prohibit_complete_again)
            biz_error("Completed tasks migrated from CN DB are not allowed to complete again.")

        // patch task
        val raw = Task.get(id)
        val task_patch = request.task
        if (task_patch != null) {
            val entity = task_patch.entity
            biz_check(entity.start_time == null || entity.start_time!! < Instant.now()) {
                "Start time must not later than now."
            }
            biz_check(entity.end_time == null || entity.end_time!! <= Instant.now()) {
                "End time must not later than now."
            }
            entity.id = id
            // if the frontend doesn't specify the complete_time, we set it to now
            var patch = if (Task::complete_time.name !in task_patch.fields) {
                entity.complete_time = now
                // as we want to set complete_time to now
                // we must explicitly add 'complete_time' field to fields list.
                task_patch
                    .explicitFields(task_patch.fields + Includes.setOf(Task::complete_time))
            } else {
                task_patch
            }

            // fill client rate
            if (Task::client_rate_usd.name !in task_patch.fields
                && task.project_sub_type == Project.SubType.Consultation
                && task.client_rate_usd == null
            ) {
                Task.join(
                    task, Includes.setOf(
                        Task::advisor,
                        Task::default_client_rate_usd
                    )
                )
                entity.client_rate_usd = task.default_client_rate_usd
                patch =
                    task_patch.explicitFields(patch.fields + Includes.setOf(Task::client_rate_usd))
            }

            if (has_billed) {
                patch = task_patch.explicitFields(patch.fields + Includes.setOf(Task::lead_uid, Task::support_uid))
            }

            if (is_failed) {
                patch.entity.call_tracker_status = TaskStatus.CallTracker.FAILED
                patch = patch.explicitFields(patch.fields + Includes.setOf(Task::call_tracker_status))
            }

            if (entity.general_status == TaskStatus.General.CANCELLED) {
                patch.entity.call_tracker_status = TaskStatus.CallTracker.CANCELED
                patch = patch.explicitFields(patch.fields + Includes.setOf(Task::call_tracker_status))
            }

            task = Task.patchThenGet(patch, Includes.setOf(Task::task_outsource_info))
        }

        if (task.is_with_advisor) {
            val advisor = Advisor.get(task.advisor_id!!)

            // patch advisor
            if (!is_re_complete) {
                advisor.completed_consultation_count++
                advisor.patch(Includes.setOf(Advisor::completed_consultation_count))
            }

            if (task.task_outsource_status != TaskOutsourceType.IMPORTED) {
                // send w9 form
                if (request.should_send_w9_form) {
                    advisorW9FormService.send_w9_form(task.advisor_id!!)
                }
            }
            // create payment(normal or adjustment)
            request.payment?.let { patch ->
                taskPaymentService.create_or_update_payment(task, patch)
            }
        }

        // survey charge
        request.survey_receivable?.let {
            create_or_update_survey_task_receivable(task.id, it)
        }

        // trigger events
        taskEventService.trigger_event(TaskEvent().apply {
            task_id = task.id
            type =
                if (task.general_status == TaskStatus.General.CANCELLED) TaskEventType.CANCELLED else TaskEventType.COMPLETE
        })

        // calculate revenue
        request.task?.let {
            if (
                task.type in setOf(TaskType.CONSULTATION, TaskType.CLIENT_TASK)
                && task.project_sub_type !in setOf(Project.SubType.Survey, Project.SubType.Patients)
            ) {
                // 客户收费信息是否发生改变
                val is_client_charge_changed = !is_client_charge_unchanged(task, raw)
                val revenues = Revenue.findAll(Revenue.Query(task_id = task.id))
                val is_operator_allowed_edit_cutoff =
                    userService.is_user_allowed_to_edit_cutoff_revenue(AuthContext.user_id)
                if (is_client_charge_changed || revenues.isEmpty()) {
                    if (revenues.any { it.is_locked } && !is_operator_allowed_edit_cutoff) {
                        biz_error("task revenue has been cutoff, not allowed to modify")
                    } else {
                        InvokeUtil.trigger(Event.TASK_REVENUE_CHANGE(task_id = id))
                    }
                }
            }
        }

        // send twilio conference asset email
        with(request.twilio_asset_email) {
            if (this != null) {
                val ticket = taskTwilioConferenceService.create_portal_ticket_for_twilio_asset_acquire(task.id)
                val email = this.copy(
                    content = ticket.resolve_token(this.content)
                )
                emailService.send_and_persist(email)
            }
        }

        // send post call portal
        if (task.task_outsource_status != TaskOutsourceType.IMPORTED) {
            listOfNotNull(
                // normally this represents a payment confirm email
                request.advisor_portal1,
                // normally this represents a post call ca email
                request.advisor_portal2,
            ).forEach {
                if (it.portal.contains_payment_confirm) {
                    if (task.has_payment_topic) {
                        // use new payment api
                        val form = paymentFormService
                            .create(
                                payable = task,
                                exclusive = true,
                                disabled_pay_methods = request.payment?.disabled_pay_methods ?: emptyList(),
                            )
                        it.portal.payment_form_id = form.id
                    } else {
                        val payment = localAdvisorPaymentConfirmService.advisor_confirming(
                            AdvisorPaymentContext.get_context_id(rm_task_id = id)
                        )
                        it.portal.payment_id = payment.id
                    }
                }
                it.portal.type = PortalType.ADVISOR_POST_CALL
                it.portal.task_id = task.id
                it.portal.advisor_id = task.advisor_id.assert_valid_id()
                taskToAdvisorService.to_advisor(it.portal, it.email)
            }
        }
        request.contact_portal?.let {
            it.portal.type = PortalType.CONTACT_POST_CALL
            it.portal.task_id_set = setOf(task.id)
            it.portal.project_id = task.project_id
            portalClientContactService.create(it.portal, it.email)
        }
        loopUpService.release_room(LoopupRoom.Query(task_id = task.id))
        if (task.is_with_advisor) {
            request.payment?.let { patch ->
                if (task.task_outsource_status == TaskOutsourceType.IMPORTED) {
                    routerApiClient.sharedActions.complete_task(
                        CompleteTask.Request(
                            id = task.tsid ?: biz_error("invalid task tsid"),
                            from_region = Region.current.to_outsource_region(),
                            amount = patch.amount.toString(),
                            currency = patch.currency.toString(),
                            minutes = patch.minutes.toString(),
                            target_region = com.cpvsn.bridge.sdk.model.Region.DEFAULT
                        )
                    )
                } else if (task.task_outsource_status == TaskOutsourceType.EXPORTED) {
                    // pass outsource charge total info when completed
                    val task_id = id
                    val outsource_task = Task.get(
                        task_id,
                        Includes.setOf(
                            Task::has_billed_jit,
                            Task::task_outsource_info,
                            Task::project,
                            Task::specified_client_rate_jit
                        )
                    )
                    val client_id = outsource_task.project?.client_id ?: biz_error("client id not found")
                    val revenues = Revenue.findAll(Revenue.Query(task_id = task_id))
                    val min_impact_time =
                        (listOf(outsource_task.start_time!!) + revenues.map { it.revenue_time }).min()
                    val affected_contracts = Contract.findAll(
                        Contract.Query(
                            client_id = client_id,
                            approval_status = Contract.ApprovalStatus.APPROVED,
                            payway_ne = PayWay.PROJECT_BASE,
                            effective_status = Contract.EffectiveStatus.EFFECTIVE
                        )
                    ).filter { it.end_time >= min_impact_time }.sortedBy { it.start_time }


                    biz_require(affected_contracts.size <= 2) { "Too complicated to handle more than two contracts." }

                    // 1. generate new revenues and update
                    val charge_cash: BigDecimal
                    val currency: BuiltInCurrency
                    if (outsource_task.project?.sub_type == Project.SubType.Survey) {
                        charge_cash = request.survey_receivable?.amount ?: 0.BD
                        currency = request.survey_receivable?.currency ?: biz_error("survey currency not specified")
                    } else {
                        val matched_contract = contractService.resolve_task_binding_by_time_match(task_id)
                            ?: biz_error("matched contracts not found")
                        val billing_hours =
                            contractCalcService.evaluate_billing_hours_using_contract(
                                task_id,
                                matched_contract
                            )
                        charge_cash = billing_hours * (outsource_task.specified_client_rate_jit
                            ?: matched_contract.unit_price_china
                            ?: 0.BD)
                        currency = outsource_task.specified_client_rate_currency_jit ?: matched_contract.currency_enum

                    }
                    routerApiClient.sharedActions.complete_task(
                        CompleteTask.Request(
                            id = outsource_task.tsid ?: biz_error("invalid task tsid"),
                            from_region = Region.current.to_outsource_region(),
                            amount = (outsource_task.specified_charge_cash ?: charge_cash).toString(),
                            currency = (outsource_task.specified_charge_cash_currency ?: currency).toString(),
                            minutes = (outsource_task.client_hours?.multiply(60.BD)?.setScale(2, RoundingMode.HALF_UP)
                                ?: 0.BD).toString(),
                            target_region = com.cpvsn.bridge.sdk.model.Region.DEFAULT
                        )
                    )
                }
            }
        }

        if (bcgHubHelper.is_bcg_hub_task(id)) {
            InvokeUtil.trigger(
                BcgExpertHubEvent.TaskCompleted(
                    project_id = raw.project_id,
                    task_id = id,
                    payload = ShareTasksStatusRequest.CompletionInfo(
                        start_time = task_patch!!.entity.start_time!!,
                        end_time = task_patch.entity.end_time!!
                    )
                )
            )
        }

        // retrieve task to refresh staled status properties
        return Task.get(id).assert_exist(id)
    }

    fun update_display_id_locked(
        task_id: Int,
        display_id_locked: Boolean,
    ): Task {
        return transactionTemplate.extExecute {
            val task = Task.get(task_id)
            Patch.fromMutator(task) {
                this.display_id_locked = display_id_locked
            }.enableOptimisticLock().patch()
            task
        }
    }

    private fun is_client_charge_unchanged(
        task: Task,
        another: Task,
    ): Boolean {
        val res = task.client_hours == another.client_hours
                && task.discount_hours == another.discount_hours
                && task.senior_rate == another.senior_rate
                && task.has_overseas == another.has_overseas
                && task.has_transcription == another.has_transcription
                && task.has_translation == another.has_translation
                && task.has_in_person == another.has_in_person
                && task.specified_charge_cash == another.specified_charge_cash
                && task.specified_charge_cash_currency == another.specified_charge_cash_currency
                && task.billing_remarks == another.billing_remarks
                && task.billing_notes == another.billing_notes

                && task.start_time == another.start_time
                && task.end_time == another.end_time
        return res
    }

    /**
     * 填充历史的completed tasks的client_rate_usd列
     * 参考Task.default_client_rate_usd的计算逻辑
     * 但要使用revenue对应的contract
     */
    fun fill_client_rate_for_legacy_completed_tasks() {
        val tasks = Task.findAll(
            query = Task.Query(
                general_status = TaskStatus.General.COMPLETED,
                client_rate_usd_is_null = true,
                project = Project.Query(
                    sub_type = Project.SubType.Consultation
                )
            ),
            include = Includes.setOf(
                Task::advisor dot Advisor::location_id_path,
                Task::revenues dot Revenue::contract
            )
        )

        val count_no_contract = tasks.count {
            val contract = it.revenues.orEmpty().firstOrNull()?.contract
            contract == null
        }
        val count_advisor_no_rate = tasks.count {
            val advisor = it.advisor
            advisor?.rate == null || advisor.rate_currency == null
        }
        val count_invalid_contract = tasks.count {
            val contract = it.revenues.orEmpty().firstOrNull()?.contract
            contract != null
                    && (
                    contract.unit_price_us == null
                            || contract.unit_price_china == null
                            || contract.china_senior_term == null
                            || contract.non_china_senior_term == null
                    )

        }

        val updates = tasks.mapNotNull {
            val contract = it.revenues.orEmpty().firstOrNull()?.contract ?: return@mapNotNull null
            val advisor = it.advisor ?: return@mapNotNull null
            if (advisor.rate != null
                && advisor.rate_currency != null
                && contract.unit_price_china != null
                && contract.unit_price_us != null
                && contract.china_senior_term != null
                && contract.non_china_senior_term != null
            ) {
                val client_rate = contractCalcService.evaluate_client_rate_usd(
                    advisor.rate!!.toBigDecimal(),
                    advisor.rate_currency!!,
                    advisorService.is_cn_by_location(advisor.location_id_path.orEmpty()),
                    contract
                )
                it.client_rate_usd = client_rate
                it
            } else {
                null
            }
        }
        taskRepository.batchPatch(updates, Includes.setOf(Task::client_rate_usd))
    }

    fun create_or_update_survey_task_receivable(
        task_id: Int,
        survey_receivable: TaskCompleteRequest.SurveyReceivable,
    ): ReceivableTransaction {
        val task = Task.get(
            task_id, Includes.setOf(
                Task::survey_receivable,
                Task::advisor
            )
        )
        val exist = task.survey_receivable
        if (exist != null) {
            return Patch.fromMutator(exist) {
                this.amount = survey_receivable.amount
                this.currency = survey_receivable.currency
            }.patchThenGet()
        } else {
            val receivable = ReceivableTransaction {
                this.project_id = task.project_id
                this.task_id = task.id
                this.type = ReceivableTransaction.ReceivableType.SURVEY
                this.advisor_name = task.advisor?.full_name.orEmpty()
                this.advisor_id = task.advisor_id
                this.transaction_date = Formatters.DATETIME.`yyyy-MM-dd`.format(task.start_time ?: Instant.now())
                this.amount = survey_receivable.amount
                this.currency = survey_receivable.currency
            }
            return ReceivableTransaction.save(receivable)
        }
    }

    fun retreive_expert_usage(
        advisor_ids: List<Int>,
        client_id: Int
    ): Map<Int, TaskExpertUsage> {
        val client_preference = ClientPreference.findAll(
            ClientPreference.Query(
                client_id = client_id
            )
        ).first()

        if (!client_preference.display_usage_in_to_client) return emptyMap()

        val allowed_sub_types = setOf(Project.SubType.Consultation, Project.SubType.`Physician Consultation`)

        val advisor_tasks_map = Task.findAll(
            query = Task.Query(
                advisor_ids = advisor_ids.toSet(),
                start_time_gte = Instant.now().minus(365, ChronoUnit.DAYS),
                general_status = TaskStatus.General.COMPLETED,
                project_sub_type_in = allowed_sub_types
            )
        ).groupBy {
            it.advisor_id ?: 0
        }

        return advisor_ids.toSet().associateWith {
            val tasks = advisor_tasks_map[it] ?: emptyList()
            TaskExpertUsage(
                calls_last_three_months = filter_tasks_by_time_range(tasks, 90).size,
                calls_last_six_months = filter_tasks_by_time_range(tasks, 180).size,
                calls_last_twelve_months = filter_tasks_by_time_range(tasks, 365).size
            )
        }


    }

    fun filter_tasks_by_time_range(
        tasks: List<Task>,
        days: Int
    ): List<Task> {
        val start_time_threshold = Instant.now().minus(days.toLong(), ChronoUnit.DAYS)
        val done = tasks.filter { task ->
            task.start_time?.isAfter(start_time_threshold) ?: false
        }
        return done
    }

    class TaskExpertUsage(
        val calls_last_three_months: Int,
        val calls_last_six_months: Int,
        val calls_last_twelve_months: Int
    )
}
