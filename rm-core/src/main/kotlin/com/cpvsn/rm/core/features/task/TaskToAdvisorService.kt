package com.cpvsn.rm.core.features.task

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.biz_require
import com.cpvsn.crud.model.Includes
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecordService
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisor
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisorCreationService
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisorRequest
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisorResponse
import com.cpvsn.rm.core.features.task.advisor_consent.TaskAdvisorRecordConsentService
import com.cpvsn.rm.core.features.task.decline.TaskAdvisorDeclineService
import com.cpvsn.rm.core.util.biz_error
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate

@Service
class TaskToAdvisorService {

    @Autowired
    private lateinit var portalAdvisorCreationService: PortalAdvisorCreationService

    @Autowired
    private lateinit var communicationRecordService: CommunicationRecordService

    @Autowired
    private lateinit var taskAdvisorDeclineService: TaskAdvisorDeclineService

    @Autowired
    private lateinit var taskAdvisorRecordConsentService: TaskAdvisorRecordConsentService

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    fun to_advisor(
        portal: PortalAdvisor,
        email: EmailRequestPojo?,
    ): PortalAdvisor {
        return transactionTemplate.extExecute {
            if (email == null) {
                to_advisor_via_portal(portal)
            } else {
                to_advisor_via_portal_email(portal, email)
            }
        }
    }

    fun to_advisor_batch(
        requests: List<PortalAdvisorRequest>
    ): List<PortalAdvisorResponse> {
        val res = requests
            .map { request ->
                val task_id = request.portal.task_id.assert_valid_id()
                var portal: PortalAdvisor? = null
                var error_hint: String? = null
                var success: Boolean = false
                try {
                    portal = to_advisor(request.portal, request.email)
                    success = true
                } catch (e: Exception) {
                    if (e is BusinessException) {
                        error_hint = e.message
                    } else {
                        // something went wrong
                        e.printStackTrace()
                    }
                }
                PortalAdvisorResponse(
                    task_id = task_id,
                    success = success,
                    portal = portal,
                    error_hint = error_hint,
                )
            }

        // historically, we return a task for the email to advisor api.
        // hence, we "join" tasks here.
        val map = Task.findAll(
            Task.Query(
                ids = res.map { it.task_id }.toSet()
            )
        ).associateBy { it.id }
        res.forEach {
            it.task = map[it.task_id]
        }

        return res
    }

    private fun to_advisor_via_portal(
        portal: PortalAdvisor,
    ): PortalAdvisor {
//        biz_require(portal.is_public_portal) {
//            "Currently, only support 'public' portal."
//        }
        return portalAdvisorCreationService.create(portal)
    }

    private fun to_advisor_via_portal_email(
        portal: PortalAdvisor,
        email: EmailRequestPojo,
    ): PortalAdvisor {
        var mail = email

        // create advisor portal and resolve portal token if it is needed
        val tmp1 = portalAdvisorCreationService
            .process_email_request(mail, portal)
        mail = tmp1.first
        val p = tmp1.second

        validate_email_request(p, mail)

        // create advisor decline portal and resolve portal token if it is needed
        val tmp2 = taskAdvisorDeclineService.process_email_request(
            email = mail,
            project_id = p.task?.project_id,
            task_id = p.task?.id,
            advisor_id = p.task?.advisor_id,
        )
        mail = tmp2.first

        // create advisor consent portal and resolve portal token if it it needed
        val tmp3 = taskAdvisorRecordConsentService.process_email_request(
            email = mail,
            task_id = p.task?.id
        )
        mail = tmp3.first

        communicationRecordService
            .send_advisor_portal_email(mail, p, p.task)

        return p
    }

    /**
     * we ensure the email.to_list contains recipient's email address
     * otherwise the user may accidentally send a portal link to incorrect person
     */
    private fun validate_email_request(
        portal: PortalAdvisor,
        email: EmailRequestPojo,
    ) {
        if (portal.advisor == null) {
            PortalAdvisor.join(portal, Includes.setOf(PortalAdvisor::advisor))
        }
        val advisor_email = portal.advisor?.email.orEmpty()
        biz_require(advisor_email.isNotBlank()) {
            "Advisor doesn't have valid email address"
        }
        if (email.to_list.isEmpty())
            biz_error("'to_list' should not be empty")
        biz_require(email.to_list.contains(advisor_email)) {
            "Advisor's email must be contained in 'to_list'"
        }
    }
}
