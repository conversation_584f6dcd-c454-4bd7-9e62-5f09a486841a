package com.cpvsn.rm.core.features.task.angle

import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.model.CrudOptions
import com.cpvsn.crud.util.*
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.user.UserResourceOrder
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Repository

@Repository
class TaskAngleRepo : RmBaseRepository<TaskAngle>(), JdbcEntityBatchRepo<Int, TaskAngle> {
    @Autowired
    override lateinit var dao: TaskAngle.MybatisMapper

    override val batchDao: JdbcEntityBatchDao<Int, TaskAngle> by lazy {
        JdbcEntityBatchDao(TaskAngle::class, dataSource)
    }

    override fun save(
        entity: TaskAngle,
        option: CrudOptions.SaveOption<TaskAngle>
    ): TaskAngle {
        // set default value
        if (entity.next_task_rank == 0) {
            entity.next_task_rank = 1
        }
        if (entity.rank == 0) {
            entity.rank = dao.autoinc_rank(entity.project_id)
        }
        return super.save(entity, option)
    }

    override fun handleJoin(
        list: List<TaskAngle>,
        include: Set<String>
    ): List<TaskAngle> {
        val res = super.handleJoin(list, include)

        if (TaskAngle::customize_order.name in include) {
            val user_id = userIdService?.get_user_id()
            if (user_id != null) {
                val items = UserResourceOrder.findAll(
                    UserResourceOrder.Query(
                        resource_type = UserResourceOrder.Type.TASK_ANGLE,
                        user_id = user_id,
                        resource_ids = res.ids()
                    )
                )
                res join items on TaskAngle::id eq UserResourceOrder::resource_id into TaskAngle::customize_order
            }
        }

        return res
    }
}
