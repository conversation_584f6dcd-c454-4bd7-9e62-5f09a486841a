package com.cpvsn.rm.core.features.task.arrange

import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.arrange.enums.ArrangementType
import com.cpvsn.rm.core.features.twiliosdk.entity.TwilioVoiceConference
import com.cpvsn.rm.core.features.twiliosdk.scheduler.MeetingMqMsg
import org.slf4j.LoggerFactory
import org.springframework.amqp.core.Message
import org.springframework.amqp.core.MessageProperties
import org.springframework.amqp.rabbit.core.RabbitTemplate
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.nio.charset.Charset
import java.time.Instant
import java.time.temporal.ChronoUnit
import java.util.*

@Service
class MeetingMqService {
    //region @
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var rabbitTemplate: RabbitTemplate
    //endregion

    companion object {
        const val exchange = "twilio.delay"
        const val routing_key = "twilio-conference-start"
    }

    private fun to_delayed_mq_message(
        msg: MeetingMqMsg,
    ): Message {
        val messageProperties = MessageProperties().apply {
            val delay_seconds = Instant.now().until(Instant.ofEpochSecond(msg.consume_time_ts), ChronoUnit.SECONDS)
            this.delay = (delay_seconds * 1000).toInt()
            this.contentType = MessageProperties.CONTENT_TYPE_TEXT_PLAIN
            this.contentEncoding = Charsets.UTF_8.name()
            this.messageId = UUID.randomUUID().toString()
        }
        val message = Message(
            CoreJsonUtil.stringify(msg).toByteArray(Charset.defaultCharset()),
            messageProperties
        )
        return message
    }

    /**
     * 使用mq的delayed message功能来触发到点回调提醒
     */
    fun send_delayed_mq(
        msg: MeetingMqMsg,
    ) {
        val message = to_delayed_mq_message(msg)
        logger.info("[SendMQ]${CoreJsonUtil.stringify(message)}")
        rabbitTemplate.convertAndSend(exchange, routing_key, message)
    }


    fun send_twilio_conference_dealyed_mq(
        conference: TwilioVoiceConference
    ) {
        val start_time = conference.expected_start_time
        send_delayed_mq(
            MeetingMqMsg().apply {
                twilio_account_sid = conference.account_sid
                twilio_conference_id = conference.id
                type = MeetingMqMsg.Type.CONFERENCE_START
                consume_time_ts = start_time.epochSecond
                set_time_ts = start_time.epochSecond
            }
        )

        send_delayed_mq(
            MeetingMqMsg().apply {
                twilio_account_sid = conference.account_sid
                twilio_conference_id = conference.id
                type = MeetingMqMsg.Type.CONFERENCE_15_MIN_FROM_START
                consume_time_ts = start_time.minus(15L, ChronoUnit.MINUTES).epochSecond
                set_time_ts = start_time.epochSecond
            }
        )
        send_delayed_mq(
            MeetingMqMsg().apply {
                twilio_account_sid = conference.account_sid
                twilio_conference_id = conference.id
                type = MeetingMqMsg.Type.CONFERENCE_STARTED_FOR_5_MIN
                consume_time_ts = start_time.plus(5L, ChronoUnit.MINUTES).epochSecond
                set_time_ts = start_time.epochSecond
            }
        )
    }

    /**
     * vetting call的endtime时自动更新会议状态
     */
    fun send_vetting_call_mq(
        arrangement: TaskArrangement
    ) {
        require(arrangement.type == ArrangementType.VETTING_CALL)
        if (arrangement.end_time == null) return
        send_delayed_mq(
            MeetingMqMsg().apply {
                task_id = arrangement.task_id
                task_arrangement_id = arrangement.id
                type = MeetingMqMsg.Type.VETTING_CALL_END
                consume_time_ts = arrangement.end_time!!.epochSecond
                set_time_ts = arrangement.end_time!!.epochSecond
            }
        )
    }


    fun handle_vetting_call_end(
        msg: MeetingMqMsg
    ) {
        val arrangement = TaskArrangement.find(msg.task_arrangement_id)
            ?: return

        if (arrangement.end_time?.epochSecond != msg.set_time_ts) {
            return
        }

        val task = Task.firstOrNull(
            query = Task.Query(
                id = msg.task_id
            ), include = Includes.Companion.setOf(
                Task::investor_call
            )
        )

        val vetting_call = task?.investor_call ?: return

        Patch.fromMutator(vetting_call) {
            this.completed = true
        }.patch()
    }
}