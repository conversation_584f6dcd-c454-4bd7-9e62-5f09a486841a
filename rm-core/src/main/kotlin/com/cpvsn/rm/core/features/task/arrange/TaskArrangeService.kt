package com.cpvsn.rm.core.features.task.arrange

import com.cpvsn.core.util.extension.assert_not_null
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.biz_require
import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.extensions.user_id
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.email.EmailService
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.EmailTemplateEngine
import com.cpvsn.rm.core.features.email_template.EmailTemplateResult
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.constant.EmailTemplatePlaceholder
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModelIds
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecordService
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.features.misc.entity_update_log.EntityUpdateLog
import com.cpvsn.rm.core.features.misc.loopup.LoopUpService
import com.cpvsn.rm.core.features.misc.loopup.LoopupRoom
import com.cpvsn.rm.core.features.misc.schedule.Calendar
import com.cpvsn.rm.core.features.misc.schedule.CalendarService
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.misc.schedule.ScheduleService
import com.cpvsn.rm.core.features.outsource.OutsourceService
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisor
import com.cpvsn.rm.core.features.portal.clientcontact.PortalClientContactService
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.portal.common.PortalService
import com.cpvsn.rm.core.features.portal.common.payload.TaskConferenceDialPayload
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskInvestorCall
import com.cpvsn.rm.core.features.task.angle.TaskAngle
import com.cpvsn.rm.core.features.task.arrange.enums.ArrangementType
import com.cpvsn.rm.core.features.task.arrange.enums.ConferenceParticipantRole
import com.cpvsn.rm.core.features.task.constant.BridgeType
import com.cpvsn.rm.core.features.task.constant.TaskOutsourceType
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.task.constant.TaskType
import com.cpvsn.rm.core.features.task.event.TaskEvent
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.pojo.TabEmail
import com.cpvsn.rm.core.features.task.pojo.TaskArrangeContext
import com.cpvsn.rm.core.features.task.pojo.TaskArrangeRequest
import com.cpvsn.rm.core.features.task.pojo.TaskArrangeVettingCallRequest
import com.cpvsn.rm.core.features.task.pojo.email.EmailInfo
import com.cpvsn.rm.core.features.thirdparty.bcg.BcgHubHelper
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.BcgExpertHubEvent
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.interop.ShareTasksStatusRequest
import com.cpvsn.rm.core.features.twiliosdk.config.TwilioProperties
import com.cpvsn.rm.core.features.twiliosdk.entity.TwilioVoiceConference
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomMeeting
import com.cpvsn.rm.core.util.Event
import com.cpvsn.rm.core.util.InvokeUtil
import com.cpvsn.rm.core.util.biz_check
import com.cpvsn.rm.core.util.global_executor
import com.cpvsn.web.auth.AuthContext
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class TaskArrangeService {
    //region @
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired
    private lateinit var scheduleService: ScheduleService

    @Autowired
    private lateinit var twilioProperties: TwilioProperties

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var calendarService: CalendarService

    @Autowired
    private lateinit var loopUpService: LoopUpService

    @Autowired
    private lateinit var taskEventService: TaskEventService

    @Autowired
    private lateinit var taskEmailRecordService: CommunicationRecordService

    @Autowired
    private lateinit var emailService: EmailService

    @Autowired
    private lateinit var portalClientContactService: PortalClientContactService

    @Autowired
    private lateinit var taskArrangementService: TaskArrangementService

    @Autowired
    private lateinit var portalService: PortalService

    @Autowired
    private lateinit var taskTwilioConferenceService: TaskTwilioConferenceService

    @Autowired
    private lateinit var slackService: SlackService

    @Autowired
    private lateinit var meetingMqService: MeetingMqService

    @Autowired
    private lateinit var taskConferenceService: TaskConferenceService

    @Autowired
    private lateinit var outsourceService: OutsourceService

    @Autowired
    private lateinit var bcgHubHelper: BcgHubHelper
    //endregion

    data class TaskScheduleResponse(
        val task: Task,
        val sent_calendars: List<Calendar>,
    )

    data class TaskArrangeResponse(
        val task: Task,
        val sent_calendars: List<Calendar>,
    )

    /**
     * schedule/pre arrange/pending approve step
     */
    @Transactional
    fun schedule(
        task_id: Int,
        request: TaskArrangeRequest,
    ): TaskScheduleResponse {
        val is_reschedule = TaskEvent.exists(
            query = TaskEvent.Query(
                task_id = task_id,
                type_in = setOf(TaskEventType.SCHEDULE, TaskEventType.ARRANGE)
            )
        )
        val task = internal_schedule(task_id, request, true)
        val send_emails_rsp = send_emails(task_id, task, request, TaskArrangeContext())
        //persist email info
        task.arrangement = (request.arrangement ?: TaskArrangement()).let {
            it.mail_info = EmailInfo.create(request).map
            it.task_id = task_id
            TaskArrangement.firstOrNull(
                query = TaskArrangement.Query(
                    task_id = task_id
                )
            )?.let {
                taskArrangementService.delete_cascade(it.id)
            }
            taskArrangementService.save_cascade(it)
        }

        if (bcgHubHelper.is_bcg_hub_task(task_id)) {
            InvokeUtil.trigger(
                BcgExpertHubEvent.TaskScheduled(
                    project_id = task.project_id,
                    task_id = task.id,
                    payload = ShareTasksStatusRequest.ScheduleInfo(
                        start_time = task.start_time!!,
                        end_time = task.end_time!!
                    ),
                    is_reschedule = is_reschedule
                )
            )
        }
        return TaskScheduleResponse(
            task = task,
            sent_calendars = send_emails_rsp.calendars,
        )
    }

    @Transactional
    fun arrange(
        task_id: Int,
        request: TaskArrangeRequest,
    ): TaskArrangeResponse {
        val is_reschedule = TaskEvent.exists(
            query = TaskEvent.Query(
                task_id = task_id,
                type_in = setOf(TaskEventType.SCHEDULE, TaskEventType.ARRANGE)
            )
        )
        request.task_id = task_id
        val task = internal_schedule(task_id, request, false)

        // trigger event
        taskEventService.trigger_event(TaskEvent().apply {
            this.task_id = task.id
            type = TaskEventType.ARRANGE
        })

        // ---------------------------- bridge arrangement -------------------------------------------------------------
        val arrangeContext = TaskArrangeContext()
        val returnTaskExtras = mutableSetOf<String>()

        when (task.arrange_bridge_type) {
            BridgeType.LOOPUP -> {
                arrangeContext.loop_up_room = allocate_loopup_for_arrange(task)
                returnTaskExtras.add(Task::loopup_room.name)
            }

            BridgeType.TWILIO -> {
                biz_require(twilioProperties.enable) {
                    "Twilio is not supported."
                }
                val arrangement = request.arrangement.assert_not_null().let {
                    it.task_id = task_id
                    it.bridge_type = BridgeType.TWILIO
                    it.start_time = task.start_time
                    it.end_time = task.end_time
                    it.conference_invitees = fill_invitees(task_id, it.conference_invitees.orEmpty())
                    it
                }
                val tickets_for_dial = arrangement.conference_invitees.orEmpty().let {
                    taskConferenceService.create_dial_ticket_for_each_role(
                        task_id = task_id,
                        participant_roles = it.map { invitee -> invitee.role }.toSet(),
                        ticket_type = PortalType.TASK_CONFERENCE_DIAL_CONFIRMATION,
                        arrangement_type = ArrangementType.COMMON
                    )
                }
                arrangeContext.arrangement = arrangement
                arrangeContext.portal_tickets_for_dial = tickets_for_dial

                global_executor.execute {
                    slackService.notify_twilio_arrange(task)
                }
                returnTaskExtras.addAll(
                    Includes.setOf(
                        Task::arrangement dot TaskArrangement::twilio_voice_conference dot TwilioVoiceConference::conference_participants
                    )
                )
            }

            BridgeType.ZOOM -> {
                val arrangement = request.arrangement.assert_not_null().let {
                    it.task_id = task_id
                    it.bridge_type = BridgeType.ZOOM
                    it.start_time = task.start_time
                    it.end_time = task.end_time
                    it.conference_invitees = fill_invitees(task_id, it.conference_invitees.orEmpty())
                    it
                }
                val (alternative_host_invitees, none_host_invitees) = arrangement.conference_invitees.orEmpty()
                    .partition {
                        it.role == ConferenceParticipantRole.ALTERNATIVE_HOST
                    }


                // alternative hosts不用创建portal ticket
                val tickets_for_dial = taskConferenceService.create_dial_ticket_for_each_role(
                    task_id = task_id,
                    participant_roles = none_host_invitees.map { invitee -> invitee.role }.toSet(),
                    ticket_type = PortalType.TASK_CONFERENCE_DIAL_CONFIRMATION,
                    arrangement_type = ArrangementType.COMMON
                )

                arrangement.alternative_host_emails = alternative_host_invitees.map {
                    it.email
                }.sorted().filter { it.isNotBlank() }
                arrangeContext.arrangement = arrangement
                arrangeContext.portal_tickets_for_dial = tickets_for_dial

                returnTaskExtras.addAll(
                    Includes.setOf(
                        Task::arrangement dot TaskArrangement::zoom_meeting dot ZoomMeeting::participants
                    )
                )
            }

            else -> {}
        }

        (arrangeContext.arrangement ?: request.arrangement ?: TaskArrangement()).apply {
            this.task_id = task_id
            this.mail_info = EmailInfo.create(request).map
            task.arrange_bridge_type?.let {
                this.bridge_type = it
            }
        }.let {
            taskArrangementService.make_arrangement(it)
        }

        val res = Task.get(
            task.id,
            include = Includes.setOf(
                Task::schedule,
                Task::task_outsource_info,
                Task::arrangement dot TaskArrangement::conference_invitees dot TaskConferenceInvitee::portal_dial_info,
            ).plus(returnTaskExtras)
        )
        arrangeContext.zoom_meeting = res.arrangement?.zoom_meeting
        arrangeContext.arrangement = res.arrangement

        val send_emails_rsp = send_emails(task_id, res, request, arrangeContext)

        if (res.task_outsource_status == TaskOutsourceType.IMPORTED) {
            outsourceService.arrange_imported_task(res, request)
        }

        // EsSync
        InvokeUtil.trigger(Event.PROJECT_DOC_CHANGED(project_id = res.project_id))

        if (bcgHubHelper.is_bcg_hub_task(task_id)) {
            InvokeUtil.trigger(
                BcgExpertHubEvent.TaskScheduled(
                    project_id = res.project_id,
                    task_id = task_id,
                    payload = ShareTasksStatusRequest.ScheduleInfo(
                        start_time = res.start_time!!,
                        end_time = res.end_time!!
                    ),
                    is_reschedule = is_reschedule,
                )
            )
        }

        return TaskArrangeResponse(
            task = res,
            sent_calendars = send_emails_rsp.calendars
        )
    }

    /**
     * task状态信息进task_investor_call,
     * 会议安排信息进task_arrangement
     */
    @Transactional
    fun arrange_vetting_call(
        task_id: Int,
        request: TaskArrangeVettingCallRequest
    ): Task {
        val extras = Includes.setOf(
            Task::vetting_call_arrangement,
            Task::investor_call
        )
        val task = Task.get(task_id, extras)

        // trigger event
        taskEventService.trigger_event(TaskEvent().apply {
            this.task_id = task.id
            type = TaskEventType.ARRANGE_VETTING_CALL
        })

        // ---------------------------- bridge arrangement -------------------
        val context = TaskArrangeContext()
        val bridgeExtras = mutableSetOf<String>()
        val arrangement = request.arrangement
        arrangement.type = ArrangementType.VETTING_CALL
        arrangement.task_id = task_id

        when (request.arrangement.bridge_type) {
            BridgeType.LOOPUP -> {
//                val loopupRoom = loopUpService.acquire_room(
//                    task_id,
//                    LoopupRoom.OccupyReason.VETTING_CALL,
//                    arrangement.loopup_room_id
//                )
                val loopupRoom = allocate_loopup_for_arrange_vetting_call(task_id, arrangement)
                context.loop_up_room = loopupRoom
                bridgeExtras.add(Task::loopup_room.name)
            }

            BridgeType.TWILIO -> {
                biz_require(twilioProperties.enable) {
                    "Twilio is not supported."
                }
                arrangement.enable_record = false
                arrangement.enable_transcription = false
                arrangement.conference_invitees = fill_invitees(task_id, arrangement.conference_invitees.orEmpty())
                val tickets_for_dial = arrangement.conference_invitees.orEmpty().let {
                    taskConferenceService.create_dial_ticket_for_each_role(
                        task_id = task_id,
                        participant_roles = it.map { invitee -> invitee.role }.toSet(),
                        ticket_type = PortalType.TASK_CONFERENCE_DIAL_CONFIRMATION,
                        arrangement_type = ArrangementType.VETTING_CALL
                    )
                }
                context.arrangement = arrangement
                context.portal_tickets_for_dial = tickets_for_dial
                bridgeExtras.addAll(
                    Includes.setOf(
                        Task::vetting_call_arrangement dot TaskArrangement::twilio_voice_conference dot TwilioVoiceConference::conference_participants
                    )
                )
            }

            BridgeType.ZOOM -> {
                arrangement.bridge_type = BridgeType.ZOOM
                arrangement.conference_invitees = fill_invitees(task_id, arrangement.conference_invitees.orEmpty())

                val (alternative_host_invitees, none_host_invitees) = arrangement.conference_invitees.orEmpty()
                    .partition {
                        it.role == ConferenceParticipantRole.ALTERNATIVE_HOST
                    }

                // alternative hosts不用创建portal ticket
                val tickets_for_dial = taskConferenceService.create_dial_ticket_for_each_role(
                    task_id = task_id,
                    participant_roles = none_host_invitees.map { invitee -> invitee.role }.toSet(),
                    ticket_type = PortalType.TASK_CONFERENCE_DIAL_CONFIRMATION,
                    arrangement_type = ArrangementType.VETTING_CALL
                )

                arrangement.alternative_host_emails = alternative_host_invitees.map {
                    it.email
                }.sorted().filter { it.isNotBlank() }
                context.arrangement = arrangement
                context.portal_tickets_for_dial = tickets_for_dial
            }

            else -> {}
        }

        taskArrangementService.make_arrangement(arrangement)

        // update status
        val vettingCall = task.investor_call
        if (vettingCall != null) {
            Patch.fromMutator(vettingCall) {
                this.scheduled = true
            }.patch()
        } else {
            val call = TaskInvestorCall {
                this.task_id = task_id
                this.client_requested = true
                this.scheduled = true
            }
            TaskInvestorCall.save(call)
        }

        // send emails
        send_vetting_call_emails(task_id, task, request, arrangement, context)

        meetingMqService.send_vetting_call_mq(arrangement)

        return Task.get(task_id, extras.plus(bridgeExtras))
    }

    @Transactional
    fun cancel_vetting_call(
        task_id: Int
    ): List<Calendar> {
        val task = Task.get(
            task_id,
            Includes.setOf(
                Task::vetting_call_arrangement,
                Task::investor_call
            )
        )

        Patch.fromMutator(task.investor_call!!) {
            this.scheduled = false
        }.patch()

        val arrangement = task.vetting_call_arrangement ?: return emptyList()

        val calendar_uid_advisor =
            calendarService.get_task_vetting_call_calendar_uid(task_id, Schedule.Role.ADVISOR)
        val calender_uid_client =
            calendarService.get_task_vetting_call_calendar_uid(task_id, Schedule.Role.CLIENT_CONTACT)

        val calendars = Calendar.findAll(
            Calendar.Query(
                calendar_uids = setOf(calendar_uid_advisor, calender_uid_client),
                is_cancelled = false
            )
        )
        calendars.forEach {
            // vetting call不进schedule表
            // calendar就不应该和task schedule绑定
            it.schedule = Schedule {
                this.task_id = task_id
                this.start_time = arrangement.start_time
                this.end_time = arrangement.end_time
            }
            calendarService.cancel(it, use_save_or_update_calendar_v2 = true)
        }
        return calendars
    }

    data class SendEmailsResponse(
        val calendars: List<Calendar>,
    )

    //region email process
    @Transactional
    fun send_emails(
        task_id: Int,
        task: Task,
        request: TaskArrangeRequest,
        context: TaskArrangeContext,
    ): SendEmailsResponse {
        val processedRequest = re_render_emails(
            task_id = task_id,
            request = request,
            context = context
        )

        var sent_calendars = ArrayList<Calendar>()

        Task.join(
            task,
            Includes.setOf(Task::project)
        )
        val tpa_project = task.project?.tpa_project ?: false

        with(processedRequest.mail_to_advisor) {
            if (this?.email != null && is_send_email) {
                taskEmailRecordService.send_client_scheduled_email_to_advisor(
                    email,
                    task,
                    EmailContentType.TASK_CLIENT_SCHEDULED_TO_ADVISOR,
                )
            }
            if (this?.email != null && is_send_calendar) {
                sent_calendars.addAll(
                    calendarService.send_task_calendar(
                        request.schedule,
                        mail_to_advisor = email,
                        is_tpa_project = tpa_project,
                    )
                )
            }
        }

        with(processedRequest.mail_to_contact) {
            if (this?.email != null && is_send_email) {
                taskEmailRecordService.send_client_scheduled_email_to_contact(
                    this.email,
                    task,
                    EmailContentType.TASK_CLIENT_SCHEDULED_TO_CONTACT,
                )
            }
            if (this?.email != null && (tpa_project || is_send_calendar)) {
                sent_calendars.addAll(
                    calendarService.send_task_calendar(
                        request.schedule,
                        mail_to_contact = this.email,
                        is_tpa_project = tpa_project,
                    )
                )
            }
        }

        val other_tabs_datas = listOf(
            RoleTabData(
                processedRequest.mails_to_others?.mail_to_client_compliance,
                EmailContentType.CALENDAR_TASK_ARRANGE_TO_CLIENT_COMPLIANCE,
                ConferenceParticipantRole.CLIENT_COMPLIANCE
            ),
            RoleTabData(
                processedRequest.mails_to_others?.mail_to_client_sponsor,
                EmailContentType.CALENDAR_TASK_ARRANGE_TO_CLIENT_SPONSOR,
                ConferenceParticipantRole.CLIENT_SPONSOR
            ),
            RoleTabData(
                processedRequest.mails_to_others?.mail_to_moderator,
                EmailContentType.CALENDAR_TASK_ARRANGE_TO_MODERATOR,
                ConferenceParticipantRole.MODERATOR
            ),
            RoleTabData(
                processedRequest.mails_to_others?.mail_to_translator,
                EmailContentType.CALENDAR_TASK_ARRANGE_TO_TRANSLATOR,
                ConferenceParticipantRole.TRANSLATOR
            )
        )

        // contains dial info
        val arrangement = context.arrangement
        val invitees = arrangement?.conference_invitees.orEmpty()

        other_tabs_datas.forEach { data ->
            val tab = data.tabEmail
            if (tab?.email == null) return@forEach

            if (tab.is_send_email) {
                emailService.send_and_persist(tab.email)
            }

            if (tab.is_send_calendar) {
                val schedule = Schedule.join(request.schedule, Includes.setOf(Schedule::task))
                val invitee = invitees.firstOrNull {
                    it.role == data.role
                }
                sent_calendars.add(
                    calendarService.send_task_calendar_to_other_roles(
                        bridge = arrangement?.bridge_type,
                        role = data.role,
                        email = tab.email,
                        schedule = schedule,
                        content_type = data.content_type,
                        task = task,
                        dial_info = invitee?.portal_dial_info
                    )
                )
            }
        }
        return SendEmailsResponse(
            calendars = sent_calendars,
        )

    }

    data class RoleTabData(
        val tabEmail: TabEmail?,
        val content_type: EmailContentType,
        val role: ConferenceParticipantRole
    )

    /*
     Similar to normal task arrange logic, but this is combination of 're_render_emails -> send'
     */
    @Transactional
    fun send_vetting_call_emails(
        task_id: Int,
        task: Task,
        request: TaskArrangeVettingCallRequest,
        arrangement: TaskArrangement,
        context: TaskArrangeContext,
    ) {
        TaskArrangement.join(
            arrangement, Includes.setOf(
                TaskArrangement::loopup_room,
                TaskArrangement::advisor_area_loopup_number,
                TaskArrangement::contact_area_loopup_numbers
            )
        )
        // step 1: re-render emails
        val rendering_context = PlaceholderBasedModelIds(
            task_id = task.id,
            project_id = task.project_id,
            advisor_id = task.advisor_id,
            contact_id = task.client_contact_id,
            user_id = AuthContext.user_id,
            client_id = task.client_id
        ).fetch_data().apply {
            context_params = mapOf(
                // fixme 发给contact的email应该用contact对应的区域的dial-in number吧？
                "loopup_area_id" to request.arrangement.advisor_loopup_area_id,
                EmailTemplatePlaceholder.CONFERENCE_LINE_NUMBER.name to context.loop_up_room?.guest_code,
                EmailTemplatePlaceholder.CONFERENCE_LINE_LINK.name to context.loop_up_room?.url,
            )
            extra_context = PlaceholderBasedModel.ExtraContext(
                task_arrangement = arrangement,
                loopup_room = arrangement.loopup_room,
                advisor_area_loopup_number = arrangement.advisor_area_loopup_number,
                contact_area_loopup_numbers = arrangement.contact_area_loopup_numbers
            )
        }
        val map_ticket = context.portal_tickets_for_dial.orEmpty()
            .associateBy { (it.payload_obj as TaskConferenceDialPayload).role }

        val advisor_email = request.mail_to_advisor?.email?.let { email ->
            re_render_email_content(
                emailRequestPojo = email,
                data = rendering_context.apply {
                    this.template = email.template_id?.let { EmailTemplate.get(it) }
                },
                portal_ticket_id = map_ticket[ConferenceParticipantRole.EXPERT]?.id
            )
        }
        val contact_email = request.mail_to_contact?.email?.let { email ->
            re_render_email_content(
                email,
                rendering_context.apply {
                    this.template = email.template_id?.let { EmailTemplate.get(it) }
                },
                portal_ticket_id = map_ticket[ConferenceParticipantRole.CLIENT]?.id
            )
        }

        // step 2: send emails
        with(request.mail_to_advisor) {
            if (advisor_email != null && this?.is_send_email == true) {
                taskEmailRecordService.send_client_scheduled_email_to_advisor(
                    advisor_email,
                    task,
                    EmailContentType.ARRANGE_VETTING_CALL_TO_ADVISOR,
                )
            }
            if (advisor_email != null && this?.is_send_calendar == true) {
                calendarService.send_vetting_call_calendar(
                    arrangement,
                    advisor_email,
                    Schedule.Role.ADVISOR,
                    EmailContentType.ARRANGE_VETTING_CALL_TO_ADVISOR
                )
            }
        }

        with(request.mail_to_contact) {
            if (contact_email != null && this?.is_send_email == true) {
                taskEmailRecordService.send_client_scheduled_email_to_contact(
                    contact_email,
                    task,
                    EmailContentType.ARRANGE_VETTING_CALL_TO_CONTACT,
                )
            }
            if (contact_email != null && this?.is_send_calendar == true) {
                calendarService.send_vetting_call_calendar(
                    arrangement,
                    contact_email,
                    Schedule.Role.CLIENT_CONTACT,
                    EmailContentType.ARRANGE_VETTING_CALL_TO_CONTACT
                )
            }
        }
    }

    /**
     * 前端展示预览邮件时调了一次process_email_template,
     * 但其实还有一些placeholder并未完成替换（比如loopup尚未真正分配/ Bridge Info需要前端在原页面输入）
     * 所以后端还需要再process一次
     *
     * @return new request instance with email updated. context param maybe modified
     */
    private fun re_render_emails(
        task_id: Int,
        request: TaskArrangeRequest,
        context: TaskArrangeContext,
    ): TaskArrangeRequest {
        val task = Task.get(task_id)

        val rendering_context = PlaceholderBasedModelIds(
            task_id = task.id,
            project_id = task.project_id,
            advisor_id = task.advisor_id,
            contact_id = task.client_contact_id,
            user_id = request.uid,
            client_id = task.client_id
        ).fetch_data().apply {
            context_params = mapOf(
                "loopup_area_id" to request.loopup_area_id,
                EmailTemplatePlaceholder.CONFERENCE_LINE_NUMBER.name to context.loop_up_room?.guest_code,
                EmailTemplatePlaceholder.CONFERENCE_LINE_LINK.name to context.loop_up_room?.url,
            )
            extra_context = PlaceholderBasedModel.ExtraContext(
                zoom_meeting = context.zoom_meeting
            )
        }

        // twilio
        val map_ticket = context.portal_tickets_for_dial.orEmpty()
            .associateBy { (it.payload_obj as TaskConferenceDialPayload).role }

        val mail_to_advisor = request.mail_to_advisor?.email?.let { email ->
            re_render_email_content(
                emailRequestPojo = email,
                data = rendering_context.apply {
                    this.template = email.template_id?.let { EmailTemplate.get(it) }
                },
                advisor_portal_ticket_id = request.portal_advisor_id,
                portal_ticket_id = map_ticket[ConferenceParticipantRole.EXPERT]?.id
            )
        }
        val mail_to_contact = request.mail_to_contact?.email?.let { email ->
            re_render_email_content(
                email,
                rendering_context.apply {
                    this.template = email.template_id?.let { EmailTemplate.get(it) }
                },
                portal_ticket_id = map_ticket[ConferenceParticipantRole.CLIENT]?.id
            )
        }
        val mail_to_client_compliance = request.mails_to_others?.mail_to_client_compliance?.email?.let { email ->
            re_render_email_content(
                email,
                rendering_context.apply {
                    this.template = email.template_id?.let { EmailTemplate.get(it) }
                },
                portal_ticket_id = map_ticket[ConferenceParticipantRole.CLIENT_COMPLIANCE]?.id
            )
        }
        val mail_to_client_sponsor = request.mails_to_others?.mail_to_client_sponsor?.email?.let { email ->
            re_render_email_content(
                email,
                rendering_context.apply {
                    this.template = email.template_id?.let { EmailTemplate.get(it) }
                },
                portal_ticket_id = map_ticket[ConferenceParticipantRole.CLIENT_SPONSOR]?.id
            )
        }
        val mail_to_moderator = request.mails_to_others?.mail_to_moderator?.email?.let { email ->
            re_render_email_content(
                email,
                rendering_context.apply {
                    this.template = email.template_id?.let { EmailTemplate.get(it) }
                },
                portal_ticket_id = map_ticket[ConferenceParticipantRole.MODERATOR]?.id
            )
        }
        val mail_to_translator = request.mails_to_others?.mail_to_translator?.email?.let { email ->
            re_render_email_content(
                email,
                rendering_context.apply {
                    this.template = email.template_id?.let { EmailTemplate.get(it) }
                },
                portal_ticket_id = map_ticket[ConferenceParticipantRole.TRANSLATOR]?.id
            )
        }

        val res = request.copy(
            mail_to_advisor = request.mail_to_advisor?.copy(
                email = mail_to_advisor
            ),
            mail_to_contact = request.mail_to_contact?.copy(
                email = mail_to_contact
            ),
            mails_to_others = TaskArrangeRequest.MailsToOthers(
                mail_to_client_compliance = request.mails_to_others?.mail_to_client_compliance?.copy(
                    email = mail_to_client_compliance
                ),
                mail_to_client_sponsor = request.mails_to_others?.mail_to_client_sponsor?.copy(
                    email = mail_to_client_sponsor
                ),
                mail_to_moderator = request.mails_to_others?.mail_to_moderator?.copy(
                    email = mail_to_moderator
                ),
                mail_to_translator = request.mails_to_others?.mail_to_translator?.copy(
                    email = mail_to_translator
                ),
            )
        )
        return res
    }

    private fun re_render_email_content(
        emailRequestPojo: EmailRequestPojo,
        data: PlaceholderBasedModel,
        advisor_portal_ticket_id: Int? = null,
        portal_ticket_id: Int? = null,
    ): EmailRequestPojo {
        var content = EmailTemplateEngine.TextEngine.process(emailRequestPojo.content, data)
        advisor_portal_ticket_id?.let {
            content = PortalAdvisor.get(it).resolve_token(content)
        }
        portal_ticket_id?.let {
            content = Portal.get(it).resolve_token(content)
        }
        return emailRequestPojo.copy(content = content)
    }

    private fun pre_replace_unique_content_placeholder(
        content_type: EmailContentType,
        content: String,
        data: PlaceholderBasedModel,
    ): String {
        val angle = data.task?.angle_id?.let {
            TaskAngle.find(it)
        }
        var res = content.replace(
            "{{WITH_DISCUSSION_TOPICS}}",
            angle?.discussion_topic?.takeIf { it.isNotBlank() }?.let {
                ", with a focus on $it"
            } ?: ""
        )
        val target_companies = EmailTemplatePlaceholder.PROJECT_INVESTMENT_TARGETS
            .resolve(data)
        res = res.replace(
            "{{WITH_TARGET_COMPANIES}}",
            target_companies.takeIf { it.isNotBlank() }?.let {
                ", with a foucus on $it"
            } ?: ""

        )
        val should_provide_bridge_info = data.context_params["should_provide_bridge_info"] == true
        res = res.replace(
            "{{TWILIO_BRIDGE_FRAGMENT}}",
            if (should_provide_bridge_info) {
                "<a href=\"{{CONFERENCE_DIAL_LINK}}\" target=\"_blank\" rel=\"noopener\">JOIN CONFERENCE NOW</a>"
            } else {
                "Conference bridge information is pending and will be provided later."
            }
        )
        res = res.replace(
            "{{ZOOM_BRIDGE_FRAGMENT}}",
            if (should_provide_bridge_info) {
                // 根据role不同，要决定是client portal还是advisor portal的链接（单页面）
                val should_provide_client_portal = content_type in setOf(
                    EmailContentType.CALENDAR_TASK_ARRANGE_CONTACT,
                    EmailContentType.CALENDAR_TASK_ARRANGE_TO_CLIENT_COMPLIANCE
                )
                val placeholder = if (should_provide_client_portal) {
                    EmailTemplatePlaceholder.CLIENT_PORTAL_CONFERENCE_DIAL_LINK.name
                } else {
                    EmailTemplatePlaceholder.CONFERENCE_DIAL_LINK.name
                }
                "<div><a href=\"{{$placeholder}}\" target=\"_blank\" rel=\"noopener\">CLICK HERE to join the conference room</a></div>"
            } else {
                "Conference bridge information is pending and will be provided later."
            }
        )
        return res
    }

    fun process_email_template_by_data_id(
        email_template_id: Int,
        dataIds: PlaceholderBasedModelIds,
    ): EmailTemplateResult {
        val template = EmailTemplate.get(email_template_id)
        val data = dataIds.fetch_data()
        template.content_template = pre_replace_unique_content_placeholder(
            template.content_type_enum!!,
            template.content_template,
            data
        )
        val res = placeholderBasedEmailTemplateService.process_by_data(template, data)
        return res
    }
    //endregion

    private fun internal_schedule(
        task_id: Int,
        request: TaskArrangeRequest,
        trigger_schedule_event: Boolean = false,
    ): Task {
        val task = Task.get(task_id)
        val schedule = request.schedule

        if (task.type == TaskType.CONSULTATION) {
            val client_contact_id = schedule.client_contact_id.assert_valid_id()
            // update client_contact status to active
            Patch.fromMap(
                client_contact_id, mapOf(
                    ClientContact::status to ClientContact.Status.ACTIVE
                )
            ).patch()
            Patch.fromMutator(task) {
                // set client contact id
                this.client_contact_id = client_contact_id
            }.patch()
        }

        Patch.fromMutator(task) {
            start_time = schedule.start_time
            end_time = schedule.end_time
            request.task?.interview_type?.let {
                interview_type = it
            }
            scheduling_status = TaskStatus.Scheduling.SCHEDULED

            advisor_loopup_area_id = request.task?.advisor_loopup_area_id
            contact_loopup_area_ids = request.task?.contact_loopup_area_ids
            arrange_bridge_type = request.task?.arrange_bridge_type
            arrange_detail_str = request.task?.arrange_detail_str
            arrange_advisor_bridge_info_str =
                request.task?.arrange_advisor_bridge_info_str
            arrange_advisor_calendar_location =
                request.task?.arrange_advisor_calendar_location
            arrange_contact_calendar_location =
                request.task?.arrange_contact_calendar_location
        }.patch()

        // trigger event
        if (trigger_schedule_event) {
            taskEventService.trigger_event(TaskEvent().apply {
                this.task_id = task.id
                type = TaskEventType.SCHEDULE
            })
        }
        scheduleService.create_pm_schedule(request.uid, task.id, schedule)

        return Task.get(task.id, setOf(Task::schedule.name))
    }

    private fun allocate_loopup_for_arrange(
        task: Task,
    ): LoopupRoom? {
        val loopupRoom = loopUpService.acquire_room(
            task_id = task.id,
            occupy_reason = LoopupRoom.OccupyReason.CALL,
            selected_room_id = task.loopup_room?.id
        )
        Task.join(
            task,
            Includes.setOf(
                Task::loopup_dial_in_for_advisor,
                Task::loopup_dial_in_for_contact
            )
        )
        Patch.fromMutator(task) {
            this.arrange_advisor_calendar_location = this.loopup_dial_in_for_advisor
            this.arrange_contact_calendar_location = this.loopup_dial_in_for_contact
        }.patch()
        return loopupRoom
    }

    private fun allocate_loopup_for_arrange_vetting_call(
        task_id: Int,
        arrangement: TaskArrangement
    ): LoopupRoom? {
        val loopupRoom = loopUpService.acquire_room(
            task_id,
            LoopupRoom.OccupyReason.VETTING_CALL,
            selected_room_id = arrangement.loopup_room_id
        )
        // assign two fields
        TaskArrangement.join(
            arrangement, Includes.setOf(
                TaskArrangement::loopup_dial_in_for_advisor,
                TaskArrangement::loopup_dial_in_for_contact
            )
        )
        arrangement.arrange_advisor_calendar_location = arrangement.loopup_dial_in_for_advisor
        arrangement.arrange_contact_calendar_location = arrangement.loopup_dial_in_for_contact
        return loopupRoom
    }

    @Transactional
    fun cancel_scheduled_task(
        task_id: Int,
    ): Task {
        val raw_task_for_log = Task.get(task_id)
        val task = Task.get(
            task_id, Includes.setOf(
                Task::loopup_room,
                Task::events
            )
        )
        biz_check(
            task.general_status in setOf(
                TaskStatus.General.SCHEDULED,
                TaskStatus.General.ARRANGED
            )
        )

        val was_scheduling = task.events.orEmpty().any {
            it.type == TaskEventType.SCHEDULING_PROCESS
        }
        Patch.fromMutator(task) {
            this.start_time = null
            this.end_time = null
            this.interview_type = null

            this.advisor_loopup_area_id = null
            this.contact_loopup_area_ids = null
            this.arrange_bridge_type = null
            this.arrange_detail_str = null
            this.arrange_advisor_bridge_info_str = null
            this.arrange_advisor_calendar_location = null
            this.arrange_contact_calendar_location = null

            this.general_status = TaskStatus.General.SELECTED
            this.scheduling_status = if (was_scheduling) {
                TaskStatus.Scheduling.IN_PROGRESS
            } else {
                TaskStatus.Scheduling.INITIAL
            }
        }.patch()

        task.loopup_room?.let {
            loopUpService.release_room(
                LoopupRoom.Query(
                    task_id = task.id,
                    occupy_reason = LoopupRoom.OccupyReason.CALL
                )
            )
        }

        taskEventService.trigger_event(TaskEvent().apply {
            this.task_id = task_id
            this.type = TaskEventType.SCHEDULE_CANCEL
            this.create_by_id = AuthContext.user_id
        })

        if (bcgHubHelper.is_bcg_hub_task(task_id)) {
            InvokeUtil.trigger(
                BcgExpertHubEvent.TaskCancelled(
                    task_id = task_id,
                    project_id = task.project_id,
                    cancel_reason = "Cancelled"
                )
            )
        }

        EntityUpdateLog.byEntityUpdate(raw_task_for_log, task)?.let {
            it.topic = EntityUpdateLog.Topic.TASK_SCHEDULE_CANCEL.name
            it.create_by_id = AuthContext.user_id
            it.save()
        }

        return task
    }


    /**
     * 为invitee列表赋值advisor_id, client_contact_id, user_id
     * 为advisor invitee添加email
     */
    private fun fill_invitees(
        task_id: Int,
        invitees: List<TaskConferenceInvitee>,
    ): List<TaskConferenceInvitee> {
        val task = Task.get(task_id, Includes.setOf(Task::advisor))

        // advisor
        invitees.firstOrNull {
            it.role == ConferenceParticipantRole.EXPERT
        }?.takeUnless { it.advisor_id.is_valid_id }?.let {
            it.advisor_id = task.advisor_id ?: 0
            it.email = task.advisor?.email ?: ""
        }

        // client contact
        val client_invitees = invitees.filter {
            it.role in setOf(
                ConferenceParticipantRole.CLIENT,
                ConferenceParticipantRole.CLIENT_COMPLIANCE
            )
        }
        val client_emails = client_invitees
            .map { it.email }
            .filter { it.isNotBlank() }
            .toSet()

        val client_contacts = ClientContact.findAll(
            ClientContact.Query(
                client_id = task.client_id,
                contact_infos = ContactInfo.Query(
                    owner_type = ContactInfo.OwnerType.CLIENT_CONTACT,
                    type = ContactInfo.Type.EMAIL,
                    value_in = client_emails
                )
            ), Includes.setOf(ClientContact::contact_infos_without_mosaic)
        )

        client_invitees.filter {
            it.email.isNotBlank() && !it.client_contact_id.is_valid_id
        }.forEach { invitee ->
            val client_contact = client_contacts.firstOrNull { cc ->
                cc.contact_infos_without_mosaic.orEmpty().any {
                    it.value == invitee.email
                }
            }
            client_contact?.let {
                invitee.client_contact_id = it.id
            }
        }

        // user_id
        val user_invitees = invitees.filter {
            it.role in setOf(ConferenceParticipantRole.MODERATOR)
        }
        user_invitees.filter {
            it.email.isNotBlank() && !it.user_id.is_valid_id
        }.forEach { invitee ->
            val user = User.firstOrNull(User.Query(email = invitee.email))
            user?.let {
                invitee.user_id = it.id
            }
        }
        return invitees
    }
}