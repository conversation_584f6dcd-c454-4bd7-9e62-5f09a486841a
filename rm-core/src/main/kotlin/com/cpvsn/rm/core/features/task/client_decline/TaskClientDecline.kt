package com.cpvsn.rm.core.features.task.client_decline

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.PatchFields
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.annotation.TableDefinition
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import com.cpvsn.rm.core.features.user.User
import java.time.Instant

@TableDefinition(
        indices = [TableDefinition.IndexDefinition(
                columns = ["task_id", "delete_at"],
                type = TableDefinition.IndexType.UNIQUE
        )]
)
class TaskClientDecline : RmEntity(), UserAuditing, SoftDeletable {
    companion object : RmCompanion<TaskClientDecline>()

    @Column
    var task_id: Int = 0

    @Column
    var reason: String = ""

    @Column
    var note: String = ""

    @Column
    var task_snapshot_json: String = ""

    @Column
    var client_portal_user_email: String? = null

    @Column
    override var create_by_id: Int = 0

    @Column
    override var update_by_id: Int = 0

    @Column(insertable = false, updatable = false)
    override var delete_at: Instant? = null

    //region +
    @PatchFields(["task_snapshot_json"])
    var task_snapshot: TaskSnapshot? by PropDelegates.json_nullable(this::task_snapshot_json)

    @Relation
    var create_by: User? = null
    //endregion

    data class TaskSnapshot(
            val client_portal_status: TaskStatus.ClientPortal,
    )

    data class Query(
            @Criteria.Eq
            val id: Int? = null,
            @Criteria.IdsIn
            val ids: Set<Int>? = null,
            @Criteria.IsNull
            val id_is_null: Boolean? = null,

            @Criteria.Eq
            val task_id: Int? = null,
            @Criteria.IdsIn
            val task_ids: Set<Int>? = null,
    ) : BaseQuery<TaskClientDecline>()
}