package com.cpvsn.rm.core.features.task.event

import com.cpvsn.core.base.entity.BaseMapper
import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.annotation.TableDefinition
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.agg.AggregationMapper
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.taskscoped.TaskScopedEntity
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.ca.TaskCa
import com.cpvsn.rm.core.features.task.feedback.TaskContactFeedback
import com.cpvsn.rm.core.features.task.review.TaskContactReview
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.Event
import com.fasterxml.jackson.annotation.JsonIgnore
import org.apache.ibatis.annotations.Mapper
import org.apache.ibatis.annotations.Param
import org.apache.ibatis.annotations.Select
import org.springframework.stereotype.Component
import java.time.Instant

@TableDefinition(
    indices = [
        TableDefinition.IndexDefinition(columns = ["task_id"]),
        TableDefinition.IndexDefinition(columns = ["project_id"]),
        // this is desired
        // see com.cpvsn.rm.core.features.advisor.activity.AdvisorActivityMapper.SqlProvider.get_active_advisor_ids
        TableDefinition.IndexDefinition(columns = ["create_at"])
    ]
)
class TaskEvent : RmEntity(), UserAuditing, Event,
    TaskScopedEntity {
    companion object : RmCompanion<TaskEvent>()

    @Column
    var task_id: Int = 0

    override fun set_project_id(project_id: Int) {
        this.project_id = project_id
    }

    @Column
    var project_id: Int = 0

    @Column
    var payload_id: Int? = null

    @Column
    var type: TaskEventType = TaskEventType.OUTREACH

    @Column
    override var create_by_id: Int = 0

    override var update_by_id: Int = 0

    @Column(insertable = true, updatable = false)
    override var create_at: Instant? = null

    @Relation
    var create_by: User? = null

    @Relation
    var task: Task? = null

    /**
     * a hint reveals the meaning of payload_id
     */
    @get:JsonIgnore
    val payload_class: String?
        get() {
            return when (type) {
                TaskEventType.SQ_SEND -> InquiryInstance::class.simpleName
                TaskEventType.SQ_RESPOND -> InquiryInstance::class.simpleName

                TaskEventType.CA_SEND -> TaskCa::class.simpleName
                TaskEventType.CA_RESPOND -> TaskCa::class.simpleName

                TaskEventType.CLIENT_COMPLIANCE_APPROVE -> TaskContactReview::class.simpleName
                TaskEventType.CLIENT_COMPLIANCE_REJECT -> TaskContactReview::class.simpleName
                TaskEventType.CLIENT_COMPLIANCE_HOLD, TaskEventType.CLIENT_COMPIANCE_REQUEST_MORE_INFO -> TaskContactReview::class.simpleName

                TaskEventType.CONTACT_APPROVE -> TaskContactReview::class.simpleName
                TaskEventType.CONTACT_REJECT -> TaskContactReview::class.simpleName
                TaskEventType.CONTACT_HOLD -> TaskContactReview::class.simpleName

                TaskEventType.CONTACT_SCHEDULE_RESPOND -> Schedule::class.simpleName

                TaskEventType.CONTACT_FEEDBACK_RESPOND -> TaskContactFeedback::class.simpleName
                TaskEventType.ADVISOR_3RD_PARTY_SURVEY_PORTAL_ACCESS -> Portal::class.simpleName
                else -> null
            }
        }

    @Mapper
    @Component
    interface MybatisMapper : BaseMapper<TaskEvent>, AggregationMapper {
        @Select(
            """
            <script>
            SELECT te.*
            FROM task_event te
            JOIN (
              SELECT MAX(id) AS max_id
              FROM task_event
              WHERE project_id IN
                <foreach collection='project_ids' item='item' open='(' separator=',' close=')'>
                    #{item}
                </foreach> 
                AND type = 'CONTACT_SEND' 
                GROUP BY project_id
            ) latest ON te.id = latest.max_id;
            </script>
            """
        )
        fun project_latest_send_to_client(
            @Param("project_ids") project_ids: Set<Int>,
        ): List<TaskEvent>

    }

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Eq
        val project_id: Int? = null,
        @Criteria.IdsIn
        val project_ids: Set<Int>? = null,
        @Criteria.Eq
        val task_id: Int? = null,
        @Criteria.IdsIn
        val task_ids: Set<Int>? = null,
        @Criteria.Eq
        val type: TaskEventType? = null,
        @Criteria.In
        val type_in: Set<TaskEventType>? = null,
        @Criteria.Eq
        val create_by_id: Int? = null,
        @Criteria.IdsIn
        val create_by_ids: Set<Int>? = null,
        @Criteria.Gte
        val create_at_gte: Instant? = null,
        @Criteria.Lte
        val create_at_lte: Instant? = null,
        @Criteria.Gt
        val create_at_ge: Instant? = null,
        @Criteria.Lt
        val create_at_le: Instant? = null,

        @Criteria.Join
        val create_by: User.Query? = null,
    ) : BaseQuery<TaskEvent>()
}
