package com.cpvsn.rm.core.features.task.feedback

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.task.Task


class TaskAdvisorFeedback : RmEntity() {
    companion object : RmCompanion<TaskAdvisorFeedback>()

    @Column
    var task_id: Int = 0

    @Column
    var advisor_id: Int = 0

    @Column
    var client_contact_id: Int? = null

    @Column
    var overall_grade: Int = 0

    @Column
    var compensation_rank: Int = 0

    @Column
    var exposure_rank: Int = 0

    @Column
    var knowledge_rank: Int = 0

    @Column
    var with_other_expert_network: Boolean = false

    //a frontend enum field
    @Column
    var proposed_improvement_area: String = ""

    @Column
    var improvement_suggestion: String = ""

    @Column
    var further_recommendation: String = ""

    @Column
    var additional_feedback: String = ""

    //region +

    @Relation
    var task: Task? = null
    //endregion

    data class Query(
            @Criteria.Eq
            val id: Int? = null,
            @Criteria.IdsIn
            val ids: String? = null,
            @Criteria.Eq
            val task_id: Int? = null,
            @Criteria.IdsIn
            val task_ids: Set<Int>? = null,
            @Criteria.Eq
            val advisor_id: Int? = null,
            @Criteria.IdsIn
            val advisor_ids: Set<Int>? = null,
    ) : BaseQuery<TaskAdvisorFeedback>()
}
