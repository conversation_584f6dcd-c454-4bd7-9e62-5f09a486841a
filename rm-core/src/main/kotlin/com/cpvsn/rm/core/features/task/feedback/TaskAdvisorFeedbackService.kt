package com.cpvsn.rm.core.features.task.feedback

import com.cpvsn.core.model.BusinessException
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.task.event.TaskEventService
import com.cpvsn.rm.core.features.task.event.TaskEventType
import com.cpvsn.rm.core.features.task.event.TaskEvent
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional


@Service
class TaskAdvisorFeedbackService : RmBaseRepository<TaskAdvisorFeedback>() {

    @Autowired
    private lateinit var taskEventService: TaskEventService

    companion object {
        const val CONTENT_MAX_LENGTH = 2000
    }

    @Transactional
    fun feedback(model: TaskAdvisorFeedback): TaskAdvisorFeedback {
        if (
            model.improvement_suggestion.length > CONTENT_MAX_LENGTH ||
            model.further_recommendation.length > CONTENT_MAX_LENGTH ||
            model.additional_feedback.length > CONTENT_MAX_LENGTH
        ) {
            throw BusinessException("The content is too long")
        }
        val existed = firstOrNull(TaskAdvisorFeedback.Query(task_id = model.task_id))
        val res = if (existed != null) {
            model.id = existed.id
            update(model)
        } else {
            save(model)
        }

        taskEventService.trigger_event(TaskEvent().apply {
            task_id = model.task_id
            type = TaskEventType.ADVISOR_SURVEY_RESPOND
            payload_id = res.id
        })
        return res
    }
}
