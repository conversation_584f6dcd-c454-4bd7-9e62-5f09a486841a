package com.cpvsn.rm.core.features.task.outreach

import com.cpvsn.core.util.delegates.PropDelegates
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.fasterxml.jackson.annotation.JsonIgnore
import java.time.Instant

class OutreachProcessRecord : RmEntity(), SoftDeletable {

    companion object : RmCompanion<OutreachProcessRecord>()

    data class TaskOutreachResult(
        val task_id: Int,
        val succeed: <PERSON><PERSON><PERSON>,
        val reason: String = "",
    )

    @Column
    var total_count: Int = 0

    @Column
    var succeed_count: Int = 0

    @Column
    var failed_count: Int = 0

    @Column
    var finished: Boolean = false

    var outreach_task_ids: List<Int> by PropDelegates.json(this::outreach_task_ids_json_str)

    @JsonIgnore
    @Column
    var outreach_task_ids_json_str: String? = null

    var failed_tasks: List<TaskOutreachResult> by PropDelegates.json(this::failed_tasks_json_str)

    @JsonIgnore
    @Column
    var failed_tasks_json_str: String? = null

    override var delete_at: Instant? = null

}