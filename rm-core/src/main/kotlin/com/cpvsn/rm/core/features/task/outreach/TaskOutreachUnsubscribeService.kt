package com.cpvsn.rm.core.features.task.outreach

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.Patch
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.rm.core.config.PortalProperties
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.AdvisorStatusChangeLog
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.email_template.PlaceholderBasedEmailTemplateService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.portal.PortalType
import com.cpvsn.rm.core.features.portal.common.Portal
import com.cpvsn.rm.core.features.portal.common.PortalService
import com.cpvsn.rm.core.features.portal.common.payload.AdvisorUnsubscribeOutreachPayload
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.rm.core.util.UrlUtil
import com.cpvsn.rm.core.util.biz_error
import com.cpvsn.rm.core.util.biz_required_not_null
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.Instant

@Service
class TaskOutreachUnsubscribeService {

    @Autowired
    private lateinit var portalService: PortalService

    @Autowired
    private lateinit var placeholderBasedEmailTemplateService: PlaceholderBasedEmailTemplateService

    @Autowired
    private lateinit var portalProperties: PortalProperties

    //    private fun placeholderExists(email: String): Boolean {
//        return urlPlaceHolder?.let {
//            email.contains(it)
//        } ?: false
//    }
//

    fun create_portal(
        task: Task,
        user_id: Int,
    ): Portal {
        return portalService.create(Portal {
            type = PortalType.ADVISOR_UNSUBSCRIBE_OUTREACH
            this.project_id = task.project_id
            this.task_id = task.id
            this.advisor_id = task.advisor_id
            payload_obj = AdvisorUnsubscribeOutreachPayload(
                id = biz_required_not_null(task.advisor_id),
                user_id = user_id,
                message_id = null,
                outreach_subject = null,
            )
        })
    }

    fun get_unsubscribe_link(portal: Portal): String? {
        return Portal.joinOnce(portal, Includes.setOf(Portal::link_jit)).link_jit
    }

    fun get_one_post_unsubscribe_link(portal: Portal): String? {
        return UrlUtil.join(portalProperties.advisorUnsubscribeOutreachClickOnceUrl, portal.token)
    }

    fun process_email_request(
        email: EmailRequestPojo,
        task: Task,
        user_id: Int,
        plain_text: Boolean = false,
        with_compliance_section: Boolean = true,
    ): Pair<EmailRequestPojo, Portal> {
        val portal = create_portal(task, user_id)
        // append unsubscribe fragment to the email content
        val advisor = Advisor.get(task.advisor_id ?: biz_error("task advisor id not found"))

        val unsubscribe_fragment = when (advisor.type) {
            Advisor.Type.LEAD -> {
                if (with_compliance_section) {
                    if (plain_text) {
                        resolve_unsubscribe_fragement_to_plain_text(portal)?.let {
                            "\n${it.content}"
                        }
                    } else {
                        resolve_unsubscribe_fragment(portal)?.let {
                            "<br>${it.content}"
                        }
                    }
                } else {
                    null
                }
            }

            else -> null
        }
        var mail = if (unsubscribe_fragment == null) {
            email
        } else {
            email.copy(
                content = "${email.content}${unsubscribe_fragment}"
            )
        }

        // mq-handler中发送邮件的EmailModel中新增了一个Map<String, String>
        // heads的属性，用于设置发送的邮件的header头部信息，
        // 需要在us DB中使用sendgrid发送的并且有退订功能的邮件中添加一个header信息：
        // List-Unsubscribe: <退订的http链接>
        get_one_post_unsubscribe_link(portal)?.let {
            mail = mail.copy(
                headers = mapOf("List-Unsubscribe" to "<$it>")
            )
        }

        return mail to portal
    }

    fun resolve_unsubscribe_fragment(
        portal: Portal
    ): EmailRequestPojo? {
        return get_unsubscribe_fragment()?.let {
            it.copy(content = portal.resolve_token(it.content))
        }
    }

    // handle it specially as a tmp solution
    fun resolve_unsubscribe_fragement_to_plain_text(
        portal: Portal
    ): EmailRequestPojo? {
        return EmailTemplate().let { template ->
            template.content_template = """Privacy Policy: https://www.capvision.com/#/privacy_policy
If you no longer would like to receive emails from Capvision, please unsubscribe here:{{PORTAL_ADVISOR_UNSUBSCRIBE_OUTREACH_LINK}}"""
            EmailRequestPojo.from_template_result(
                placeholderBasedEmailTemplateService.process_by_data(
                    template,
                    PlaceholderBasedModel(),
                )
            ).let { email ->
                email.copy(content = portal.resolve_token(email.content))
            }
        }
    }

    private fun get_unsubscribe_fragment(): EmailRequestPojo? {
        val fragment_template = EmailTemplate.firstOrNull(
            EmailTemplate.Query(
                is_draft = false,
                content_type = EmailContentType.ADVISOR_UNSUBSCRIBE_OUTREACH_FRAGMENT,
                is_system_template = true
            ),
            Sort.by { desc(EmailTemplate::id.name) }
        )
        return fragment_template?.let {
            EmailRequestPojo.from_template_result(
                placeholderBasedEmailTemplateService.process_by_data(
                    it,
                    PlaceholderBasedModel(),
                )
            )
        }
    }

    fun unsubscribe(portal: Portal, unsubscribe_type: Advisor.UnsubscribeType = Advisor.UnsubscribeType.EMAIL) {
        val payload = portal.get_payload(AdvisorUnsubscribeOutreachPayload::class)
        val advisor = Advisor.find(payload.id) ?: return

        // change advisor status
        Patch.fromMutator(advisor) {
            this.status_enum = Advisor.Status.BLACKLIST
            this.unsubscribe_type = unsubscribe_type
            this.put_blacklist_at = Instant.now()
            this.put_blacklist_by_id = payload.user_id
        }.patch()

        // save advisorStatusChangeLog
        val user = User.find(payload.user_id)
        AdvisorStatusChangeLog().apply {
            this.advisor_id = payload.id
            this.advisor_name = advisor.full_name
            this.create_at = Instant.now()
            this.user_id = payload.user_id
            this.user_name = user?.name
            this.project_id = portal.project_id
            this.status = AdvisorStatusChangeLog.ExpertStatus.BAN
            this.type = AdvisorStatusChangeLog.ChangeType.UNSUBSCRIBE_LINK
        }.save()

        // send notification
        val model = PlaceholderBasedModel(
            user = User.find(payload.user_id),
            advisor = advisor,
        ).apply {
            // I don't want to create a placeholder for this "one time" usage
//            context_params = mapOf("portal_payload" to payload)
        }
        var email = EmailRequestPojo.from_template_result(
            placeholderBasedEmailTemplateService.process_first_by_data(
                EmailTemplate.Query(
                    is_draft = false,
                    content_type = EmailContentType.NOTIFY_KM_ADVISOR_OUTREACH_UNSUBSCRIBED,
                    is_system_template = true
                ),
                model
            )
        )
        payload.message_id?.let { message_id ->
            val subject = payload.outreach_subject
                ?.let {
                    "Re: $it"
                } ?: email.subject
            email = email.copy(
                subject = subject,
                reply_message_id = message_id
            )
        }
        email.send()
    }

}
