package com.cpvsn.rm.core.features.task.outreach.ipwarmup

import com.cpvsn.rm.core.features.core.app_config.AppConfigEntryService
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlin.math.pow

@Service
class OutreachIPWarmUpService {

    companion object {
        internal fun get_day(
            warm_up_start_date: LocalDate,
            now: LocalDate = LocalDate.now()
        ): Int {
            return ChronoUnit.DAYS.between(warm_up_start_date, now).toInt()
        }

        /**
         * "预热计划发送数量调整一下吧，改为预热45天， 第一天200第二天 400 第三天 700 ,  后面每天在前面一天的基础上加700"
         *  -- Ping
         * @see https://twilio-cms-prod.s3.amazonaws.com/documents/Generic_IP_Warmup_Schedule.pdf
         */
        internal fun get_volume(day: Int): Int {
            return when (val d = day.coerceAtMost(45)) {
                1 -> 200
                2 -> 400
                3 -> 700
                else -> 700 + (d - 3) * 700
            }
        }
    }

    @Autowired
    private lateinit var appConfigEntryService: AppConfigEntryService

    @Autowired
    private lateinit var outreachWarmUpSentCounter: OutreachWarmUpSentCounter


    fun process_outreach_email(
        email_request: EmailRequestPojo,
        user_email: String,
        user_outreach_email: String?,
    ): EmailRequestPojo {
        if (user_outreach_email == null)
        // this case, user doesn't have outreach email, no need to process
            return email_request
        if (user_email == user_outreach_email)
        // no need to process
            return email_request

        // for backward compatibility
        // if "OUTREACH_IP_WARM_UP_START_DATE" is not set, we don't use outreach email
        val warm_up_start_date = get_warm_up_start_date()
            ?: return not_use_warm_up_ip(
                email_request,
                user_email,
                user_outreach_email
            )

        // generally, "warm up" process will not lasts over 21 days
        // if it exceed this value, we treat it as warm up process is finished.
        // no need to process the email request
        val day = get_day(warm_up_start_date)
        when {
            day < 0 -> {
                // warm up hasn't started yet
                return not_use_warm_up_ip(
                    email_request,
                    user_email,
                    user_outreach_email
                )
            }
            day > 45 -> {
                // warm up has finished
                return email_request
            }
        }

        // how many emails we should send through outreach email today.
        val volume = get_volume(day)

        val sent_count = outreachWarmUpSentCounter
            .inc_then_get_sent_count()
        return if (sent_count > volume) {
            not_use_warm_up_ip(email_request, user_email, user_outreach_email)
        } else {
            email_request
        }
    }

    internal fun get_warm_up_start_date(): LocalDate? {
        return appConfigEntryService
            .find(AppConfigKey.OUTREACH_IP_WARM_UP_START_DATE)
            ?.get_value(LocalDate::class)
    }

    /**
     * as outreach_email domain bound to "WARMING UP" IP
     * email domain bound to in-use IP.
     * replace outreach_email to email means NOT use warm up IP.
     */
    internal fun not_use_warm_up_ip(
        email_request: EmailRequestPojo,
        user_email: String,
        user_outreach_email: String,
    ): EmailRequestPojo {
        return email_request.copy(
            from = if (email_request.from == user_outreach_email)
                user_email
            else email_request.from,
            // erase reply_to_list, this might be a superfluous action.
            reply_to_list = if (email_request.reply_to_list == listOf(user_email))
                null
            else email_request.reply_to_list,
        )
    }

}
