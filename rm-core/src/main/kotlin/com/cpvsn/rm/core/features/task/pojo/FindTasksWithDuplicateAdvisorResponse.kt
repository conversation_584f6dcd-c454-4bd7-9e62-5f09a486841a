package com.cpvsn.rm.core.features.task.pojo

import com.cpvsn.rm.core.features.task.Task

data class FindTasksWithDuplicateAdvisorResponse(
    val not_follow_up: List<Task>,
    val follow_up: List<Task>,
    val duplicate_in_angle: List<Task>,
    val duplicate_in_project: List<Task>,
    // not exclusive, may contain completed tasks
    val without_advisor: List<Task>,
    // not exclusive, may contain tasks without advisor
    val completed: List<Task>,
    val empty_employment_history_advisor: List<Task>,
    val with_not_current_region_advisor: List<Task>,
    val employees_conflict_investment_target: List<Task>,
    val outsource_limit_advisor: List<Task>,
)
