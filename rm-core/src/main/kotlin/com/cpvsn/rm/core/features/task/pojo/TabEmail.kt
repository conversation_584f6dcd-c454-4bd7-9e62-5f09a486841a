package com.cpvsn.rm.core.features.task.pojo

import com.cpvsn.rm.core.features.email.pojo.EmailRequestPojo

data class TabEmail(
    val email: EmailRequestPojo? = null,
    val is_send_email: <PERSON><PERSON><PERSON> = false,
    val is_send_calendar: <PERSON><PERSON><PERSON> = false,

    // 20230323 经前端确认这个参数在前端就是写死的false
    // @JsonProperty("send_task_client_scheduled_email_to_advisor")
    // val is_send_task_client_scheduled_email_to_advisor: <PERSON><PERSON><PERSON> = false,
)