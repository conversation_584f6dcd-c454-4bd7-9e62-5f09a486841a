package com.cpvsn.rm.core.features.task.pojo

import com.cpvsn.rm.core.features.task.Task
import javax.validation.constraints.NotNull

class TaskCreateRequest(
        @NotNull
        val task: Task? = null,
        /**
         * if task.advisor_profile is not null, we just cascade save it
         * if it is null, then
         * this property determines we use which job id to create advisor profile
         * if this property is also null, we find the current job of the advisor
         */
        val advisor_job_id: Int? = null,
        /**
         * If true, will copy latest_submitted_sq if either [original_task_id] or Task::latest_submitted_sq are not null
         */
        val include_sq: Boolean = false,
        val original_task_id: Int? = null
)
