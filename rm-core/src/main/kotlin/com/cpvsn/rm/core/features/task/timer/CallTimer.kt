package com.cpvsn.rm.core.features.task.timer

import com.cpvsn.rm.core.features.task.arrange.enums.ConferenceParticipantRole
import java.time.Instant

class CallTimer(
    val timer_actions: MutableList<TimerAction>
) {
    private fun add_timer_action(action: TimerAction): List<TimerAction> {
        when (action.role) {
            ConferenceParticipantRole.EXPERT -> {
                val is_timer_running = this.timer_actions.lastOrNull {
                    it.role == ConferenceParticipantRole.EXPERT
                }?.type == TimerAction.Type.BEGIN
                if (
                    (action.type == TimerAction.Type.BEGIN && !is_timer_running)
                    || (action.type == TimerAction.Type.SUSPEND && is_timer_running)
                ) {
                    timer_actions.add(action)
                }
            }

            else -> {
                val is_timer_running = this.timer_actions.lastOrNull {
                    it.role != ConferenceParticipantRole.EXPERT
                }?.type == TimerAction.Type.BEGIN
                if (
                    (action.type == TimerAction.Type.BEGIN && !is_timer_running)
                    || (action.type == TimerAction.Type.SUSPEND && is_timer_running)
                ) {
                    timer_actions.add(action)
                }
            }
        }
        return this.timer_actions
    }

    fun begin_pay_expert(
        timestamp: Instant?,
        description: String,
    ) {
        val action = TimerAction(
            role = ConferenceParticipantRole.EXPERT,
            type = TimerAction.Type.BEGIN,
            timestamp = timestamp,
            description = description
        )
        add_timer_action(action)
    }

    fun suspend_pay_expert(
        timestamp: Instant?,
        description: String,
    ) {
        val action = TimerAction(
            role = ConferenceParticipantRole.EXPERT,
            type = TimerAction.Type.SUSPEND,
            timestamp = timestamp,
            description = description
        )
        add_timer_action(action)
    }

    fun begin_charge_client(
        timestamp: Instant?,
        description: String,
    ) {
        val action = TimerAction(
            role = ConferenceParticipantRole.CLIENT,
            type = TimerAction.Type.BEGIN,
            timestamp = timestamp,
            description = description
        )
        add_timer_action(action)
    }

    fun suspend_charge_client(
        timestamp: Instant?,
        description: String,
    ) {
        val action = TimerAction(
            role = ConferenceParticipantRole.CLIENT,
            type = TimerAction.Type.SUSPEND,
            timestamp = timestamp,
            description = description
        )
        add_timer_action(action)
    }
}