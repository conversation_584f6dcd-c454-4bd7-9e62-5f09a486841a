package com.cpvsn.rm.core.features.task.timer

import com.cpvsn.rm.core.features.task.arrange.enums.ConferenceParticipantRole
import java.time.Instant


/*
https://www.notion.so/capvision/Post-Twilio-Release-Calculate-expert-and-client-duration-and-display-72c94fde775f488bb91a868d499745dc
四个：
(1) expert - begin
(2) expert - suspend
(3) client - begin
(4) client - suspend
 */
data class TimerAction(
    val role: ConferenceParticipantRole,
    val type: Type,
    val timestamp: Instant?,
    val description: String,
) {
    enum class Type {
        BEGIN,
        SUSPEND
    }

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        return this === other
    }

    override fun hashCode(): Int {
        var result = role.hashCode()
        result = 31 * result + type.hashCode()
        result = 31 * result + (timestamp?.hashCode() ?: 0)
        result = 31 * result + description.hashCode()
        return result
    }
}