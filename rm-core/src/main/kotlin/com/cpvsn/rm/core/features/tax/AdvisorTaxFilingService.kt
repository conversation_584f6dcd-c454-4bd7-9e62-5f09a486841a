package com.cpvsn.rm.core.features.tax

import com.cpvsn.rm.core.features.metrics.MetricsMapper
import com.cpvsn.rm.core.features.tax.pojo.AdvisorAnnualPayment
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class AdvisorTaxFilingService {
    //region @
    private val log = LoggerFactory.getLogger(this.javaClass)

    @Autowired
    private lateinit var metricsMapper: MetricsMapper

    @Autowired
    private lateinit var avalara1099ApiClient: Avalara1099ApiClient
    //endregion


    //region aggrgate advisor list
    fun aggregate_advisor_annnual_payment(): List<AdvisorAnnualPayment> {
        //TODO
        val annual_payments = emptyList<AdvisorAnnualPayment>()
        return annual_payments
    }


    fun update_tax_filing_records(
        annual_payments: List<AdvisorAnnualPayment>
    ) {

    }

    //endregion


    //region W9q


    //endregion
}