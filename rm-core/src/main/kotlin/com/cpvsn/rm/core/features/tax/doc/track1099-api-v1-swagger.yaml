---
openapi: 3.0.1
info:
  title: Track1099 API V1
  version: v0.6.0
  description: |
    This is the specification for the Avalara 1099 & W9 API. Some overall notes about the API:

    - The API generally follows the [JSON:API](https://jsonapi.org/) specification
    - Authentication is done by including an API `Bearer` token in the `Authorization` header (API tokens can be generated from your [profile page](/api_tokens) when logged into the application)
    - The maximum request size allowed is **100MB**
paths:
  "/api/v1/{team_api_id}/issuers":
    get:
      summary: List issuers
      tags:
      - Issuers (also known as Payers)
      security:
      - API token: []
      description: List existing issuers for a given tax year.
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: sort
        in: query
        required: false
        example: "-tax_year,name"
        description: |+
          Comma-separated list of fields to sort by, each optionally preceded by a dash (`-`) to indicate descending sort.

          Collections support sorting only on certain fields. An attempt to sort on an unsupported field will receive a `400 Bad Request` response.

        schema:
          type: string
      - name: filter
        in: query
        required: false
        example:
          tax_year_eq: '2024'
        style: deepObject
        description: |+
          Any number of filtering predicates, which will be applied with logical AND. That is, the results will be the intersection of all predicates.

          Due to documentation tool limitations, filter is shown and edited below as JSON, but when making a request predicate-value pairs must be expanded as query parameters in the form `filter[PREDICATE]=value`. The documentation tool does this expansion automatically when you use "Try it out."

          `PREDICATE` is composed of a field name followed by an underscore (`_`) followed by an abbreviated comparison method such as `eq`, `gt`, or `lt`.

          Collections support filtering only on certain fields. An attempt to filter on an unsupported field will receive a `400 Bad Request` response.

        schema:
          type: object
          additionalProperties:
            type: string
      - name: page
        in: query
        required: false
        example:
          number: 1
          size: 100
        style: deepObject
        description: |
          Control pagination with `page[number]` and `page[size]`. `page[size]` outside the supported range will receive a `400 Bad Request` response.

          Due to documentation tool limitations, page parameters are shown and edited below as JSON. The documentation tool automatically reformats the parameters for the request when you use "Try it out."
        schema:
          type: object
          properties:
            number:
              type: integer
              example: 1
              minimum: 1
            size:
              type: integer
              example: 100
              minimum: 1
              maximum: 1000
          required: []
      responses:
        '200':
          description: List of issuers
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      "$ref": "#/components/schemas/issuer"
                  links:
                    type: object
                    properties:
                      self:
                        type: string
                        format: url
                        example: https://track1099.com/api/v1/{team_api_id}/issuers?filter[tax_year_eq]=2024&sort=-tax_year,name
                        description: Link to the current object or collection
                      current:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/issuers?filter[tax_year_eq]=2024&page[number]=2&sort=-tax_year,name
                        format: url
                        description: Link to the current page of the collection
                      first:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/issuers?filter[tax_year_eq]=2024&page[number]=1&sort=-tax_year,name
                        format: url
                        description: Link to the first page of the collection, if
                          current is not the first page
                      prev:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/issuers?filter[tax_year_eq]=2024&page[number]=1&sort=-tax_year,name
                        format: url
                        description: Link to the previous page of the collection,
                          if current is not the first page
                      next:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/issuers?filter[tax_year_eq]=2024&page[number]=3&sort=-tax_year,name
                        format: url
                        description: Link to the next page of the collection, if current
                          is not the last page
                      last:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/issuers?filter[tax_year_eq]=2024&page[number]=5&sort=-tax_year,name
                        format: url
                        description: Link to the last page of the collection, if current
                          is not the last page
                    required:
                    - self
                    - current
                required:
                - data
                - links
        '400':
          description: Bad request (e.g., invalid sort key)
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      allOf:
                      - "$ref": "#/components/schemas/error_400"
                      - type: object
                        properties:
                          detail:
                            type: string
                            example: Sort not supported
                        required: []
                required:
                - errors
        '401':
          description: Authentication failed
    post:
      summary: Create issuer
      tags:
      - Issuers (also known as Payers)
      security:
      - API token: []
      description: Create a new issuer
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      responses:
        '201':
          description: Created issuer
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/issuer"
                required:
                - data
        '422':
          description: Unprocessable entity
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_422"
                required:
                - errors
        '401':
          description: Authentication failed
      requestBody:
        content:
          application/vnd.api+json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    type:
                      type: string
                      example: issuer
                    attributes:
                      type: object
                      properties:
                        name:
                          type: string
                          nullable: true
                          example: ACME Corp
                          description: Legal name, not DBA
                        name_dba:
                          type: string
                          nullable: true
                          example: ''
                          description: Optional DBA name or continuation of a long
                            legal name
                        transfer_agent_name:
                          type: string
                          nullable: true
                          example: ''
                          description: Required if there is a transfer agent; omit
                            otherwise
                        tin:
                          type: string
                          nullable: true
                          example: 23-8234555
                        tax_year:
                          type: integer
                          example: 2024
                          nullable: false
                          immutable: true
                        reference_id:
                          type: string
                          nullable: true
                          example: WB-1949
                          description: Optional identifier for your reference, never
                            shown to any agency or recipient. We will also prefix
                            download filenames with this value, if present.
                        shipping_country_code:
                          type: string
                          nullable: true
                          example: US
                          description: If there is a transfer agent, use the shipping
                            address of the transfer agent.
                        shipping_address:
                          type: string
                          nullable: true
                          example: 1234 Meep Meep Blvd
                        city:
                          type: string
                          nullable: true
                          example: Tucson
                        state:
                          type: string
                          nullable: true
                          example: AZ
                        foreign_province:
                          type: string
                          nullable: true
                          example: 
                        zip:
                          type: string
                          nullable: true
                          example: '85701'
                        telephone:
                          type: string
                          nullable: true
                          example: ************
                        email:
                          type: string
                          nullable: true
                          example: <EMAIL>
                        last_filing:
                          type: boolean
                          example: false
                          nullable: true
                      required:
                      - name
                      - tin
                      - tax_year
                      - shipping_country_code
                      - shipping_address
                      - city
                      - zip
                      - telephone
                      - email
                  required:
                  - type
                  - attributes
              required:
              - data
  "/api/v1/{team_api_id}/issuers/{issuer_id}":
    get:
      summary: Retrieve single issuer
      tags:
      - Issuers (also known as Payers)
      security:
      - API token: []
      description: Retrieve a single issuer by id.
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: issuer_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Single issuer
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/issuer"
                required:
                - data
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
        '401':
          description: Authentication failed
        '403':
          description: Forbidden
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_403"
                required:
                - errors
    patch:
      summary: Update an issuer
      tags:
      - Issuers (also known as Payers)
      security:
      - API token: []
      description: |
        **Update an issuer**

        Any attributes not supplied will remain unchanged. Note that `tax_year` cannot be changed after creation.
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: issuer_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Updated issuer
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/issuer"
                required:
                - data
        '422':
          description: Unprocessable entity
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_422"
                required:
                - errors
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
        '401':
          description: Authentication failed
        '403':
          description: Forbidden
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_403"
                required:
                - errors
      requestBody:
        content:
          application/vnd.api+json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    id:
                      type: string
                      example: '216108'
                    type:
                      type: string
                      example: issuer
                    attributes:
                      type: object
                      properties:
                        name:
                          type: string
                          nullable: true
                          example: ACME Corp
                          description: Legal name, not DBA
                        name_dba:
                          type: string
                          nullable: true
                          example: ''
                          description: Optional DBA name or continuation of a long
                            legal name
                        transfer_agent_name:
                          type: string
                          nullable: true
                          example: ''
                          description: Required if there is a transfer agent; omit
                            otherwise
                        tin:
                          type: string
                          nullable: true
                          example: 23-8234555
                        reference_id:
                          type: string
                          nullable: true
                          example: WB-1949
                          description: Optional identifier for your reference, never
                            shown to any agency or recipient. We will also prefix
                            download filenames with this value, if present.
                        shipping_country_code:
                          type: string
                          nullable: true
                          example: US
                          description: If there is a transfer agent, use the shipping
                            address of the transfer agent.
                        shipping_address:
                          type: string
                          nullable: true
                          example: 1234 Meep Meep Blvd
                        city:
                          type: string
                          nullable: true
                          example: Tucson
                        state:
                          type: string
                          nullable: true
                          example: AZ
                        foreign_province:
                          type: string
                          nullable: true
                          example: 
                        zip:
                          type: string
                          nullable: true
                          example: '85701'
                        telephone:
                          type: string
                          nullable: true
                          example: ************
                        email:
                          type: string
                          nullable: true
                          example: <EMAIL>
                        last_filing:
                          type: boolean
                          example: false
                          nullable: true
                      required: []
                  required:
                  - id
                  - type
              required:
              - data
    delete:
      summary: Delete issuer
      tags:
      - Issuers (also known as Payers)
      security:
      - API token: []
      description: Delete a 1099 issuer and its associated forms if they have not
        been e-filed
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: issuer_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '204':
          description: Issuer deleted
        '422':
          description: Cannot delete issuer
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_422"
                required:
                - errors
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
        '401':
          description: Authentication failed
        '403':
          description: Forbidden
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_403"
                required:
                - errors
  "/api/v1/{team_api_id}/issuers/{issuer_id}/import-forms":
    post:
      summary: Uploads CSV of forms
      tags:
      - 1099 Forms
      security:
      - API token: []
      description: Submit a CSV file of forms. Please see <a href="https://www.track1099.com/info/csv_home">CSV
        Importing</a> to download the various CSV templates.
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: issuer_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '201':
          description: Upload job submitted
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/import_job"
                required:
                - data
        '400':
          description: Missing CSV
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      allOf:
                      - "$ref": "#/components/schemas/error_400"
                      - type: object
                        properties:
                          detail:
                            type: string
                            example: Must include a .csv file as multipart/form-data
                        required: []
                required:
                - errors
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
        '401':
          description: Authentication failed
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                csv_file:
                  type: string
                  format: binary
              required:
              - csv_file
        required: true
  "/api/v1/{team_api_id}/issuers/{issuer_id}/update-forms":
    post:
      summary: Uploads CSV containing updates to forms
      tags:
      - 1099 Forms
      security:
      - API token: []
      description: |-
        Submit a CSV file containing updates to forms. Please see [CSV Importing](https://www.track1099.com/info/csv_home) to download the various CSV templates.
        You may also:
        1. Export your forms to CSV.
        1. Make updates to the downloaded CSV.
        1. Re-import the updated CSV using this endpoint.

        The system will attempt to match records in this order:
        1. Form ID
        1. Reference ID
        1. TIN

        If it can't match, it will skip that row, or create a new record if `upsert` is set to `true`
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: issuer_id
        in: path
        required: true
        schema:
          type: string
      - name: form_type
        in: query
        required: true
        example: 1099-NEC
        description: name of the form you are updating
        schema:
          type: string
      - name: upsert
        in: query
        required: false
        example: false
        description: defaults to false. If true, it will create records that don't
          already exist
        schema:
          type: boolean
      - name: dry_run
        in: query
        required: false
        example: false
        description: defaults to false. If true, it will NOT change the DB. It will
          just return a report of what would've have been changed in the DB
        schema:
          type: boolean
      responses:
        '201':
          description: Update job submitted
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/update_job"
                required:
                - data
        '400':
          description: Missing CSV
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      allOf:
                      - "$ref": "#/components/schemas/error_400"
                      - type: object
                        properties:
                          detail:
                            type: string
                            example: Must include a .csv file as multipart/form-data
                        required: []
                required:
                - errors
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
        '401':
          description: Authentication failed
        '403':
          description: Forbidden
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_403"
                required:
                - errors
      requestBody:
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                csv_file:
                  type: string
                  format: binary
              required:
              - csv_file
        required: true
  "/api/v1/{team_api_id}/authorized_api_requests":
    post:
      summary: create URL for downloads
      tags:
      - Authorized Api Requests
      security:
      - API token: []
      description: Create a URL that can be used to download forms. It will be valid
        until the given Time To Live (TTL) has passed
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      responses:
        '201':
          description: URL Created
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/authorized_api_request"
                required:
                - data
        '400':
          description: Bad request
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_400"
                required:
                - errors
        '401':
          description: Authentication failed
        '422':
          description: Unprocessable entity
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_422"
                required:
                - errors
      requestBody:
        content:
          application/vnd.api+json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    type:
                      type: string
                      example: authorized_api_request
                    attributes:
                      type: object
                      properties:
                        path:
                          type: string
                          immutable: true
                          example: form-pdf?filter[form_type_eq]=1099-NEC&filter[reference_id_eq]=SE-02453450&filter[tax_year_eq]=2024
                          description: The path and query of the API request you want
                            to pre-authorize, omitting the leading `/api/v1/{team_api_id}/`
                        ttl:
                          type: integer
                          example: '3600'
                          immutable: true
                          write_only: true
                          description: Seconds until this AuthorizedApiRequest should
                            expire, 3600 if omitted; values greater than 86400 will
                            not be honored
                      required:
                      - path
                  required:
                  - type
                  - attributes
              required:
              - data
  "/api/v1/{team_api_id}/authorized_api_requests/{authorized_api_request_id}":
    get:
      summary: retrieve URL for downloads
      tags:
      - Authorized Api Requests
      security:
      - API token: []
      description: Retrieve the URL you created
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: authorized_api_request_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Authorized API Request Retrieved
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/authorized_api_request"
                required:
                - data
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
  "/api/v1/{team_api_id}/authorized_api_requests/{authorized_api_request_id}/execute":
    get:
      summary: execute URL for downloads
      tags:
      - Authorized Api Requests
      security:
      - API token: []
      description: Execute the URL you created
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: authorized_api_request_id
        in: path
        required: true
        schema:
          type: string
      - name: r
        in: query
        required: true
        description: |-
          The 'r' query string parameter in the `path` you created in the POST operation.

           `path`: https://www.track1099.com/api/v1/public/authorized_api_requests/15e59a62-bd14-4ac0-b1e0-f45100a2196b/execute?r=`a4Sox99A3DKmmwUY`
        example: a4Sox99A3DKmmwUY
        schema:
          type: string
      responses:
        '200':
          description: Authorized API Request Executed to Retrieve PDF Form
        '404':
          description: Not found
          content:
            application/pdf:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
  "/api/v1/{team_api_id}/w9forms":
    get:
      summary: List W9/W8/W4 Forms
      tags:
      - W9/W8/W4 Forms
      security:
      - API token: []
      description: List W9/W4/W8 forms.
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: sort
        in: query
        required: false
        example: "-type,display_name"
        description: |
          Comma-separated list of fields to sort by, each optionally preceded by a dash (`-`) to indicate descending sort.

          Collections support sorting only on certain fields. An attempt to sort on an unsupported field will receive a `400 Bad Request` response.

          Supported sorting fields are as follows:
          `id`, `type`, `entry_status`, `updated_at`, `reference_id`, `company_id`, `company_reference_id`, `display_name`, `email`, `archived`.
        schema:
          type: string
      - name: filter
        in: query
        required: false
        example:
          type_eq: w9
        style: deepObject
        description: |
          Any number of filtering predicates, which will be applied with logical AND. That is, the results will be the intersection of all predicates.

          Due to documentation tool limitations, filter is shown and edited below as JSON, but when making a request predicate-value pairs must be expanded as query parameters in the form `filter[PREDICATE]=value`. The documentation tool does this expansion automatically when you use "Try it out."

          `PREDICATE` is composed of a field name followed by an underscore (`_`) followed by an abbreviated comparison method such as `eq`, `gt`, or `lt`.

          Collections support filtering only on certain fields. An attempt to filter on an unsupported field will receive a `400 Bad Request` response.

          Supported filtering fields are as follows:
          `id`, `type`, `entry_status`, `updated_at`, `reference_id`, `company_id`, `company_reference_id`, `display_name`, `email`, `archived`.
        schema:
          type: object
          additionalProperties:
            type: string
      - name: page
        in: query
        required: false
        example:
          number: 1
          size: 100
        style: deepObject
        description: |
          Control pagination with `page[number]` and `page[size]`. `page[size]` outside the supported range will receive a `400 Bad Request` response.

          Due to documentation tool limitations, page parameters are shown and edited below as JSON. The documentation tool automatically reformats the parameters for the request when you use "Try it out."
        schema:
          type: object
          properties:
            number:
              type: integer
              example: 1
              minimum: 1
            size:
              type: integer
              example: 100
              minimum: 1
              maximum: 1000
          required: []
      responses:
        '200':
          description: List of W9/W4/W8 Forms
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    minItems: 0
                    items:
                      anyOf:
                      - "$ref": "#/components/schemas/w9_form"
                      - "$ref": "#/components/schemas/w4_form"
                      - "$ref": "#/components/schemas/w8imy_form"
                      - "$ref": "#/components/schemas/w8ben_form"
                      - "$ref": "#/components/schemas/w8bene_form"
                      discriminator: 
                  links:
                    type: object
                    properties:
                      self:
                        type: string
                        format: url
                        example: https://track1099.com/api/v1/{team_api_id}/w9forms?filter[entry_status_eq]=manual&sort=-type,display_name
                        description: Link to the current object or collection
                      current:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/w9forms?filter[entry_status_eq]=manual&page[number]=2&sort=-type,display_name
                        format: url
                        description: Link to the current page of the collection
                      first:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/w9forms?filter[entry_status_eq]=manual&page[number]=1&sort=-type,display_name
                        format: url
                        description: Link to the first page of the collection, if
                          current is not the first page
                      prev:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/w9forms?filter[entry_status_eq]=manual&page[number]=1&sort=-type,display_name
                        format: url
                        description: Link to the previous page of the collection,
                          if current is not the first page
                      next:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/w9forms?filter[entry_status_eq]=manual&page[number]=3&sort=-type,display_name
                        format: url
                        description: Link to the next page of the collection, if current
                          is not the last page
                      last:
                        type: string
                        example: https://track1099.com/api/v1/{team_api_id}/w9forms?filter[entry_status_eq]=manual&page[number]=5&sort=-type,display_name
                        format: url
                        description: Link to the last page of the collection, if current
                          is not the last page
                    required:
                    - self
                    - current
                required:
                - data
                - links
        '400':
          description: Bad request (e.g., invalid sort key)
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      allOf:
                      - "$ref": "#/components/schemas/error_400"
                      - type: object
                        properties:
                          detail:
                            type: string
                            example: Sort not supported
                        required: []
                required:
                - errors
        '401':
          description: Authentication failed
  "/api/v1/{team_api_id}/form-pdf":
    get:
      summary: Retrieve single pdf
      tags:
      - 1099 Forms
      security:
      - API token: []
      description: Retrieve a single 1099 form PDF
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: filter
        in: query
        required: true
        example:
          issuer_reference_id_eq: "<your reference ID for issuer> (optional)"
          reference_id_eq: "<your reference ID for this form recipient>"
          tax_year_eq: '2024'
          form_type_eq: 1099-NEC
        style: deepObject
        description: |+
          Any number of filtering predicates, which will be applied with logical AND. That is, the results will be the intersection of all predicates.

          Due to documentation tool limitations, filter is shown and edited below as JSON, but when making a request predicate-value pairs must be expanded as query parameters in the form `filter[PREDICATE]=value`. The documentation tool does this expansion automatically when you use "Try it out."

          `PREDICATE` is composed of a field name followed by an underscore (`_`) followed by an abbreviated comparison method such as `eq`, `gt`, or `lt`.

          Collections support filtering only on certain fields. An attempt to filter on an unsupported field will receive a `400 Bad Request` response.

        schema:
          type: object
          additionalProperties:
            type: string
      - name: mark_edelivered
        in: query
        required: false
        description: Mark the document retrieved as e-delivered if true
        schema:
          type: boolean
      responses:
        '200':
          description: Single form PDF download
        '400':
          description: Bad request
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_400"
                required:
                - errors
        '401':
          description: Authentication failed
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
  "/api/v1/{team_api_id}/form_requests":
    post:
      summary: Create form request
      tags:
      - Embedded W-9/W-8
      security:
      - API token: []
      description: |-
        Create a W-9, W-8BEN or W-8BEN-E form request for the given `company_id` (ID of a company in the W-9 section of the Track1099 app) and
        your internal `reference_id` for the vendor. `reference_id` is opaque to Track1099 but should be meaningful to you.
        If provided, it must uniquely identify (to you) the person or company from whom you are requesting the form.
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      responses:
        '201':
          description: Created form request
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/form_request"
                required:
                - data
        '422':
          description: Unprocessable entity
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_422"
                required:
                - errors
        '404':
          description: Not found (company)
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
        '401':
          description: Authentication failed
        '403':
          description: Forbidden (Plan does not allow API access)
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_403"
                required:
                - errors
      requestBody:
        content:
          application/vnd.api+json:
            schema:
              type: object
              properties:
                data:
                  type: object
                  properties:
                    type:
                      type: string
                      example: form_request
                    attributes:
                      type: object
                      properties:
                        form_type:
                          type: string
                          enum:
                          - W-9
                          - W-8BEN
                          - W-8BEN-E
                          immutable: true
                          example: W-9
                          description: '"W-9", "W-8BEN" and "W-8BEN-E" are the only
                            supported values'
                        company_id:
                          type: integer
                          example: '2345678'
                          immutable: true
                          description: Track1099's ID of your company, found in the
                            W-9 UI
                        reference_id:
                          type: string
                          immutable: true
                          example: SE-02453450
                          description: Your internal identifier for the vendor from
                            whom you are requesting a form
                        ttl:
                          type: integer
                          example: '3600'
                          immutable: true
                          write_only: true
                          description: Seconds until this FormRequest should expire,
                            3600 if omitted; values greater than 86400 will not be
                            honored
                      required:
                      - form_type
                      - company_id
                  required:
                  - type
                  - attributes
              required:
              - data
  "/api/v1/{team_api_id}/form_requests/{form_request_id}":
    get:
      summary: Get form request
      tags:
      - Embedded W-9/W-8
      security:
      - API token: []
      description: 'Retrieve a form request again after creation: not likely to be
        useful except in testing. Previously-valid form requests will be Not Found
        after `expires_at`.'
      parameters:
      - name: team_api_id
        in: path
        required: true
        description: An identifier used in all API URLs, unique to your team. See
          [where to find it](/api_info/readme#3-copy-your-team-api-id).
        schema:
          type: string
      - name: form_request_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Form Request
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    "$ref": "#/components/schemas/form_request"
                required:
                - data
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  errors:
                    type: array
                    minItems: 1
                    items:
                      "$ref": "#/components/schemas/error_404"
                required:
                - errors
        '401':
          description: Authentication failed
  "/api/v1/{team_api_id}/jobs/{job_id}":
    get:
      summary: Get job status
      tags:
      - Jobs
      security:
      - API token: []
      description: 'Check the status and results of a job. Multiple job types exist:
        check "Schema" in the 200 response below for all possibilities.'
      parameters:
      - name: team_api_id
        in: path
        required: true
        schema:
          type: string
      - name: job_id
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Job status
          content:
            application/vnd.api+json:
              schema:
                type: object
                properties:
                  data:
                    type: object
                    oneOf:
                    - "$ref": "#/components/schemas/import_job"
                    - "$ref": "#/components/schemas/update_job"
                    properties: {}
                    required: []
                required:
                - data
        '404':
          description: Not found
          content:
            application/vnd.api+json:
              schema:
                "$ref": "#/components/schemas/errors"
        '401':
          description: Authentication failed
components:
  securitySchemes:
    API token:
      type: http
      scheme: bearer
      description: If you don't have one already, get an API token from the [API tab
        of your Account page](/api_tokens).
  schemas:
    issuer:
      type: object
      properties:
        id:
          type: string
          example: '216108'
        type:
          type: string
          example: issuer
        attributes:
          type: object
          properties:
            name:
              type: string
              nullable: true
              example: ACME Corp
              description: Legal name, not DBA
            name_dba:
              type: string
              nullable: true
              example: ''
              description: Optional DBA name or continuation of a long legal name
            transfer_agent_name:
              type: string
              nullable: true
              example: ''
              description: Required if there is a transfer agent; omit otherwise
            tin:
              type: string
              nullable: true
              example: 23-8234555
            tax_year:
              type: integer
              example: 2024
              nullable: false
              immutable: true
            reference_id:
              type: string
              nullable: true
              example: WB-1949
              description: Optional identifier for your reference, never shown to
                any agency or recipient. We will also prefix download filenames with
                this value, if present.
            shipping_country_code:
              type: string
              nullable: true
              example: US
              description: If there is a transfer agent, use the shipping address
                of the transfer agent.
            shipping_address:
              type: string
              nullable: true
              example: 1234 Meep Meep Blvd
            city:
              type: string
              nullable: true
              example: Tucson
            state:
              type: string
              nullable: true
              example: AZ
            foreign_province:
              type: string
              nullable: true
              example: 
            zip:
              type: string
              nullable: true
              example: '85701'
            telephone:
              type: string
              nullable: true
              example: ************
            email:
              type: string
              nullable: true
              example: <EMAIL>
            last_filing:
              type: boolean
              example: false
              nullable: true
            name_for_download:
              type: string
              example: WB-1949 ACME Corp
              read_only: true
              description: Download filename, derived from `reference_id` and `name`
            created_at:
              type: string
              format: date-time
              nullable: false
              read_only: true
              example: '2022-04-29T15:19:42.995-04:00'
            updated_at:
              type: string
              format: date-time
              nullable: false
              read_only: true
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - name
          - tin
          - tax_year
          - shipping_country_code
          - shipping_address
          - city
          - zip
          - telephone
          - email
        relationships:
          type: object
          properties:
            group:
              type: object
              properties:
                data:
                  type: object
                  nullable: true
                  properties:
                    id:
                      type: string
                      example: '0'
                    type:
                      type: string
                      example: 
                  required:
                  - id
                  - type
              required:
              - data
          required: []
        links:
          type: object
          properties:
            self:
              type: string
              format: url
              nullable: false
              example: "/api/v1/{team_api_id}/issuers/216108"
          required: []
      required:
      - id
      - type
      - links
    b_record:
      type: object
      properties:
        id:
          type: string
          example: '125398'
        type:
          type: string
          example: 1099_form
        attributes:
          type: object
          properties:
            validation_errors:
              type: array
              nullable: true
              items:
                "$ref": "#/components/schemas/ValidationErrors"
            api_issuer_id:
              type: integer
              example: 7
              immutable: true
            api_issuer_reference_id:
              type: string
              nullable: true
              immutable: true
              example: string
            api_issuer_tin:
              type: string
              nullable: true
              immutable: true
              example: string
            api_tax_year:
              type: integer
              example: 3
              immutable: true
            type:
              type: string
              nullable: false
              immutable: true
              example: string
            api_federal_efile:
              type: boolean
              example: true
            federal_efile_status:
              "$ref": "#/components/schemas/FederalEfileStatus"
            api_state_efile:
              type: boolean
              example: true
            state_efile_status:
              type: array
              items:
                "$ref": "#/components/schemas/StateEfileStatus"
            api_postal_mail:
              type: boolean
              example: true
            postal_mail_status:
              "$ref": "#/components/schemas/PostalMailStatus"
            api_tin_match:
              type: boolean
              example: true
            tin_match_status:
              "$ref": "#/components/schemas/TINMatchStatus"
            api_address_verification:
              type: boolean
              example: true
            address_verification_status:
              "$ref": "#/components/schemas/AddressVerificationStatus"
            reference_id:
              type: string
              nullable: true
              example: string
            email:
              type: string
              nullable: true
              example: string
            type_of_tin:
              type: string
              nullable: true
              example: string
            tin:
              type: string
              nullable: true
              example: string
            no_tin:
              type: boolean
              example: true
              nullable: false
            first_payee_name:
              type: string
              nullable: true
              example: string
            second_payee_name:
              type: string
              nullable: true
              example: string
            address:
              type: string
              nullable: true
              example: string
            address_recipient_second:
              type: string
              nullable: true
              example: string
            city:
              type: string
              nullable: true
              example: string
            state:
              type: string
              nullable: true
              example: string
            zip:
              type: string
              nullable: true
              example: string
            foreign_province:
              type: string
              nullable: true
              example: string
            mailing_country_code:
              type: string
              nullable: true
              example: string
            box_state:
              type: string
              nullable: true
              example: string
            box_state_id_no:
              type: string
              nullable: true
              example: string
            box_state_income:
              type: number
              example: 2.34
              nullable: true
            box_state_tax_withheld:
              type: number
              example: 2.34
              nullable: true
            box_locality:
              type: string
              nullable: true
              example: string
            box_local_income:
              type: number
              example: 2.34
              nullable: true
            box_local_tax_withheld:
              type: number
              example: 2.34
              nullable: true
            e_delivery_status:
              "$ref": "#/components/schemas/EDeliveryStatus"
            created_at:
              type: string
              format: date-time
              nullable: false
              read_only: true
              example: '2022-04-29T15:19:42.995-04:00'
            updated_at:
              type: string
              format: date-time
              nullable: false
              read_only: true
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - type
      required:
      - id
      - type
    import_job:
      type: object
      properties:
        id:
          type: string
          example: import-348948
        type:
          type: string
          example: import_job
        attributes:
          type: object
          properties:
            original_filename:
              type: string
              example: some_file.csv
            status:
              type: string
              enum:
              - IN_PROGRESS
              - SUCCESS
              - FAILED
              example: IN_PROGRESS
            error_message:
              type: string
              nullable: true
              example: 
            forms_imported_count:
              type: integer
              example: 0
            forms_ok_count:
              type: integer
              example: 
              nullable: true
            forms_missing_email_count:
              type: integer
              example: 
              nullable: true
            forms_error_count:
              type: integer
              example: 
              nullable: true
          required:
          - forms_imported_count
        relationships:
          type: object
          properties:
            issuer:
              type: object
              properties:
                data:
                  type: object
                  nullable: true
                  properties:
                    id:
                      type: string
                      example: '216108'
                    type:
                      type: string
                      example: issuer
                  required:
                  - id
                  - type
                links:
                  type: object
                  properties:
                    related:
                      type: string
                      example: "/api/v1/{team_api_id}/issuers/216108"
                  required: []
              required:
              - data
          required: []
        links:
          type: object
          properties:
            self:
              type: string
              format: url
              nullable: false
              example: "/api/v1/{team_api_id}/jobs/import-348948"
              description: Link to retrieve the status and progress of this job as
                it is processed
          required: []
      required:
      - id
      - type
      - links
    update_job:
      type: object
      properties:
        id:
          type: string
          example: update-206388
        type:
          type: string
          example: update_job
        attributes:
          type: object
          properties:
            original_filename:
              type: string
              example: some_file.csv
            dry_run:
              type: boolean
              example: false
            upsert:
              type: boolean
              example: false
            status:
              type: string
              enum:
              - IN_PROGRESS
              - SUCCESS
              - FAILED
              example: IN_PROGRESS
            error_message:
              type: string
              nullable: true
              example: 
            total_processed:
              type: integer
              example: 1
              description: Total number of rows processed in the CSV
            total_rows:
              type: integer
              example: 3
              description: Total number of rows in the CSV
            updated_valid:
              type: integer
              example: 1
              description: Forms updated and valid for e-filing and e-delivery
            updated_no_email:
              type: integer
              example: 1
              description: Forms updated and valid for e-filing but missing email
                or email is undeliverable
            updated_invalid:
              type: integer
              example: 42
              description: Forms updated but invalid for e-filing
            skipped_duplicate:
              type: integer
              example: 3
              description: Updates skipped because they would have updated a record
                already updated once in this file
            skipped_invalid:
              type: integer
              example: 1
              description: Updates skipped because they would have made a form invalid
                and the form is already e-filed or scheduled for e-filing
            skipped_multiple_matches:
              type: integer
              example: 7
              description: Updates skipped because the CSV row matched multiple forms
            not_found:
              type: integer
              example: 42
              description: Rows skipped because no matching form or issuer could be
                found
            created_invalid:
              type: integer
              example: 10
              description: New forms created because no matching form could be found
                (and `upsert` was true) - with errors
            created_no_email:
              type: integer
              example: 0
              description: New forms created because no matching form could be found
                (and `upsert` was true) - valid for e-filing but missing email or
                email is undeliverable
            created_valid:
              type: integer
              example: 0
              description: New forms created because no matching form could be found
                (and `upsert` was true) - valid for e-filing and e-delivery
          required:
          - total_processed
          - total_rows
          - updated_valid
          - updated_no_email
          - updated_invalid
          - skipped_duplicate
          - skipped_invalid
          - skipped_multiple_matches
          - not_found
          - created_invalid
          - created_no_email
          - created_valid
        relationships:
          type: object
          properties:
            issuer:
              type: object
              properties:
                data:
                  type: object
                  nullable: true
                  properties:
                    id:
                      type: string
                      example: '216108'
                    type:
                      type: string
                      example: issuer
                  required:
                  - id
                  - type
                links:
                  type: object
                  properties:
                    related:
                      type: string
                      example: "/api/v1/{team_api_id}/issuers/216108"
                  required: []
              required:
              - data
          required: []
        links:
          type: object
          properties:
            self:
              type: string
              format: url
              nullable: false
              example: "/api/v1/{team_api_id}/jobs/update-206388"
              description: Link to retrieve the status and progress of this job as
                it is processed
          required: []
      required:
      - id
      - type
      - links
    form_request:
      type: object
      properties:
        id:
          type: string
          example: d0d09a2f-619e-4e9d-aae4-3311d9e0c67c
        type:
          type: string
          example: form_request
        attributes:
          type: object
          properties:
            form_type:
              type: string
              enum:
              - W-9
              - W-8BEN
              - W-8BEN-E
              immutable: true
              example: W-9
              description: '"W-9", "W-8BEN" and "W-8BEN-E" are the only supported
                values'
            company_id:
              type: integer
              example: '2345678'
              immutable: true
              description: Track1099's ID of your company, found in the W-9 UI
            company_name:
              type: string
              read_only: true
              example: ACME Corp
              description: Name of your company, set in the W-9 UI
            company_email:
              type: string
              read_only: true
              example: <EMAIL>
              description: Contact email of your company, set in the W-9 UI
            reference_id:
              type: string
              immutable: true
              example: SE-02453450
              description: Your internal identifier for the vendor from whom you are
                requesting a form
            form_id:
              type: integer
              example: '8765432'
              read_only: true
              nullable: true
              description: The Avalara ID of the signed form
            signed_at:
              type: string
              format: date-time
              read_only: true
              nullable: true
              description: The timestamp this vendor (identified by your reference_id)
                last signed a complete W-9, W-8BEN, or W-8BEN-E, or null if you did
                not include a reference_id or the vendor has not yet signed a form
                of that type in Track1099
              example: '2022-04-29T15:19:42.995-04:00'
            tin_match_status:
              type: string
              read_only: true
              nullable: true
              example: matched
              description: Result of IRS TIN match query for name and TIN in the last
                signed form, null if signed_at is null
            expires_at:
              type: string
              format: date-time
              read_only: true
              description: Timestamp when this FormRequest will expire, `ttl` (or
                3600) seconds from creation
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - form_type
          - company_id
        links:
          type: object
          properties:
            signed_pdf:
              type: string
              format: url
              nullable: true
              example: 
              description: URL of PDF representation of just-signed form, otherwise
                null. Integrations may use this value to offer a "download for your
                records" function after a vendor completes and signs a form. Link
                expires at the same time as this FormRequest. Treat the format of
                this URL as opaque and expect it to change in the future.
            action_validate:
              type: string
              format: url
              nullable: false
              example: "/api/v1/public/form_requests/d0d09a2f-619e-4e9d-aae4-3311d9e0c67c/validate"
              description: URL to [re-]validate data for requested form. Behavior
                is not documented; INTERNAL USE ONLY.
            action_complete:
              type: string
              format: url
              nullable: false
              example: "/api/v1/public/form_requests/d0d09a2f-619e-4e9d-aae4-3311d9e0c67c/complete"
              description: URL to submit data to complete requested form. Behavior
                is not documented; INTERNAL USE ONLY.
          required: []
      required:
      - id
      - type
      - links
    authorized_api_request:
      type: object
      properties:
        id:
          type: string
          example: '854848'
        type:
          type: string
          example: authorized_api_request
        attributes:
          type: object
          properties:
            path:
              type: string
              immutable: true
              example: form-pdf?filter[form_type_eq]=1099-NEC&filter[reference_id_eq]=SE-02453450&filter[tax_year_eq]=2024
              description: The path and query of the API request you want to pre-authorize,
                omitting the leading `/api/v1/{team_api_id}/`
            expires_at:
              type: string
              format: date-time
              read_only: true
              description: Timestamp when this AuthorizedApiRequest will expire, `ttl`
                (or 3600) seconds from creation
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - path
        links:
          type: object
          properties:
            self:
              type: string
              format: url
              nullable: false
              example: "/api/v1/418038/authorized_api_requests/854848"
              description: Link to retrieve or delete this AuthorizedApiRequest
            execute:
              type: string
              format: url
              nullable: false
              example: https://www.track1099.com/api/v1/public/authorized_api_requests/15e59a62-bd14-4ac0-b1e0-f45100a2196b/execute?r=a4Sox99A3DKmmwUY
              description: Full one-time-use URL that, when accessed, executes the
                API request specified in `path` using the account and credentials
                you used to create this `AuthorizedApiRequest`
          required: []
      required:
      - id
      - type
      - links
    w9_form:
      type: object
      properties:
        id:
          type: string
          example: '50864'
        type:
          type: string
          example: w9
        attributes:
          type: object
          properties:
            id:
              type: integer
              example: *********
              nullable: false
            type:
              type: string
              nullable: true
              example: w9
            tin_match_status:
              type: string
              example: string
            entry_status:
              type: string
              nullable: true
              example: string
            signed_date:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            archived:
              type: boolean
              example: true
              nullable: true
            reference_id:
              type: string
              nullable: true
              example: string
            company_id:
              type: integer
              example: 2
              nullable: true
            display_name:
              type: string
              nullable: true
              example: string
            email:
              type: string
              nullable: true
              example: string
            name:
              type: string
              nullable: true
              example: string
            business_classification:
              type: string
              nullable: true
              example: string
            business_name:
              type: string
              nullable: true
              example: string
            type_of_tin:
              type: string
              nullable: true
              example: string
            tin:
              type: string
              nullable: true
              example: string
            address:
              type: string
              nullable: true
              example: string
            city:
              type: string
              nullable: true
              example: string
            state:
              type: string
              nullable: true
              example: string
            zip:
              type: string
              nullable: true
              example: string
            account_number:
              type: string
              nullable: true
              example: string
            foreign_country_indicator:
              type: boolean
              example: true
              nullable: true
            foreign_address:
              type: string
              nullable: true
              example: string
            backup_withholding:
              type: boolean
              example: true
              nullable: true
            e_delivery_consented_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            foreign_partner_owner_or_beneficiary:
              type: boolean
              example: true
              nullable: true
            updated_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            created_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - id
          - entry_status
        relationships:
          type: object
          properties:
            company:
              type: object
              properties:
                data:
                  type: object
                  nullable: true
                  properties:
                    id:
                      type: string
                      example: '0'
                    type:
                      type: string
                      example: 
                  required:
                  - id
                  - type
              required:
              - data
          required: []
      required:
      - id
      - type
    w4_form:
      type: object
      properties:
        id:
          type: string
          example: '24624'
        type:
          type: string
          example: w4
        attributes:
          type: object
          properties:
            id:
              type: integer
              example: *********
              nullable: false
            type:
              type: string
              nullable: true
              example: w4
            entry_status:
              type: integer
              example: signed
              nullable: true
            signed_date:
              type: string
              format: date-time
              nullable: true
              example: '20241002'
            archived:
              type: boolean
              example: true
              nullable: true
            reference_id:
              type: string
              nullable: true
              example: string
            company_id:
              type: integer
              example: 2
              nullable: true
            created_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            updated_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            display_name:
              type: string
              nullable: true
              example: string
            email:
              type: string
              nullable: true
              example: string
            employee_first_name:
              type: string
              nullable: true
              example: string
            employee_middle_name:
              type: string
              nullable: true
              example: string
            employee_last_name:
              type: string
              nullable: true
              example: string
            employee_name_suffix:
              type: string
              nullable: true
              example: string
            address:
              type: string
              nullable: true
              example: string
            city:
              type: string
              nullable: true
              example: string
            state:
              type: string
              nullable: true
              example: string
            zip:
              type: string
              nullable: true
              example: string
            tin:
              type: string
              nullable: true
              example: string
            box3_marital_status:
              type: string
              nullable: true
              example: string
            box4_last_name_differs:
              type: boolean
              example: true
              nullable: true
            box5_num_allowances:
              type: integer
              example: 42
              nullable: true
            other_dependents:
              type: integer
              example: 42
              nullable: true
            non_job_income:
              type: number
              example: 2.34
              nullable: true
            deductions:
              type: number
              example: 2.34
              nullable: true
            box6_additional_withheld:
              type: number
              example: 2.34
              nullable: true
            box7_exempt_from_withholding:
              type: boolean
              example: true
              nullable: true
            office_code:
              type: string
              nullable: true
              example: string
            e_delivery_consented_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - id
          - entry_status
        relationships:
          type: object
          properties:
            company:
              type: object
              properties:
                data:
                  type: object
                  nullable: true
                  properties:
                    id:
                      type: string
                      example: '0'
                    type:
                      type: string
                      example: 
                  required:
                  - id
                  - type
              required:
              - data
          required: []
      required:
      - id
      - type
    w8imy_form:
      type: object
      properties:
        id:
          type: string
          example: '61710'
        type:
          type: string
          example: w8imy
        attributes:
          type: object
          properties:
            id:
              type: integer
              example: *********
              nullable: false
            type:
              type: string
              nullable: true
              example: w8imy
            entry_status:
              type: integer
              example: signed
              nullable: true
            signed_date:
              type: string
              format: date-time
              nullable: true
              example: '20241002'
            disregarded_entity_chapter_4_fatca_status:
              type: string
              nullable: true
              example: string
            archived:
              type: boolean
              example: true
              nullable: true
            reference_id:
              type: string
              nullable: true
              example: string
            company_id:
              type: integer
              example: 2
              nullable: true
            created_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            updated_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            display_name:
              type: string
              nullable: true
              example: string
            email:
              type: string
              nullable: true
              example: string
            name:
              type: string
              nullable: true
              example: string
            citizenship_country:
              type: string
              nullable: true
              example: string
            chapter_3_entity_type:
              type: string
              nullable: true
              example: string
            chapter_4_fatca_status:
              type: string
              nullable: true
              example: string
            residence_address:
              type: string
              nullable: true
              example: string
            residence_city:
              type: string
              nullable: true
              example: string
            residence_state:
              type: string
              nullable: true
              example: string
            residence_postal_code:
              type: string
              nullable: true
              example: string
            residence_country:
              type: string
              nullable: true
              example: string
            residence_is_mailing:
              type: boolean
              example: true
              nullable: true
            mailing_address:
              type: string
              nullable: true
              example: string
            mailing_city:
              type: string
              nullable: true
              example: string
            mailing_state:
              type: string
              nullable: true
              example: string
            mailing_postal_code:
              type: string
              nullable: true
              example: string
            mailing_country:
              type: string
              nullable: true
              example: string
            disregarded_entity:
              type: string
              nullable: true
              example: string
            disregarded_address:
              type: string
              nullable: true
              example: string
            disregarded_city:
              type: string
              nullable: true
              example: string
            disregarded_state:
              type: string
              nullable: true
              example: string
            disregarded_postal_code:
              type: string
              nullable: true
              example: string
            disregarded_country:
              type: string
              nullable: true
              example: string
            disregarded_entity_giin:
              type: string
              nullable: true
              example: string
            ein:
              type: string
              nullable: true
              example: string
            ein_type:
              type: string
              nullable: true
              example: string
            giin:
              type: string
              nullable: true
              example: string
            foreign_tin:
              type: string
              nullable: true
              example: string
            reference_number:
              type: string
              nullable: true
              example: string
            certify_box_14:
              type: boolean
              example: true
              nullable: true
            certify_box_15a:
              type: boolean
              example: true
              nullable: true
            certify_box_15b:
              type: boolean
              example: true
              nullable: true
            certify_box_15c:
              type: boolean
              example: true
              nullable: true
            certify_box_15d:
              type: boolean
              example: true
              nullable: true
            certify_box_15e:
              type: boolean
              example: true
              nullable: true
            certify_box_15f:
              type: boolean
              example: true
              nullable: true
            certify_box_15g:
              type: boolean
              example: true
              nullable: true
            certify_box_15h:
              type: boolean
              example: true
              nullable: true
            certify_box_15i:
              type: boolean
              example: true
              nullable: true
            certify_box_16a:
              type: boolean
              example: true
              nullable: true
            box_16b_qdd_corporate:
              type: boolean
              example: true
              nullable: true
            box_16b_qdd_partnership:
              type: boolean
              example: true
              nullable: true
            box_16b_qdd_disregarded_entity:
              type: boolean
              example: true
              nullable: true
            certify_box_17a:
              type: boolean
              example: true
              nullable: true
            certify_box_17b:
              type: boolean
              example: true
              nullable: true
            certify_box_17c:
              type: boolean
              example: true
              nullable: true
            certify_box_17d:
              type: boolean
              example: true
              nullable: true
            certify_box_17e:
              type: boolean
              example: true
              nullable: true
            certify_box_18a:
              type: boolean
              example: true
              nullable: true
            certify_box_18b:
              type: boolean
              example: true
              nullable: true
            certify_box_18c:
              type: boolean
              example: true
              nullable: true
            certify_box_18d:
              type: boolean
              example: true
              nullable: true
            certify_box_18e:
              type: boolean
              example: true
              nullable: true
            certify_box_18f:
              type: boolean
              example: true
              nullable: true
            certify_box_19a:
              type: boolean
              example: true
              nullable: true
            certify_box_19b:
              type: boolean
              example: true
              nullable: true
            certify_box_19c:
              type: boolean
              example: true
              nullable: true
            certify_box_19d:
              type: boolean
              example: true
              nullable: true
            certify_box_19e:
              type: boolean
              example: true
              nullable: true
            certify_box_19f:
              type: boolean
              example: true
              nullable: true
            certify_box_20:
              type: boolean
              example: true
              nullable: true
            certify_box_21a:
              type: boolean
              example: true
              nullable: true
            certify_box_21b:
              type: boolean
              example: true
              nullable: true
            certify_box_21c:
              type: boolean
              example: true
              nullable: true
            certify_box_21d:
              type: boolean
              example: true
              nullable: true
            certify_box_21e:
              type: boolean
              example: true
              nullable: true
            certify_box_21f:
              type: boolean
              example: true
              nullable: true
            certify_box_22:
              type: boolean
              example: true
              nullable: true
            box_23a_name_sponsoring_entity:
              type: string
              nullable: true
              example: string
            certify_box_23b:
              type: boolean
              example: true
              nullable: true
            certify_box_23c:
              type: boolean
              example: true
              nullable: true
            certify_box_24a:
              type: boolean
              example: true
              nullable: true
            certify_box_24b:
              type: boolean
              example: true
              nullable: true
            certify_box_24c:
              type: boolean
              example: true
              nullable: true
            certify_box_25:
              type: boolean
              example: true
              nullable: true
            certify_box_26:
              type: boolean
              example: true
              nullable: true
            box_27a_name_sponsoring_entity:
              type: string
              nullable: true
              example: string
            certify_box_27b:
              type: boolean
              example: true
              nullable: true
            certify_box_28:
              type: boolean
              example: true
              nullable: true
            certify_box_29:
              type: boolean
              example: true
              nullable: true
            certify_box_30a:
              type: boolean
              example: true
              nullable: true
            certify_box_30b:
              type: boolean
              example: true
              nullable: true
            certify_box_30c:
              type: boolean
              example: true
              nullable: true
            certify_box_31:
              type: boolean
              example: true
              nullable: true
            certify_box_32:
              type: boolean
              example: true
              nullable: true
            box_32_iga_country:
              type: string
              nullable: true
              example: string
            box_32_iga_type:
              type: string
              nullable: true
              example: string
            box_32_iga_treated_as:
              type: string
              nullable: true
              example: string
            box_32_trustee_or_sponsor:
              type: string
              nullable: true
              example: string
            box_32_trustee_is_foreign:
              type: boolean
              example: true
              nullable: true
            certify_box_33a:
              type: boolean
              example: true
              nullable: true
            certify_box_33b:
              type: boolean
              example: true
              nullable: true
            certify_box_33c:
              type: boolean
              example: true
              nullable: true
            certify_box_33d:
              type: boolean
              example: true
              nullable: true
            certify_box_33e:
              type: boolean
              example: true
              nullable: true
            certify_box_33f:
              type: boolean
              example: true
              nullable: true
            certify_box_34:
              type: boolean
              example: true
              nullable: true
            certify_box_35:
              type: boolean
              example: true
              nullable: true
            certify_box_36:
              type: boolean
              example: true
              nullable: true
            certify_box_37a:
              type: boolean
              example: true
              nullable: true
            box_37a_securities_market:
              type: string
              nullable: true
              example: string
            certify_box_37b:
              type: boolean
              example: true
              nullable: true
            box_37b_name_of_entity:
              type: string
              nullable: true
              example: string
            box_37b_securities_market:
              type: string
              nullable: true
              example: string
            certify_box_38:
              type: boolean
              example: true
              nullable: true
            certify_box_39:
              type: boolean
              example: true
              nullable: true
            certify_box_40:
              type: boolean
              example: true
              nullable: true
            box_41_sponsoring_entity:
              type: string
              nullable: true
              example: string
            certify_box_42:
              type: boolean
              example: true
              nullable: true
            signer_name:
              type: string
              nullable: true
              example: string
            e_delivery_consented_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - id
          - entry_status
        relationships:
          type: object
          properties:
            company:
              type: object
              properties:
                data:
                  type: object
                  nullable: true
                  properties:
                    id:
                      type: string
                      example: '0'
                    type:
                      type: string
                      example: 
                  required:
                  - id
                  - type
              required:
              - data
          required: []
      required:
      - id
      - type
    w8ben_form:
      type: object
      properties:
        id:
          type: string
          example: '108900'
        type:
          type: string
          example: w8ben
        attributes:
          type: object
          properties:
            id:
              type: integer
              example: *********
              nullable: false
            type:
              type: string
              nullable: true
              example: w8ben
            entry_status:
              type: integer
              example: signed
              nullable: true
            signed_date:
              type: string
              format: date-time
              nullable: true
              example: '20241002'
            birthday:
              type: string
              format: date
              nullable: true
              example: date
            foreign_tin_not_required:
              type: boolean
              example: true
            archived:
              type: boolean
              example: true
              nullable: true
            reference_id:
              type: string
              nullable: true
              example: string
            company_id:
              type: integer
              example: 2
              nullable: true
            display_name:
              type: string
              nullable: true
              example: string
            email:
              type: string
              nullable: true
              example: string
            name:
              type: string
              nullable: true
              example: string
            residence_address:
              type: string
              nullable: true
              example: string
            residence_city:
              type: string
              nullable: true
              example: string
            residence_state:
              type: string
              nullable: true
              example: string
            residence_postal_code:
              type: string
              nullable: true
              example: string
            residence_country:
              type: string
              nullable: true
              example: string
            mailing_address:
              type: string
              nullable: true
              example: string
            mailing_city:
              type: string
              nullable: true
              example: string
            mailing_state:
              type: string
              nullable: true
              example: string
            mailing_postal_code:
              type: string
              nullable: true
              example: string
            mailing_country:
              type: string
              nullable: true
              example: string
            tin:
              type: string
              nullable: true
              example: string
            foreign_tin:
              type: string
              nullable: true
              example: string
            reference_number:
              type: string
              nullable: true
              example: string
            citizenship_country:
              type: string
              nullable: true
              example: string
            treaty_country:
              type: string
              nullable: true
              example: string
            treaty_article:
              type: string
              nullable: true
              example: string
            withholding_rate:
              type: string
              nullable: true
              example: string
            income_type:
              type: string
              nullable: true
              example: string
            treaty_reasons:
              type: string
              nullable: true
              example: string
            signer_name:
              type: string
              nullable: true
              example: string
            signer_capacity:
              type: string
              nullable: true
              example: string
            e_delivery_consented_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            created_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            updated_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - id
          - entry_status
        relationships:
          type: object
          properties:
            company:
              type: object
              properties:
                data:
                  type: object
                  nullable: true
                  properties:
                    id:
                      type: string
                      example: '0'
                    type:
                      type: string
                      example: 
                  required:
                  - id
                  - type
              required:
              - data
          required: []
      required:
      - id
      - type
    w8bene_form:
      type: object
      properties:
        id:
          type: string
          example: '149760'
        type:
          type: string
          example: w8bene
        attributes:
          type: object
          properties:
            id:
              type: integer
              example: *********
              nullable: false
            type:
              type: string
              nullable: true
              example: w8bene
            entry_status:
              type: integer
              example: signed
              nullable: true
            signed_date:
              type: string
              format: date-time
              nullable: true
              example: '20241002'
            disregarded_entity_chapter_4_fatca_status:
              type: string
              nullable: true
              example: string
            archived:
              type: boolean
              example: true
              nullable: true
            reference_id:
              type: string
              nullable: true
              example: string
            company_id:
              type: integer
              example: 2
              nullable: true
            created_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            updated_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
            display_name:
              type: string
              nullable: true
              example: string
            email:
              type: string
              nullable: true
              example: string
            name:
              type: string
              nullable: true
              example: string
            residence_address:
              type: string
              nullable: true
              example: string
            residence_city:
              type: string
              nullable: true
              example: string
            residence_state:
              type: string
              nullable: true
              example: string
            residence_postal_code:
              type: string
              nullable: true
              example: string
            residence_country:
              type: string
              nullable: true
              example: string
            mailing_address:
              type: string
              nullable: true
              example: string
            mailing_city:
              type: string
              nullable: true
              example: string
            mailing_state:
              type: string
              nullable: true
              example: string
            mailing_postal_code:
              type: string
              nullable: true
              example: string
            mailing_country:
              type: string
              nullable: true
              example: string
            disregarded_address:
              type: string
              nullable: true
              example: string
            disregarded_city:
              type: string
              nullable: true
              example: string
            disregarded_state:
              type: string
              nullable: true
              example: string
            disregarded_postal_code:
              type: string
              nullable: true
              example: string
            disregarded_country:
              type: string
              nullable: true
              example: string
            tin:
              type: string
              nullable: true
              example: string
            foreign_tin:
              type: string
              nullable: true
              example: string
            ftin_not_required:
              type: boolean
              example: true
              nullable: true
            reference_number:
              type: string
              nullable: true
              example: string
            giin:
              type: string
              nullable: true
              example: string
            chapter_3_entity_type:
              type: string
              nullable: true
              example: string
            chapter_4_fatca_status:
              type: string
              nullable: true
              example: string
            disregarded_entity:
              type: string
              nullable: true
              example: string
            disregarded_entity_giin:
              type: string
              nullable: true
              example: string
            benefit_limitation:
              type: string
              nullable: true
              example: string
            part_4_sponsoring_entity:
              type: string
              nullable: true
              example: string
            part_4_sponsoring_entity_giin:
              type: string
              nullable: true
              example: string
            part_7_sponsoring_entity:
              type: string
              nullable: true
              example: string
            part_12_iga_country:
              type: string
              nullable: true
              example: string
            part_12_iga_type:
              type: string
              nullable: true
              example: string
            part_12_fatca_status_under_iga_annex_ii:
              type: string
              nullable: true
              example: string
            part_12_trustee_name:
              type: string
              nullable: true
              example: string
            part_12_trustee_is_foreign:
              type: boolean
              example: true
              nullable: true
            part_12_model_2_iga_giin:
              type: string
              nullable: true
              example: string
            box_37a_exchange:
              type: string
              nullable: true
              example: string
            box_37b_exchange:
              type: string
              nullable: true
              example: string
            box_37b_entity:
              type: string
              nullable: true
              example: string
            part_28_sponsoring_entity:
              type: string
              nullable: true
              example: string
            part_28_sponsoring_entity_giin:
              type: string
              nullable: true
              example: string
            signer_name:
              type: string
              nullable: true
              example: string
            e_delivery_consented_at:
              type: string
              format: date-time
              nullable: true
              example: '2022-04-29T15:19:42.995-04:00'
          required:
          - id
          - entry_status
        relationships:
          type: object
          properties:
            company:
              type: object
              properties:
                data:
                  type: object
                  nullable: true
                  properties:
                    id:
                      type: string
                      example: '0'
                    type:
                      type: string
                      example: 
                  required:
                  - id
                  - type
              required:
              - data
          required: []
      required:
      - id
      - type
    covered_individual:
      type: object
      properties:
        id:
          type: string
          example: '428400'
        type:
          type: string
          example: covered_individual
        attributes:
          type: object
          properties:
            id:
              type: integer
              example: 2
              nullable: false
            first_name:
              type: string
              nullable: true
              example: string
            middle_name:
              type: string
              nullable: true
              example: string
            last_name:
              type: string
              nullable: true
              example: string
            name_suffix:
              type: string
              nullable: true
              example: string
            tin:
              type: string
              nullable: true
              example: string
            covered_month_indicator_0:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_1:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_2:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_3:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_4:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_5:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_6:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_7:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_8:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_9:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_10:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_11:
              type: boolean
              example: true
              nullable: true
            covered_month_indicator_12:
              type: boolean
              example: true
              nullable: true
          required: []
      required:
      - id
      - type
    ma_covered_individual:
      type: object
      properties:
        id:
          type: string
          example: '428400'
        type:
          type: string
          example: covered_individual
        attributes:
          type: object
          properties:
            id:
              type: integer
              example: 2
              nullable: false
            first_name:
              type: string
              nullable: true
              example: string
            middle_name:
              type: string
              nullable: true
              example: string
            last_name:
              type: string
              nullable: true
              example: string
            name_suffix:
              type: string
              nullable: true
              example: string
            subscriber_number:
              type: string
              nullable: true
              example: string
            covered_for_full_year:
              type: boolean
              example: true
          required: []
      required:
      - id
      - type
    offer_and_coverage:
      type: object
      properties:
        id:
          type: string
          example: '269424'
        type:
          type: string
          example: offer_and_coverage
        attributes:
          type: object
          properties:
            id:
              type: integer
              example: 2
              nullable: false
            month:
              type: integer
              example: 7
              nullable: true
            offer_code:
              type: string
              nullable: true
              example: string
            safe_harbor_code:
              type: string
              nullable: true
              example: string
            ichra_zip:
              type: string
              nullable: true
              example: string
          required: []
      required:
      - id
      - type
    FederalEfileStatus:
      type: object
      properties:
        status:
          type: string
          description: 'One of: unscheduled, scheduled, sent, accepted, corrected_scheduled,
            corrected, corrected_accepted'
          example: accepted
        time:
          type: string
          format: date-time
          example: '2022-04-29T15:19:42.995-04:00'
      required: []
    StateEfileStatus:
      type: object
      properties:
        jurisdiction:
          type: string
          description: 'One of: AA, AE, AK, AL, AP, AR, AS, AZ, CA, CO, CT, DC, DE,
            FL, FM, GA, GU, HI, IA, ID, IL, IN, KS, KY, LA, MA, MD, ME, MH, MI, MN,
            MO, MP, MS, MT, NC, ND, NE, NH, NJ, NM, NV, NY, OH, OK, OR, PA, PR, PW,
            RI, SC, SD, TN, TX, UT, VA, VI, VT, WA, WI, WV, WY'
          example: OR
        status:
          type: string
          description: 'One of: unscheduled, scheduled, sent, rejected, accepted,
            corrected_scheduled, corrected_sent, corrected_rejected, corrected_accepted'
          example: accepted
        time:
          type: string
          format: date-time
          example: '2022-04-29T15:19:42.995-04:00'
      required: []
    PostalMailStatus:
      type: object
      properties:
        status:
          type: string
          description: 'One of: unscheduled, pending, sent, delivered'
          example: sent
        time:
          type: string
          format: date-time
          example: '2022-04-29T15:19:42.995-04:00'
      required: []
    TINMatchStatus:
      type: object
      properties:
        status:
          type: string
          description: 'One of: none, pending, matched, failed'
          example: matched
        time:
          type: string
          format: date-time
          example: '2022-04-29T15:19:42.995-04:00'
      required: []
    AddressVerificationStatus:
      type: object
      properties:
        status:
          type: string
          description: 'One of: unknown, pending, failed, incomplete, unchanged, verified'
          example: verified
        time:
          type: string
          format: date-time
          example: '2022-04-29T15:19:42.995-04:00'
      required: []
    EDeliveryStatus:
      type: object
      properties:
        status:
          type: string
          description: 'One of: unscheduled, scheduled, sent, bounced, refused, recipient_entry_error,
            e_delivery_accepted, second_delivery, missing_email, undeliverable'
          example: sent
        time:
          type: string
          format: date-time
          nullable: true
          example: '2022-04-29T15:19:42.995-04:00'
      required: []
    ValidationErrors:
      type: object
      properties:
        field:
          type: string
          description: Name of the field that has validation errors
          example: zip
        errors:
          type: array
          minItems: 1
          items:
            type: string
      required:
      - errors
    errors:
      type: object
      description: The entire body of an error response. It contains one or more error
        objects.
      properties:
        errors:
          type: array
          minItems: 1
          items:
            anyOf:
            - "$ref": "#/components/schemas/error_400"
            - "$ref": "#/components/schemas/error_404"
            - "$ref": "#/components/schemas/error_422"
            discriminator: 
      required:
      - errors
    error:
      type: object
      description: All error objects conform to this general structure.
      properties:
        status:
          type: string
          example: string
        title:
          type: string
          example: string
        source:
          type: object
          nullable: true
          properties: {}
          required: []
        detail:
          type: string
          nullable: true
          example: 
        code:
          type: string
          nullable: true
          example: 
      required:
      - status
      - title
    error_400:
      allOf:
      - "$ref": "#/components/schemas/error"
      - type: object
        properties:
          status:
            type: string
            example: '400'
          title:
            type: string
            example: Bad Request
        required: []
      description: An error object indicating a bad request.
    error_403:
      allOf:
      - "$ref": "#/components/schemas/error"
      - type: object
        properties:
          status:
            type: string
            example: '403'
          title:
            type: string
            example: Forbidden
        required: []
      description: An error object indicating that the requested object is not accessible
        to this API token's user.
    error_404:
      allOf:
      - "$ref": "#/components/schemas/error"
      - type: object
        properties:
          status:
            type: string
            example: '404'
          title:
            type: string
            example: Not Found
        required: []
      description: An error object indicating that the requested object was not found
        or not accessible.
    error_422:
      allOf:
      - "$ref": "#/components/schemas/error"
      - type: object
        properties:
          status:
            type: string
            example: '422'
          title:
            type: string
            example: Unprocessable Entity
          source:
            type: object
            nullable: true
            properties:
              pointer:
                type: string
                example: "/data/attributes/name"
            required: []
          detail:
            type: string
            example: Name can't be blank
          code:
            type: string
            example: blank
        required: []
      description: An error object indicating that the record you are trying to save
        is incomplete or invalid.
