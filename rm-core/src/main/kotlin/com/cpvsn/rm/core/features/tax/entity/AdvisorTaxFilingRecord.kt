package com.cpvsn.rm.core.features.tax.entity

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import java.math.BigDecimal
import java.time.Instant

class AdvisorTaxFilingRecord : RmEntity() {
    companion object : RmCompanion<AdvisorTaxFilingRecord>()


    @Column
    var advisor_id: Int = 0

    @Column
    var email_address: String = ""

    @Column
    var location: String = ""

    @Column
    var tax_year: Int = 0

    /**
     * Payment Sum
     */
    @Column
    var total_paid_amount: BigDecimal = BigDecimal.ZERO

    @Column
    var payment_snapshot_time: Instant = Instant.MIN

    @Column
    var is_cut_off: Boolean = false

    /**
     * Tax filing (W9 form, 1099 NEC form)
     */
    @Column
    var w9_id: Int? = null

    @Column
    var sign_w9_id: Int? = null

    @Column
    var w9_status: W9Status = W9Status.INIT

    @Column
    var form_1099_nec_status: Form1099NecStatus = Form1099NecStatus.INIT


    enum class W9Status(
        val description: String = ""
    ) {
        INIT,
        REQUESTED,
        SUBMITTED
    }


    enum class Form1099NecStatus(
        val description: String = ""
    ) {
        INIT(""),
        UPLOADED(""),
        SENT_TO_IRS("")
    }

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
    ) : BaseQuery<AdvisorTaxFilingRecord>()
}