package com.cpvsn.rm.core.features.tax.entity

import java.math.BigDecimal
import java.time.LocalDate

/**
 * Request data class for /api/v1/{team_api_id}/form_requests endpoint
 */
data class FormRequestCreateRequest(
    val tax_year: Int,
    val recipients: List<FormRequestRecipient>,
    val form_type: String = "1099-NEC"
)

/**
 * Recipient information for 1099 form
 */
data class FormRequestRecipient(
    val advisor_id: Int,
    val recipient_name: String,
    val recipient_email: String,
    val recipient_tin: String? = null,
    val recipient_address: FormRequestAddress,
    val payment_amounts: Map<String, BigDecimal>
)

/**
 * Address information
 */
data class FormRequestAddress(
    val address_line1: String,
    val address_line2: String? = null,
    val city: String,
    val state: String,
    val postal_code: String,
    val country: String = "US"
)

/**
 * Response data class for form request operations
 */
data class FormRequestResponse(
    val request_id: String,
    val status: String,
    val team_api_id: String,
    val tax_year: Int,
    val form_type: String,
    val recipient_count: Int,
    val created_at: String,
    val results: List<FormRequestResult>? = null,
    val errors: List<String>? = null
)

/**
 * Individual form processing result
 */
data class FormRequestResult(
    val advisor_id: Int,
    val form_id: String? = null,
    val status: String,
    val error_message: String? = null,
    val pdf_url: String? = null
)

class Avalara1099
