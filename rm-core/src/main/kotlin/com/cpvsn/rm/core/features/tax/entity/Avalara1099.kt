package com.cpvsn.rm.core.features.tax.entity

import com.fasterxml.jackson.annotation.JsonProperty
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDate

/**
 * Data classes for /api/v1/{team_api_id}/form_requests endpoint
 * Used for Avalara 1099 form request and response handling
 */

/**
 * Request data class for creating/submitting 1099 form requests
 */
@Schema(description = "Request for creating 1099 form requests")
data class FormRequestCreateRequest(
    @Schema(description = "Tax year for the 1099 form", example = "2024")
    val tax_year: Int,

    @Schema(description = "List of recipients for 1099 forms")
    val recipients: List<FormRequestRecipient>,

    @Schema(description = "Form type", example = "1099-NEC")
    val form_type: String = "1099-NEC",

    @Schema(description = "Due date for form submission")
    val due_date: LocalDate? = null,

    @Schema(description = "Additional metadata for the request")
    val metadata: Map<String, Any?>? = null
)

/**
 * Recipient information for 1099 form
 */
@Schema(description = "Recipient information for 1099 form")
data class FormRequestRecipient(
    @Schema(description = "Advisor ID", example = "12345")
    val advisor_id: Int,

    @Schema(description = "Recipient name")
    val recipient_name: String,

    @Schema(description = "Recipient email")
    val recipient_email: String,

    @Schema(description = "Recipient TIN (Tax Identification Number)")
    val recipient_tin: String? = null,

    @Schema(description = "Recipient address")
    val recipient_address: FormRequestAddress,

    @Schema(description = "Payment amounts by box number")
    val payment_amounts: Map<String, BigDecimal>,

    @Schema(description = "Additional recipient metadata")
    val metadata: Map<String, Any?>? = null
)

/**
 * Address information for recipients
 */
@Schema(description = "Address information")
data class FormRequestAddress(
    @Schema(description = "Street address line 1")
    val address_line1: String,

    @Schema(description = "Street address line 2")
    val address_line2: String? = null,

    @Schema(description = "City")
    val city: String,

    @Schema(description = "State or province")
    val state: String,

    @Schema(description = "Postal code")
    val postal_code: String,

    @Schema(description = "Country code", example = "US")
    val country: String = "US"
)

/**
 * Response data class for form request operations
 */
@Schema(description = "Response for form request operations")
data class FormRequestResponse(
    @Schema(description = "Request ID")
    val request_id: String,

    @Schema(description = "Status of the form request")
    val status: FormRequestStatus,

    @Schema(description = "Team API ID")
    val team_api_id: String,

    @Schema(description = "Tax year")
    val tax_year: Int,

    @Schema(description = "Form type")
    val form_type: String,

    @Schema(description = "Number of recipients")
    val recipient_count: Int,

    @Schema(description = "Creation timestamp")
    val created_at: String,

    @Schema(description = "Last update timestamp")
    val updated_at: String? = null,

    @Schema(description = "Due date for submission")
    val due_date: LocalDate? = null,

    @Schema(description = "List of form processing results")
    val results: List<FormRequestResult>? = null,

    @Schema(description = "Error messages if any")
    val errors: List<String>? = null,

    @Schema(description = "Additional metadata")
    val metadata: Map<String, Any?>? = null
)

/**
 * Individual form processing result
 */
@Schema(description = "Individual form processing result")
data class FormRequestResult(
    @Schema(description = "Advisor ID")
    val advisor_id: Int,

    @Schema(description = "Form ID generated by Avalara")
    val form_id: String? = null,

    @Schema(description = "Processing status for this recipient")
    val status: FormRequestStatus,

    @Schema(description = "Error message if processing failed")
    val error_message: String? = null,

    @Schema(description = "PDF URL if form was generated successfully")
    val pdf_url: String? = null,

    @Schema(description = "Processing timestamp")
    val processed_at: String? = null
)

/**
 * Status enumeration for form requests
 */
@Schema(description = "Status of form request processing")
enum class FormRequestStatus {
    @JsonProperty("pending")
    PENDING,

    @JsonProperty("processing")
    PROCESSING,

    @JsonProperty("completed")
    COMPLETED,

    @JsonProperty("failed")
    FAILED,

    @JsonProperty("cancelled")
    CANCELLED
}

/**
 * Query parameters for listing form requests
 */
@Schema(description = "Query parameters for listing form requests")
data class FormRequestQuery(
    @Schema(description = "Filter by tax year")
    val tax_year: Int? = null,

    @Schema(description = "Filter by status")
    val status: FormRequestStatus? = null,

    @Schema(description = "Filter by form type")
    val form_type: String? = null,

    @Schema(description = "Filter by advisor ID")
    val advisor_id: Int? = null,

    @Schema(description = "Filter by creation date from")
    val created_from: LocalDate? = null,

    @Schema(description = "Filter by creation date to")
    val created_to: LocalDate? = null
)

/**
 * Update request for existing form requests
 */
@Schema(description = "Request for updating form request status or metadata")
data class FormRequestUpdateRequest(
    @Schema(description = "New status for the form request")
    val status: FormRequestStatus? = null,

    @Schema(description = "Updated metadata")
    val metadata: Map<String, Any?>? = null,

    @Schema(description = "Updated due date")
    val due_date: LocalDate? = null
)

/**
 * Batch form request for multiple recipients
 */
@Schema(description = "Batch request for processing multiple form requests")
data class FormRequestBatchRequest(
    @Schema(description = "List of individual form requests")
    val requests: List<FormRequestCreateRequest>,

    @Schema(description = "Batch processing options")
    val batch_options: FormRequestBatchOptions? = null
)

/**
 * Batch processing options
 */
@Schema(description = "Options for batch processing")
data class FormRequestBatchOptions(
    @Schema(description = "Whether to continue processing if individual requests fail")
    val continue_on_error: Boolean = true,

    @Schema(description = "Maximum number of concurrent processing threads")
    val max_concurrency: Int = 5,

    @Schema(description = "Batch processing priority")
    val priority: BatchPriority = BatchPriority.NORMAL
)

/**
 * Batch processing priority
 */
@Schema(description = "Priority level for batch processing")
enum class BatchPriority {
    @JsonProperty("low")
    LOW,

    @JsonProperty("normal")
    NORMAL,

    @JsonProperty("high")
    HIGH,

    @JsonProperty("urgent")
    URGENT
}

/**
 * Batch response for multiple form requests
 */
@Schema(description = "Response for batch form request operations")
data class FormRequestBatchResponse(
    @Schema(description = "Batch ID")
    val batch_id: String,

    @Schema(description = "Overall batch status")
    val batch_status: FormRequestStatus,

    @Schema(description = "Total number of requests in batch")
    val total_requests: Int,

    @Schema(description = "Number of successfully processed requests")
    val successful_requests: Int,

    @Schema(description = "Number of failed requests")
    val failed_requests: Int,

    @Schema(description = "Individual request responses")
    val responses: List<FormRequestResponse>,

    @Schema(description = "Batch creation timestamp")
    val created_at: String,

    @Schema(description = "Batch completion timestamp")
    val completed_at: String? = null
)

class Avalara1099 {
    // This class can contain utility methods or constants related to Avalara 1099 processing
}
