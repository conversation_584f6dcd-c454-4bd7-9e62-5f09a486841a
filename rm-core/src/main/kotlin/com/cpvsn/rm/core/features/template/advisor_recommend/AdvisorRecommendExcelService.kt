package com.cpvsn.rm.core.features.template.advisor_recommend

import com.cpvsn.rm.core.features.client.ClientFileTemplate
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.template.FileTemplate
import com.cpvsn.rm.core.features.template.advisor_recommend.custom.GenerationImExcelService
import com.cpvsn.rm.core.features.template.advisor_recommend.custom.MckExcelService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class AdvisorRecommendExcelService {

    @Autowired
    private lateinit var generationImExcelService: GenerationImExcelService

    @Autowired
    private lateinit var mckExcelService: MckExcelService

    fun get_model(
            tasks: List<Task>,
            template: FileTemplate,
            file_name: String? = null,
            client_file_template: ClientFileTemplate? = null,
            user_id: Int,
    ): AdvisorRecommendExcelModel {
        val custom_client_name = client_file_template?.client_name_enum
                ?: return CommonTaskExcelModel.create(tasks, file_name)
        return when (custom_client_name) {
            ClientFileTemplate.ClientNameEnum.MCK_US -> mckExcelService.get_model(tasks, file_name, user_id)
            ClientFileTemplate.ClientNameEnum.GENERATION_IM -> generationImExcelService.get_model(tasks, file_name, user_id)
        }
    }

}
