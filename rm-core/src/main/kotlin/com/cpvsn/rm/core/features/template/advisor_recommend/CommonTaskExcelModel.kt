package com.cpvsn.rm.core.features.template.advisor_recommend

import com.cpvsn.core.base.model.BaseModelCompanion
import com.cpvsn.core.base.model.RequireExtra
import com.cpvsn.poiext.excel.annotation.ExcelColumn
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task
import java.time.ZoneId
import java.time.format.DateTimeFormatter

data class CommonTaskExcelModel(
    val file_name: String?,
    val items: List<Item>,
) : AdvisorRecommendExcelModel {
    companion object : BaseModelCompanion<CommonTaskExcelModel>() {
        fun create(
            tasks: List<Task>,
            file_name: String? = null
        ): CommonTaskExcelModel {
            val name = file_name ?: get_default_file_name(tasks)
            Task.join(tasks, Item.require_extra)
            return CommonTaskExcelModel(
                file_name = name,
                items = tasks.map { Item.fromTask(it) }
            )
        }

        private fun get_default_file_name(
            tasks: List<Task>,
        ): String {
            val projects = Project.findAll(
                Project.Query(
                    ids = tasks.map { it.project_id }.toSet()
                )
            )
            return if (projects.isEmpty()) {
                "tasks"
            } else {
                projects.take(3).joinToString(",") { it.name.orEmpty() }
            }
        }

        val `MMMM, yyyy` = DateTimeFormatter
            .ofPattern("MMMM, yyyy")
            .withZone(ZoneId.systemDefault())
    }

    data class Item(
        @ExcelColumn(index = 10)
        val id: Int,

        @ExcelColumn(index = 20)
        @RequireExtra(["advisor"])
        val first_name: String?,
        @ExcelColumn(index = 21)
        @RequireExtra(["advisor"])
        val last_name: String?,

        @ExcelColumn(index = 30)
        @RequireExtra(["advisor_profile.company"])
        val current_company: String?,
        @ExcelColumn(index = 31)
        @RequireExtra(["advisor_profile"])
        val profile_start_date: String?,
        @ExcelColumn(index = 32)
        @RequireExtra(["advisor_profile"])
        val profile_end_date: String?,

        @ExcelColumn(index = 35)
        val cleared: String = "",
        @ExcelColumn(index = 36)
        val conflicted: String = "",

        @ExcelColumn(index = 40)
        @RequireExtra(["advisor_profile"])
        val title: String?,
        @ExcelColumn(index = 50, maxWidth = 50)
        @RequireExtra(["advisor"])
        val background: String?,

        @ExcelColumn(index = 53)
        @RequireExtra(["advisor.mobile_jit"])
        val mobile: String,
        @ExcelColumn(index = 54)
        val email: String,
        @ExcelColumn(index = 55)
        @RequireExtra(["advisor.linkedin_url_jit"])
        val linkedin_url: String,
        @ExcelColumn(index = 56)
        @RequireExtra(["advisor"])
        val linkedin_premium: String,

        @ExcelColumn(index = 60, maxWidth = 50)
        @RequireExtra(["latest_submitted_sq.plain_text"])
        val screening_question: String?,
        @ExcelColumn(index = 70, width = 35)
        @RequireExtra(["advisor_schedules"])
        val availability: String?,
        @ExcelColumn(index = 80)
        val advisor_id: Int?,
        @RequireExtra(["latest_or_create_survey_portal.link_jit"])
        val latest_survey_link: String?,
        @RequireExtra(["latest_or_create_unsubscribe_outreach_portal.link_jit"])
        val latest_unsubscribe_link: String?,
        @RequireExtra(["latest_or_create_decline_portal.link_jit"])
        val latest_decline_link: String?,
    ) {
        companion object : BaseModelCompanion<Item>() {
            fun fromTask(task: Task): Item {
                return Item(
                    id = task.id,
                    first_name = task.advisor?.firstname,
                    last_name = task.advisor?.lastname,
                    current_company = task.advisor_profile?.company?.name,
                    profile_start_date = task.advisor_profile
                        ?.local_start_date
                        ?.format(`MMMM, yyyy`),
                    profile_end_date = task.advisor_profile?.let { profile ->
                        if (profile.is_current) {
                            "Current"
                        } else {
                            profile.local_end_date?.format(`MMMM, yyyy`)
                        }
                    }.orEmpty(),
                    title = task.advisor_profile?.position,
                    background = task.advisor?.background
                        ?: task.advisor?.auto_background,

                    mobile = task.advisor?.mobile_jit.orEmpty(),
                    email = task.advisor?.email.orEmpty(),
                    linkedin_url = task.advisor?.linkedin_url_jit.orEmpty(),
                    linkedin_premium = task.advisor?.is_linkedin_premium?.toString().orEmpty(),
                    screening_question = task.latest_submitted_sq?.plain_text,
                    availability = task.advisor_schedules.orEmpty()
                        .joinToString("\n") { it.to_range_string() },
                    advisor_id = task.advisor_id,
                    latest_survey_link = task.latest_or_create_survey_portal?.link_jit,
                    latest_unsubscribe_link = task.latest_or_create_unsubscribe_outreach_portal?.link_jit,
                    latest_decline_link = task.latest_or_create_decline_portal?.link_jit,
                )
            }
        }
    }
}
