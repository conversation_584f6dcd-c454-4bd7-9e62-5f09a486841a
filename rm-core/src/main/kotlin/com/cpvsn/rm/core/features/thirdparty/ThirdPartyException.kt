package com.cpvsn.rm.core.features.thirdparty

import com.cpvsn.core.model.BusinessException
import com.cpvsn.core.model.ResponseStatus

class ThirdPartyException(
    val vendor: ThirdPartyVendor? = null,
    message: String? = null,
    cause: Throwable? = null,
) : BusinessException(
    status = ResponseStatus.BUSINESS_EXCEPTION,
    message = message
        ?: vendor?.let { "Call ${it.displayName} API failed! (cause = '${cause?.message}')" },
    cause = cause,
)
