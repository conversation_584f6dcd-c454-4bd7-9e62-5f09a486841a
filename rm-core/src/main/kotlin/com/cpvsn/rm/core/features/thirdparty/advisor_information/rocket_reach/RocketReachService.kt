package com.cpvsn.rm.core.features.thirdparty.advisor_information.rocket_reach

import com.cpvsn.rm.core.features.thirdparty.advisor_information.rocket_reach.config.RocketReachApiClient
import com.cpvsn.rm.core.util.biz_error
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class RocketReachService {

    @Autowired
    private lateinit var rocketReachApiClient: RocketReachApiClient

    fun search_people(linkedin_profile_url: String): SearchPeople.Response {
        return rocketReachApiClient.search_people(
            request = SearchPeople.Request(
                query = SearchPeople.Query(
                    link = listOf(linkedin_profile_url),
                )
            )
        ) ?: biz_error("empty response")
    }

    fun lookup_person(linkedin_profile_url: String): LookupPerson.Response {
        return rocketReachApiClient.lookup_person(
            request = LookupPerson.Request(
                linkedin_url = linkedin_profile_url,
            )
        ) ?: biz_error("empty response")
    }

}