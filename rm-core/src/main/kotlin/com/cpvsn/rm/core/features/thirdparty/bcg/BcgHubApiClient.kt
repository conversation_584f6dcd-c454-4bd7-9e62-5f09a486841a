package com.cpvsn.rm.core.features.thirdparty.bcg

import com.cpvsn.core.svc.rpc.http.unirest.UnirestUtil.configureLogger
import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.rm.core.features.thirdparty.bcg.config.BcgProperties
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.BcgHub
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.enums.BcgExpertHubModule
import com.cpvsn.rm.core.features.thirdparty.bcg.token.BcgTokenInterceptor
import com.cpvsn.rm.core.util.biz_error
import kong.unirest.HttpRequest
import kong.unirest.Unirest
import org.slf4j.LoggerFactory
import kotlin.reflect.KClass


/**
 * Vendor/Network refer to us Capvision
 * Requestor refer to BCG side
 *
 *
 *
 * EEH的设计是：
 *  - 当vendor有信息要发给EEH， 则调对应的post接口；
 *  - 而当EEH有信息要发给vendor, 它们会把信息放到SQS中，
 *      需要vendor调对应的get接口来poll信息，并且一般需要在处理完成后进行ack。
 *      另外也存在EEH直接调用vendor api来发送信息的情况（call management process中）
 *
 * The basic design of EEH is as follows:
 *  - when the vendor has information to send to EEH, they call the corresponding POST API.
 *  - When EEH has information to send to the vendor, they place the information in an SQS queue,
 *      and the vendor needs to call the corresponding GET API to poll the information.
 *      Typically, after processing the information, an acknowledgment is required.
 *      Additionally, there are cases where EEH directly calls the vendor’s API to send information (in the call management process).
 */

/**
 * @see BcgExpertHubModule.kt
 *
 * To check the logs:
 * kubectl logs -n dev -l app=rm-web-preview --tail=100 | grep 'BcgHubApiClient'
 * kubectl logs -n dev -l app=rm-web-preview --tail=100 | grep 'EXPERT_PROFILE'
 */
class BcgHubApiClient(
    private val properties: BcgProperties,
) {
    //region @
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val rs = "/routerservice"
    private val unirest = Unirest.spawnInstance()
        .apply {
            config()
                .defaultBaseUrl(properties.baseUrl)
                .addDefaultHeader("Content-Type", "application/json")
                .addDefaultHeader("x-api-key", properties.apiKey)
                .interceptor(BcgTokenInterceptor)
            configureLogger(logger)
        }

    private enum class Type {
        POLL_EEH,
        EMPTY_POLL_EEH,
        SEND_TO_EEH,
        ACK_EEH,
        EEH_RESPONSE
    }

    private fun <T : Any> HttpRequest<*>.extToResponse(clazz: KClass<T>): T? {
        val response = this.asString()
        val contentAsString = response.body
        logger.info("[EEH_Response_Content]${contentAsString}")
        if (contentAsString == "{}" || contentAsString.isBlank()) {
            return null//when queue is empty, bcg will return an empty object
        }
        return if (response.status != 200) {
            // here we don't parse contentAsString to ServiceException since the real exception returned is not this Exception
            biz_error(
                contentAsString
            )
        } else {
            CoreJsonUtil.parse(contentAsString, clazz)
        }
    }
    //endregion

    fun getApiDocs(): String? {
        val res = unirest
            .get("/routerservice/v3/api-docs")
            .asString().body
        return res
    }

    private fun eehlog(type: Type, module: BcgExpertHubModule, obj: Any?, path: String? = null) {
        var ty = type
        var content = ""
        when {
            obj == null -> {
                if (ty == Type.POLL_EEH) {
                    ty = Type.EMPTY_POLL_EEH
                }
                content = "empty"
            }

            (obj is List<*>) && obj.isEmpty() -> {
                if (ty == Type.POLL_EEH) {
                    ty = Type.EMPTY_POLL_EEH
                }
                content = "empty array"
            }

            else -> {
                content = CoreJsonUtil.stringify(obj)
            }
        }
        if (path.isNullOrBlank()) {
            logger.info("[$module][$ty]$content")
        } else {
            logger.info("[$module][$ty][$path]$content")
        }
    }

    //region expert interview request
    /**
     * Get a list of Expert Interview Requests which are new and needs to be polled
     */
    fun pollExpertInterviewRequests(): List<BcgHub.Project.ExpertInterviewRequest> {
        val res = unirest
            .get("$rs/expert-interview-requests")
            .extToResponse(Array<BcgHub.Project.ExpertInterviewRequest>::class)?.toList() ?: biz_error("empty request")
        eehlog(Type.POLL_EEH, BcgExpertHubModule.INTERVIEW_REQUEST, res)
        return res
    }

    /**
     * Receive Project Id from Vendors
     */
    fun ackInterviewRequest(request: BcgHub.Project.ExpertInterviewProjectIdRequest): BcgHub.Project.ExpertInterviewProjectIdResponse? {
        eehlog(Type.ACK_EEH, BcgExpertHubModule.INTERVIEW_REQUEST, request)
        val res = unirest
            .post("$rs/expert-interview-requests/projectId")
            .body(request)
            .extToResponse(BcgHub.Project.ExpertInterviewProjectIdResponse::class)
        eehlog(Type.EEH_RESPONSE, BcgExpertHubModule.INTERVIEW_REQUEST, res)
        return res
    }
    //endregion

    //region expert survey request
    /**
     * Get a list of survey requests
     */
    fun pollExpertSurveyRequests(): List<BcgHub.Project.ExpertSurveyRequest> {
        val res = unirest
            .get("$rs/expert-survey-requests")
            .extToResponse(Array<BcgHub.Project.ExpertSurveyRequest>::class)?.toList() ?: biz_error("empty request")
        eehlog(Type.POLL_EEH, BcgExpertHubModule.SURVEY_REQUEST, res)
        return res
    }
    //endregion

    //region communication template
    /**
     * Receive new template from Vendors
     */
    fun sendTemplate(request: BcgHub.Template.TemplateRequest): BcgHub.Template.TemplateResponse? {
        eehlog(Type.SEND_TO_EEH, BcgExpertHubModule.EMAIL_TEMPLATE, request, path = "sendTemplate")
        val res = unirest
            .post("$rs/templates")
            .body(request)
            .extToResponse(BcgHub.Template.TemplateResponse::class)
        eehlog(Type.EEH_RESPONSE, BcgExpertHubModule.EMAIL_TEMPLATE, res, path = "sendTemplate")
        return res
    }

    /**
     * Get approved Templates for Expert Interview Requests
     */
    fun getApprovedTemplate(): BcgHub.Template.ApprovedTemplateResponse? {
        val res = unirest
            .get("$rs/templates/approved")
            .extToResponse(BcgHub.Template.ApprovedTemplateResponse::class)
        eehlog(Type.POLL_EEH, BcgExpertHubModule.EMAIL_TEMPLATE, res)
        return res
    }

    /**
     * Receive Acknowledgement from Vendors for Approved Templates
     */
    fun ackApprovedTemplate(request: BcgHub.Template.AcknowledgementRequest): BcgHub.Template.AcknowledgementResponse? {
        eehlog(Type.ACK_EEH, BcgExpertHubModule.EMAIL_TEMPLATE, request, path = "ackTemplate")
        val res = unirest
            .post("$rs/templates/acknowledgement")
            .body(request)
            .extToResponse(BcgHub.Template.AcknowledgementResponse::class)
        eehlog(Type.SEND_TO_EEH, BcgExpertHubModule.EMAIL_TEMPLATE, res, path = "ackTemplate")
        return res
    }
    //endregion

    //region expert profile
    /**
     * Receive new expert profiles from Vendors
     */
    fun send_expert_profiles(request: BcgHub.Profile.ExpertProfileRequest): BcgHub.Profile.ExpertProfileResponse? {
        eehlog(Type.SEND_TO_EEH, BcgExpertHubModule.EXPERT_PROFILE, request, path = "send_expert_profiles")
        val res = unirest
            .post("$rs/expert-profile")
            .body(request)
            .extToResponse(BcgHub.Profile.ExpertProfileResponse::class)
        eehlog(Type.EEH_RESPONSE, BcgExpertHubModule.EXPERT_PROFILE, res, path = "send_expert_profiles")
        return res
    }

    /**
     * Get status regarding expert profile whether it has been declined
     */
    fun poll_expert_profile_status(): List<BcgHub.Profile.ExpertProfilesStatusUpdate> {
        val res = unirest
            .get("$rs/expert-profile/status")
            .extToResponse(Array<BcgHub.Profile.ExpertProfilesStatusUpdate>::class)?.toList()
            ?: biz_error("empty request")
        eehlog(Type.POLL_EEH, BcgExpertHubModule.EXPERT_PROFILE, res)
        return res
    }

    /**
     * Receive Acknowledgement from Vendors for Declined Expert Profiles
     */
    fun ack_expert_profile_decline(request: BcgHub.Profile.AckExpertProfile): BcgHub.Profile.AckExpertProfileResponse? {
        eehlog(Type.ACK_EEH, BcgExpertHubModule.EXPERT_PROFILE, request)
        val res = unirest
            .post("$rs/expert-profile/acknowledgement")
            .body(request)
            .extToResponse(BcgHub.Profile.AckExpertProfileResponse::class)
        eehlog(Type.EEH_RESPONSE, BcgExpertHubModule.EXPERT_PROFILE, res)
        return res
    }

    /**
     * Receive expert profiles for non-considerable or deletion from Vendors
     */
    fun update_availablity(request: BcgHub.Profile.ExpertProfilesStatusUpdate): BcgHub.Profile.ExpertProfileResponse? {
        eehlog(Type.SEND_TO_EEH, BcgExpertHubModule.EXPERT_PROFILE, request, path = "update_availablity")
        val res = unirest
            .put("$rs/expert-profile/update-availability")
            .body(listOf(request))
            .extToResponse(BcgHub.Profile.ExpertProfileResponse::class)
        eehlog(Type.EEH_RESPONSE, BcgExpertHubModule.EXPERT_PROFILE, res, path = "update_availablity")
        return res
    }
    //endregion

    //region calls management
    /**
     * Receiving calls related information from Networks and details varies as per call status.
     */
    fun send_call_management_info(request: BcgHub.Call.CallDetailsNetworkRequest): BcgHub.Call.CallDetailsResponse? {
        eehlog(Type.SEND_TO_EEH, BcgExpertHubModule.CALL_MANAGEMENT, request, path = "send_call_management_info")
        val res = unirest
            .post("$rs/call-management")
            .body(request)
            .extToResponse(BcgHub.Call.CallDetailsResponse::class)
        eehlog(Type.EEH_RESPONSE, BcgExpertHubModule.CALL_MANAGEMENT, res, path = "send_call_management_info")
        return res
    }

    /**
     * List of call details shared by requestor for Networks of different experts
     */
    fun get_external_call_management_info(): List<BcgHub.Call.CallDetails> {
        val res = unirest
            .get("$rs/call-management")
            .extToResponse(Array<BcgHub.Call.CallDetails>::class)?.toList() ?: biz_error("empty request")
        eehlog(Type.POLL_EEH, BcgExpertHubModule.CALL_MANAGEMENT, res)
        return res
    }

    /**
     * Receive Acknowledgement from Networks for shared Call Details
     */
    fun acknowledge_get_external_call_management_info(request: BcgHub.Call.AcknowledgementCallDetails): BcgHub.Call.AcknowledgementCallDetailsResponse? {
        eehlog(Type.ACK_EEH, BcgExpertHubModule.CALL_MANAGEMENT, request)
        val res = unirest
            .post("$rs/call-management/acknowledgement")
            .body(request)
            .extToResponse(BcgHub.Call.AcknowledgementCallDetailsResponse::class)
        eehlog(Type.EEH_RESPONSE, BcgExpertHubModule.CALL_MANAGEMENT, res)
        return res
    }

    //endregion

    fun send_research_manager_info(request: BcgHub.ResearchManager.ContactDetailsRequest): BcgHub.ResearchManager.ContactDetailsResponse? {
        val res = unirest
            .put("$rs/research-managers/${request.request_id}/contacts")
            .body(request)
            .extToResponse(BcgHub.ResearchManager.ContactDetailsResponse::class)
        eehlog(Type.SEND_TO_EEH, BcgExpertHubModule.RESEARCH_MANAGER, res)
        return res
    }


}
