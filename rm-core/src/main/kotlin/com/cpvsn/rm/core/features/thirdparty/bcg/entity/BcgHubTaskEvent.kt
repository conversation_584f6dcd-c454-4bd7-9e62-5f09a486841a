package com.cpvsn.rm.core.features.thirdparty.bcg.entity

import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.entity.SoftDeletable
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.enums.BcgCallStatus
import java.time.Instant

class BcgHubTaskEvent : RmEntity(), SoftDeletable {

    companion object : RmCompanion<BcgHubTaskEvent>()

    @Column
    var bcg_hub_task_id: Int = 0

    @Column
    var request_id: Long = 0

    @Column
    var call_status: BcgCallStatus? = null

    @Column
    var source: Source = Source.LOCAL

    @Column
    var message: String? = null

    @Column
    var type: Type? = null

    enum class Type {
        EXPERT_PROFILE_SENT,
        EXPERT_PROFILE_APPROVED,
        EXPERT_PROFILE_DECLINED,

        EXPERT_SLOTS_SENT,
        EXPERT_SLOTS_SELECTED,


        BCG_CANCELLED_CALL,
        BCG_DISPUTED_CALL,
        BCG_SCHEDULED_CALL,
        BCG_RESCHEDULE_CALL,
        BCG_REQUEST_FOLLOW_UP,
    }


    enum class Source {
        BCG,
        LOCAL,
    }

    override var delete_at: Instant? = null

}