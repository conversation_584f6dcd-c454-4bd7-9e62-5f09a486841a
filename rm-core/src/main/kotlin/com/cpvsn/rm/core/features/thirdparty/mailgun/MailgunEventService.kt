package com.cpvsn.rm.core.features.thirdparty.mailgun

import com.cpvsn.core.util.LoggerUtil.debug
import com.cpvsn.core.util.extension.biz_error
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.config.oversea.OverSeaEnvService
import com.cpvsn.rm.core.features.email.EmailRecord
import com.cpvsn.rm.core.features.email.EmailRecordTracking
import com.cpvsn.rm.core.features.email.tracking.EmailWebhookEventService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate
import javax.crypto.Mac
import javax.crypto.spec.SecretKeySpec
import kotlin.text.Charsets.UTF_8


@Service
class MailgunEventService {

    @Autowired
    private lateinit var overSeaEnvService: OverSeaEnvService

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var emailWebhookEventService: EmailWebhookEventService

    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val WEBHOOK_SIGNING_KEY = "b8a5d9edab9060e9a66da4e2c04da081"
        const val ALGORITHM = "HmacSHA256"
    }

    fun env(): String {
        return "${overSeaEnvService.region()}-${overSeaEnvService.current()}"
    }


    fun handle(event: MailgunEvent) {
        transactionTemplate.execute {
            if (event.env == env()) {
                val email_record = EmailRecord.get(event.email_record_id ?: biz_error("email record id not passed"))
                val recipient_email = event.recipient ?: biz_error("recipient mail is null")
                val email_record_tracking = EmailRecordTracking.firstOrNull(
                    EmailRecordTracking.Query(
                        email_record_id = email_record.id,
                        email = recipient_email
                    )
                ) ?: EmailRecordTracking {
                    this.email_record_id = email_record.id
                    this.email = recipient_email
                }
                if (email_record_tracking.is_new) {
                    val communicationRecord = CommunicationRecord.firstOrNull(
                        CommunicationRecord.Query(
                            email_record_id = email_record.id
                        )
                    )
                    email_record_tracking.task_id = communicationRecord?.task_id
                    when (communicationRecord?.email_content_type) {
                        EmailContentType.PROJECT_OUTREACH -> {
                            communicationRecord.advisor_id?.let {
                                email_record_tracking.advisor_id =
                                    communicationRecord.advisor_id
                            }
                        }

                        EmailContentType.CLIENT_OUTREACH -> {
                            communicationRecord.client_contact_id?.let {
                                email_record_tracking.client_contact_id =
                                    communicationRecord.client_contact_id
                            }
                        }

                        else -> {}
                    }
                    event.affect(email_record_tracking)
                    EmailRecordTracking.save(email_record_tracking)

                } else {
                    val patch = Patch.fromMutator(email_record_tracking) {
                        event.affect(email_record_tracking)
                    }
                    logger.debug {
                        patch.toDescription()
                    }
                    patch.patch()
                }
                MailgunEvent.save(event)
                emailWebhookEventService.evaluate_email_contact_healty_score(
                    email = event.recipient ?: biz_error("recipient not found"),
                    target_status = event.event?.to_status()
                )
            }
        }
    }

    fun verify_message_source(signature: MailgunWebhookReq.Signature) {
        val verified_message = signature.timestamp + signature.token
        val hmac = Mac.getInstance(ALGORITHM).apply {
            init(SecretKeySpec(WEBHOOK_SIGNING_KEY.toByteArray(UTF_8), ALGORITHM))
        }
        val calculatedSignature = hmac.doFinal(verified_message.toByteArray(UTF_8)).toHexString()
        if (signature.signature != calculatedSignature) {
            biz_error("signature is not valid")
        }
    }

    private fun ByteArray.toHexString(): String {
        return joinToString("") { "%02x".format(it) }
    }

}