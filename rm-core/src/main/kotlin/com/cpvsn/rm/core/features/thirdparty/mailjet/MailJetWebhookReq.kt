package com.cpvsn.rm.core.features.thirdparty.mailjet

import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.rm.core.util.biz_error

data class MailJetWebhookReq(
    val event: MailjetEvent.Type? = null,
    val time: String? = null,
    val MessageId: String? = null,
    val Message_GUID: String? = null,
    val email: String? = null,
    val mj_campaign_id: String? = null,
    val mj_contact_id: String? = null,
    val customcampaign: String? = null,
    val mj_message_id: String? = null,
    val smtp_reply: String? = null,
    val CustomID: String? = null,
    val Payload: String? = null,
)

data class PayloadObj(
    val email_record_id: Int,
    val env: String,
    val tag: String,
)

fun MailJetWebhookReq.toDbEvent(): MailjetEvent {
    val request = this
    val payload = CoreJsonUtil.parse(request.Payload ?: biz_error("payload not found"), PayloadObj::class)
    return MailjetEvent().apply {
        this.event = request.event
        this.time = request.time
        this.message_id = request.MessageId
        this.message_guid = request.Message_GUID
        this.email = request.email
        this.mj_companion_id = request.mj_campaign_id
        this.mj_contact_id = request.mj_contact_id
        this.customcampaign = request.customcampaign
        this.mj_message_id = request.mj_message_id
        this.smtp_reply = request.smtp_reply
        this.custom_id = request.CustomID
        this.email_record_id = payload.email_record_id
        this.env = payload.env
        this.tag = payload.tag
    }
}