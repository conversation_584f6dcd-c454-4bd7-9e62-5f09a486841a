package com.cpvsn.rm.core.features.thirdparty.mailjet

object MailjetConstant {
    /**
     * if the key name in com.cpvsn.core.svc.email.EmailRequest#getExtra_params
     * start with this prefix, then in mq_handler module,
     * if we use Postmark service to handle this request,
     * we will pass such parameters to the metadata parameters.
     */
    private const val CUSTOM_ARGS_PREFIX = "MAILJET_CUSTOM_ARGS_"

    enum class CustomArgs {
        email_record_id,
        env,
        ;

        val key: String
            get() = "${CUSTOM_ARGS_PREFIX}$name"
    }
}