package com.cpvsn.rm.core.features.thirdparty.resend

import com.cpvsn.core.svc.spring.ext.extExecute
import com.cpvsn.core.util.LoggerUtil.debug
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.config.oversea.OverSeaEnvService
import com.cpvsn.rm.core.features.email.EmailRecord
import com.cpvsn.rm.core.features.email.EmailRecordTracking
import com.cpvsn.rm.core.features.email.tracking.EmailWebhookEventService
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import com.cpvsn.rm.core.util.biz_error
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service
import org.springframework.transaction.support.TransactionTemplate

@Service
class ResendEventService {

    @Autowired
    private lateinit var transactionTemplate: TransactionTemplate

    @Autowired
    private lateinit var overSeaEnvService: OverSeaEnvService

    @Autowired
    private lateinit var emailWebhookEventService: EmailWebhookEventService

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun env(): String {
        return "${overSeaEnvService.region()}-${overSeaEnvService.current()}"
    }

    fun handle(event: ResendEvent) {
        transactionTemplate.extExecute {
            val resend_email_id = event.data?.email_id ?: biz_error("resend email id not passed")
            // the persisted email id in db use 'RESEND-' to identify the provider, we need to add the prefix here for querying
            val persisited_resend_email_id = "RESEND-${resend_email_id}"
            val cur_record_id = EmailRecord.firstOrNull(
                query = EmailRecord.Query(
                    vendor_email_id = persisited_resend_email_id
                )
            )?.id ?: biz_error("email_record_not_found")
            event.email_record_id = cur_record_id
            // find one or create
            val email_record_tracking = EmailRecordTracking
                .firstOrNull(
                    EmailRecordTracking.Query(
                        email_record_id = cur_record_id,
                        email = event.email,
                    )
                ) ?: EmailRecordTracking {
                this.email_record_id = cur_record_id
                this.email = event.email
            }
            if (email_record_tracking.is_new) {
                val communicationRecord = CommunicationRecord.firstOrNull(
                    CommunicationRecord.Query(
                        email_record_id = cur_record_id,
                    )
                )
                email_record_tracking.task_id = communicationRecord?.task_id
                when (communicationRecord?.email_content_type) {
                    EmailContentType.PROJECT_OUTREACH -> {
                        communicationRecord.advisor_id?.let {
                            email_record_tracking.advisor_id =
                                communicationRecord.advisor_id
                        }
                    }

                    EmailContentType.CLIENT_OUTREACH -> {
                        communicationRecord.client_contact_id?.let {
                            email_record_tracking.client_contact_id =
                                communicationRecord.client_contact_id
                        }
                    }

                    else -> {}
                }
                event.affect(email_record_tracking)
                EmailRecordTracking.save(email_record_tracking)
            } else {
                val patch = Patch.fromMutator(email_record_tracking) {
                    event.affect(email_record_tracking)
                }
                logger.debug {
                    patch.toDescription()
                }
                patch.patch()
            }
        }
        ResendEvent.save(event)

        emailWebhookEventService.evaluate_email_contact_healty_score(
            email = event.email,
            target_status = event.type_enum?.to_status(),
        )
    }

}