package com.cpvsn.rm.core.features.thirdparty.trolley.config

import com.trolley.Configuration
import com.trolley.Gateway
import org.springframework.context.annotation.Bean
import org.springframework.stereotype.Component

@Component
class TrolleyConfiguration(
    private val trolleyProps: TrolleyProps
) {
    @Bean
    fun trolleyGateway(): Gateway {
        val config = Configuration(trolleyProps.accessKey, trolleyProps.secretKey)
        val gateway = Gateway(config)
        return gateway
    }
}