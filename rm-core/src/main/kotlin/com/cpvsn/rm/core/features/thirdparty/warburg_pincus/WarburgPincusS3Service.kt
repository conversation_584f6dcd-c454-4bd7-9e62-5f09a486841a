package com.cpvsn.rm.core.features.thirdparty.warburg_pincus

import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.rm.core.config.aws_s3.S3SdkProperties
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.email_template.constant.EmailContentType
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstanceTextFormUtil
import com.cpvsn.rm.core.features.misc.communication.CommunicationRecord
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomAsset
import com.cpvsn.rm.core.features.zoom.meeting.pojo.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import java.io.ByteArrayInputStream
import java.net.HttpURLConnection
import java.net.URL
import java.nio.charset.StandardCharsets
import java.time.Duration
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

@Service
class WarburgPincusS3Service {

    // region @
    private val logger = LoggerFactory.getLogger(this::class.java)

    @Autowired(required = false)
    @Qualifier("warburgPincusS3Client")
    private var warburgPincusS3Client: S3Client? = null

    @Autowired
    private lateinit var s3_properties: S3SdkProperties
    // endregion

    companion object {
        const val WARBURG_PINCUS_FOLDER = "warburg_pincus"
        const val BCG_FOLDER = "bcg"
    }

    fun generate_warburg_pincus_call_json(
        asset: ZoomAsset,
        task: Task?,
        audio_file_name: String
    ): WarburgPincusMeetingData {
        val duration = try {
            val start = Instant.parse(asset.recording_start)
            val end = Instant.parse(asset.recording_end)
            Duration.between(start, end).seconds.toInt()
        } catch (e: Exception) {
            0
        }

        val participants = task?.let { cur_task ->
            val client_emails = CommunicationRecord.firstOrNull(
                CommunicationRecord.Query(
                    task_id = cur_task.id,
                    email_content_type = EmailContentType.CALENDAR_TASK_ARRANGE_CONTACT
                ),
                Sort.by(Sort.Order(direction = Sort.Direction.DESC, name = CommunicationRecord::id.name)),
                Includes.setOf(CommunicationRecord::email_record)
            )?.email_record?.to?.split(",").orEmpty().filter { !it.contains("capvision") }
            client_emails.map {
                Participant(
                    email = it,
                    name = it.substringBefore("@"),
                    participant_type = ParticipantType.CLIENT,
                )
            }
        }.orEmpty()

        val advisor = task?.advisor_id?.let {
            Advisor.find(it, Includes.setOf(Advisor::current_jobs dot AdvisorJob::company, Advisor::location))
        }
        val screener_questions = task?.let { cur_task ->
            Task.join(cur_task, Includes.setOf(Task::latest_submitted_sq))
            cur_task.latest_submitted_sq?.let { inquiryInstance ->
                inquiryInstance.qa_list_snapshot.map {
                    ScreenerQuestion(
                        question = it.title_text ?: "",
                        answer = InquiryInstanceTextFormUtil.construct_answer_text(it, it.answer)
                    )
                }
            }
        }
        val expert = Expert(
            bio = advisor?.background ?: "",
            country = advisor?.location?.name ?: "",
            job_title = advisor?.current_jobs?.joinToString(" | ") { it.position } ?: "",
            first_name = advisor?.firstname ?: "",
            last_name = advisor?.lastname ?: "",
            organization = advisor?.current_jobs?.mapNotNull { it.company?.name }?.joinToString(" | ") ?: "",
            angles = "",
            screener_questions = screener_questions ?: emptyList()
        )

        val request = Request(
            id = task?.id?.toString() ?: "",
            case_code = task?.project?.code ?: "",
            project_id = task?.project_id?.toString() ?: "",
            project_title = task?.project?.name ?: "",
            client = task?.client?.name ?: "",
            project_email_body = task?.project?.request_content ?: ""
        )

        return WarburgPincusMeetingData(
            audio_file_name = audio_file_name,
            date = task?.start_time?.toString() ?: "",
            duration = duration,
            id = asset.asset_id,
            participants = participants,
            expert = expert,
            request = request
        )
    }

    fun upload_call_json(
        asset: ZoomAsset,
        task: Task?,
        audio_file_name: String,
        bucket: String,
        folder_name: String,
    ) {
        val file_name = audio_file_name.substringBeforeLast(".") + ".json"
        val data = generate_warburg_pincus_call_json(asset, task, audio_file_name)
        val json_str = CoreJsonUtil.stringify(data)
        val input_stream = ByteArrayInputStream(json_str.toByteArray(StandardCharsets.UTF_8))
        val content_length = json_str.toByteArray(StandardCharsets.UTF_8).size.toLong()

        val request = PutObjectRequest.builder()
            .bucket(bucket)
            .key("$folder_name/$file_name")
            .build()

        try {
            warburgPincusS3Client!!.putObject(
                request,
                RequestBody.fromInputStream(input_stream, content_length)
            )
        } catch (e: Exception) {
            logger.error("S3 upload failed: ${e.message}", e)
        } finally {
            input_stream.close()
        }
    }

    fun open_zoom_asset_connection(
        is_from_sync: Boolean = false,
        asset: ZoomAsset,
    ): HttpURLConnection? {
        val conn = if (is_from_sync) {
            if (asset.capvision_file_url.isBlank()) return null
            URL(asset.capvision_file_url).openConnection() as HttpURLConnection
        } else {
            (URL(asset.download_url).openConnection() as HttpURLConnection).apply {
                setRequestProperty("Authorization", "Bearer ${asset.download_token}")
            }
        }
        conn.connect()

        if (conn.responseCode != HttpURLConnection.HTTP_OK) {
            logger.error("Failed to download file. HTTP Response: ${conn.responseCode}")
            return null
        }
        return conn
    }

    fun generate_file_name(asset: ZoomAsset, task: Task?): String {
        val asset_name = when (asset.file_type) {
            ZoomAsset.FileType.M4A -> asset.asset_id + ".m4a"
            ZoomAsset.FileType.MP4 -> asset.asset_id + ".mp4"
            ZoomAsset.FileType.TRANSCRIPT -> asset.asset_id + ".txt"
            else -> asset.asset_id
        }
        val date_string = task?.start_time?.let {
            DateTimeFormatter.ofPattern("MM.dd.yyyy").withZone(ZoneId.systemDefault()).format(it)
        }
        return listOfNotNull(task?.project?.name, date_string, asset_name).joinToString("-")
    }

    fun upload_file_to_warburg_pincus_s3(
        asset: ZoomAsset,
        is_from_sync: Boolean = false,
        task: Task?,
        folder_name: String,
    ) {
        warburgPincusS3Client ?: return
        val warburgPincusproperties =
            s3_properties.clients.firstOrNull { it.clientName == S3SdkProperties.WARBURG_PINCUS } ?: return

        val conn = open_zoom_asset_connection(is_from_sync, asset) ?: return
        val file_name = generate_file_name(asset, task)

        upload_call_json(asset, task, file_name, warburgPincusproperties.bucket, folder_name)

        val request = PutObjectRequest.builder()
            .bucket(warburgPincusproperties.bucket)
            .key("$folder_name/$file_name")
            .build()

        try {
            conn.inputStream.use { input_stream ->
                warburgPincusS3Client!!.putObject(
                    request,
                    RequestBody.fromInputStream(input_stream, conn.contentLengthLong)
                )
            }
        } catch (e: Exception) {
            logger.error("S3 upload failed: ${e.message}", e)
        } finally {
            conn.disconnect()
        }
    }

}