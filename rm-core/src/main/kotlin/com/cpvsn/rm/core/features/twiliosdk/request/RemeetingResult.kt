package com.cpvsn.rm.core.features.twiliosdk.request

import com.fasterxml.jackson.annotation.JsonIgnore

data class RemeetingResult(
        val user_token: String,
        val results: List<Results>,
        val id: String,
        val event: String,
) {
    data class Results(
            val result_index: Int,
            val results: List<Sentence>,
    )

    data class Sentence(
            val alternatives: List<SentenceAlternative>,
            val final: Boolean,
    )

    data class SentenceAlternative(
            val confidence: Double,
            val speaker_label: String,
            val transcript: String,
    )

    @get:JsonIgnore
    val sentences: List<SentenceAlternative>
        get() = results.firstOrNull()
            ?.results.orEmpty()
            .mapNotNull {
                it.alternatives.firstOrNull()
            }
            .filter { it.transcript.isNotBlank() }
}