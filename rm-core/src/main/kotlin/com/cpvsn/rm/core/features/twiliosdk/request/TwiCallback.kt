package com.cpvsn.rm.core.features.twiliosdk.request

import com.cpvsn.rm.core.features.twiliosdk.enums.TwiStatusCallbackEventEnum
import com.cpvsn.rm.core.features.twiliosdk.enums.TwilioAddOnEnum
import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import java.time.Instant
import java.time.format.DateTimeFormatter
import java.util.*

interface TwiCallback {
    @Suppress("EnumEntryName")
    @JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy::class)
    data class ConferenceRecordingStatusCallbackRequest(
            val RecordingSource: String = "",
            val RecordingSid: String = "",
            val ConferenceSid: String = "",
            val RecordingUrl: String = "",
            val RecordingStatus: String = "",
            val CallSid: String = "",
            val RecordingChannels: String = "",
            val RecordingStartTime: String = "",
            val AccountSid: String = "",
    ) {
        val recordingStatusEnum: RecordingStatusEnum
            get() = RecordingStatusEnum.valueOf(RecordingStatus)

        enum class RecordingStatusEnum {
            `in-progress`,
            completed,
            absent
        }
    }

    /*
     * https://www.twilio.com/docs/voice/twiml/conference#attributes-statusCallback-parameters
     */
    @Suppress("EnumEntryName")
    @JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy::class) // https://blog.csdn.net/niugang0920/article/details/*********
    data class ConferenceStatusCallbackRequest(
            val ConferenceSid: String = "",
            val FriendlyName: String = "",
            val ParticipantLabel: String = "",
            val Coaching: String = "",
            val AccountSid: String = "",
            val SequenceNumber: String = "",
            val Timestamp: String = "",
            val StatusCallbackEvent: String = "",
            val CallSidEndingConference: String = "",
            val ReasonConferenceEnded: String = "",
            val ParticipantLabelEndingConference: String = "",
            val Reason: String = "",
            val CallSid: String = "",
    ) {
        companion object {
            @JsonIgnore
            val timestamp_formatter: DateTimeFormatter =
                DateTimeFormatter.ofPattern("EEE, d MMM yyyy HH:mm:ss Z", Locale.US)
        }
        @get:JsonIgnore
        val statusCallbackEventEnum: TwiStatusCallbackEventEnum
            get() = TwiStatusCallbackEventEnum.valueOf(StatusCallbackEvent)
        @get:JsonIgnore
        val TimestampTs: Long
            get() = Instant.from(timestamp_formatter.parse(Timestamp)).epochSecond
    }

    @JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy::class)
    data class GatherActionCallbackRequest(
        val AccountSid: String = "",
        val ApiVersion: String = "",
        val CallSid: String = "",
        val CallStatus: String = "",
        val Called: String = "",
        val CalledCity: String = "",
        val CalledCountry: String = "",
        val CalledState: String = "",
        val CalledZip: String = "",
        val Caller: String = "",
        val CallerCity: String = "",
        val CallerCountry: String = "",
        val CallerState: String = "",
        val CallerZip: String = "",
        val Digits: String = "",
        val Direction: String = "",
        val FinishedOnKey: String = "",
        val From: String = "",
        val FromCity: String = "",
        val FromCountry: String = "",
        val FromState: String = "",
        val FromZip: String = "",
        val To: String = "",
        val ToCity: String = "",
        val ToCountry: String = "",
        val ToState: String = "",
        val ToZip: String = "",
        val msg: String = "",
        val SpeechResult: String = "",
        val Language: String = "",
        val Confidence: String = "",
    )

    @JsonNaming(PropertyNamingStrategy.UpperCamelCaseStrategy::class)
    data class IncomingCallRequest(
            val Called: String = "",
            val ToState: String = "",
            val CallerCountry: String = "",
            val Direction: String = "",
            val CallerState: String = "",
            val ToZip: String = "",
            val CallSid: String = "",
            val To: String = "",
            val CallerZip: String = "",
            val ToCountry: String = "",
            val CallToken: String = "",
            val ApiVersion: String = "",
            val CalledZip: String = "",
            val CallStatus: String = "",
            val CalledCity: String = "",
            val From: String = "",
            val AccountSid: String = "",
            val CalledCountry: String = "",
            val CallerCity: String = "",
            val Caller: String = "",
            val FromCountry: String = "",
            val ToCity: String = "",
            val FromCity: String = "",
            val CalledState: String = "",
            val FromZip: String = "",
            val FromState: String = "",
            // TwiML App customization
            val IsJsDevice: String = "",
            val TaskId: String = "",
            val EmailAddress: String = "",
            val InputName: String = ""
    )

    /**
     * See Add-On documention page.
     *
     * 流程与格式：https://www.twilio.com/docs/add-ons/how-to-use#add-on-results-available-callback
     * https://www.twilio.com/docs/add-ons/api/results
     */
    data class TranscriptionAddOnCallbackRequest(
            val status: String = "",
            val message: String? = null,
            val code: String? = null,
            val results: Results,
    ) {
        companion object {
            /**
             * Account Sid (AC)
             * Recording Sid (RE)
             * Request Sid (XR: Add-On Request)
             * Payload Sid (XH)
             */
            val payload_url_regex =
                "https://api.twilio.com/2010-04-01/Accounts/([a-zA-Z0-9]+)/Recordings/([a-zA-Z0-9]+)/AddOnResults/([a-zA-Z0-9]+)/Payloads/([a-zA-Z0-9]+)/Data".toRegex()
        }

        data class Links(
                val add_on_result: String = "",
                val payloads: String = "",
                val recording: String = "",
        )

        data class PayloadItem(
                val content_type: String = "",
                /**
                 * this is the final result
                 */
                val url: String = "",
        )

        data class ResultData(
            val request_sid: String = "",
            val status: String = "",
            val message: String? = null,
            val code: String? = null,
            val payload: List<PayloadItem> = emptyList(),
            val links: Links,
        )

        data class Results(
                // unique name of Add-On
                val ibm_watson_speechtotext: ResultData? = null,
                val voicebase_transcription: ResultData? = null,
                val remeeting_speech_recognition: ResultData? = null,
        )

        val add_on: TwilioAddOnEnum by lazy {
            when {
                results.ibm_watson_speechtotext != null -> TwilioAddOnEnum.ibm_watson_speechtotext
                results.voicebase_transcription != null -> TwilioAddOnEnum.voicebase_transcription
                results.remeeting_speech_recognition != null -> TwilioAddOnEnum.remeeting_speech_recognition
                else -> error("unconfigured")
            }
        }

        val result_data = results.ibm_watson_speechtotext
            ?: results.voicebase_transcription
            ?: results.remeeting_speech_recognition!!

        // val links: Links = result_data.links

        val record_sid: String? by lazy {
            val payload0: PayloadItem? = result_data.payload.firstOrNull()
            val ids: List<String> = payload0?.let {
                payload_url_regex.find(it.url)?.groupValues ?: emptyList()
            }.orEmpty()
            ids.getOrNull(2)
        }
    }
}