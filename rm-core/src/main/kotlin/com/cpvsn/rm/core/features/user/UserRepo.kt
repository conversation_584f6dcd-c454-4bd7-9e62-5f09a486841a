package com.cpvsn.rm.core.features.user

import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.model.CrudOptions
import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.extensions.contains_any
import com.cpvsn.rm.core.extensions.to_ids
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntry
import com.cpvsn.rm.core.features.core.app_config.AppConfigKey
import com.cpvsn.rm.core.util.PasswordEncoders
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class UserRepo : RmBaseRepository<User>(), JdbcEntityBatchRepo<Int, User> {

    override val batchDao: JdbcEntityBatchDao<Int, User> by lazy {
        JdbcEntityBatchDao(User::class, dataSource)
    }

    override fun patch(patch: Patch<User>, option: CrudOptions.PatchOption<User>) {
        var p = patch

        if (patch.fields.contains_any(User::given_name.name, User::family_name.name)) {
            if (!patch.fields.containsAll(setOf(
                            User::given_name.name, User::family_name.name
                    ))) {
                val existed = User.get(patch.entity.id)
                if (User::given_name.name !in patch.fields) {
                    patch.entity.given_name = existed.given_name
                }
                if (User::family_name.name !in patch.fields) {
                    patch.entity.family_name = existed.family_name
                }
            }
        }

        p.entity.password?.let { raw_password ->
            if (p.fields.contains(User::password.name)) {
                p.entity.password = PasswordEncoders.BCryptEncoder.encode(raw_password)
            }
        }

        super.patch(p, option)
    }

    override fun handleIncludeProperty(
        list: List<User>,
        propertyName: String,
        nestedIncludes: Set<String>
    ) {
        when (propertyName) {
            User::enabled_outreach_ai_refine.name -> {
                val enabled_ids = AppConfigEntry.firstOrNull(
                    AppConfigEntry.Query(
                        key_enum = AppConfigKey.AI_COLD_EMAIL_PERSONALIZATION_BY_EMPLOYEE
                    )
                )?.value.orEmpty().to_ids()
                list.forEach {
                    it.enabled_outreach_ai_refine = it.id in enabled_ids
                }
            }

            else -> return
        }
    }
}
