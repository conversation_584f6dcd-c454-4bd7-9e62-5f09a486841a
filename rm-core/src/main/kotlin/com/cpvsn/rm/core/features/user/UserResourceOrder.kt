package com.cpvsn.rm.core.features.user

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchDao
import com.cpvsn.crud.jdbc.batch.JdbcEntityBatchRepo
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.TableDefinition
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.base.service.RmBaseRepository
import org.springframework.stereotype.Repository

@TableDefinition(
    indices = [
        TableDefinition.IndexDefinition(
            columns = ["user_id", "resource_type", "resource_id"],
            type = TableDefinition.IndexType.UNIQUE
        )
    ]
)
class UserResourceOrder : RmEntity() {
    companion object : RmCompanion<UserResourceOrder>() {
        override fun saveSql(): String {
            return super.saveSql() + " on duplicate key update display_order=#{display_order}"
        }
    }

    @Column
    override var id: Int = 0

    @Column
    var user_id: Int = 0

    @Column
    var resource_id: Int = 0

    @Column
    var resource_type: Type = Type.TASK_ANGLE

    @Column
    var display_order: Int = 0

    enum class Type {
        TASK_ANGLE,
        PROJECT, //project & user are for the allocation dashboard
        USER,
        LEAD_GROUP,
    }

    data class Query(
            @Criteria.Eq
            val id: Int? = null,
            @Criteria.IdsIn
            val ids: Set<Int>? = null,
            @Criteria.Eq
            val user_id: Int? = null,
            @Criteria.IdsIn
            val resource_ids: Set<Int>? = null,
            @Criteria.Eq
            val resource_type: Type? = null,
    ) : BaseQuery<UserResourceOrder>()

    @Repository
    class Repo : RmBaseRepository<UserResourceOrder>(), JdbcEntityBatchRepo<Int, UserResourceOrder> {
        override val batchDao: JdbcEntityBatchDao<Int, UserResourceOrder> by lazy {
            JdbcEntityBatchDao(UserResourceOrder::class, dataSource)
        }
    }
}
