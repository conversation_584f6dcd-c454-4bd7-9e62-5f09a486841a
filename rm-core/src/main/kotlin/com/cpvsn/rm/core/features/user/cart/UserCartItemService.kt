package com.cpvsn.rm.core.features.user.cart

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.extensions.user_id
import com.cpvsn.rm.core.util.EntityRepositionUtil
import com.cpvsn.rm.core.util.biz_require
import com.cpvsn.web.auth.AuthContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Service

@Service
class UserCartItemService {

    @Autowired
    private lateinit var repo: UserCartItem.Repo

    fun create_batch(
        items: List<UserCartItem>,
        ref_type: UserCartItem.RefType,
        user_id: Int,
    ): Int {
        biz_require(items.isNotEmpty()) {
            "items must not be empty"
        }
        items.forEach { it.user_id = user_id }
        items.forEach { it.ref_type = ref_type }

        UserCartItem.save(items)

        return UserCartItem.count(
            UserCartItem.Query(
                user_id = user_id,
                ref_type = ref_type,
            )
        )
    }

    fun remove_batch(
        query: UserCartItem.Query,
        user_id: Int,
    ): Int {
        val items = UserCartItem.findAll(
            query.copy(user_id = AuthContext.user_id)
        )
        repo.batchDelete(items.ids().toList())
        val ref_type = items
            .map { it.ref_type }.toSet().singleOrNull()
        return UserCartItem.count(
            UserCartItem.Query(
                user_id = user_id,
                ref_type = ref_type,
            )
        )
    }

    fun reposition(
        id_index_map: Map<Int, Int>,
        ref_type: UserCartItem.RefType,
        user_id: Int,
    ): List<UserCartItem> {
        val items = UserCartItem
            .listAll(query = UserCartItem.Query(
                user_id = user_id,
                ref_type = ref_type,
            ), sort = Sort.by {
                asc("display_order_is_null")
                asc(UserCartItem::display_order.name)
                asc(UserCartItem::id.name)
            })
        val least_updated_items =
            EntityRepositionUtil.reposition(items, id_index_map)
        least_updated_items
            .forEachIndexed { index, item ->
                item.display_order = index
            }
        repo.batchPatch(
            least_updated_items,
            fields = Includes.setOf(UserCartItem::display_order),
        )
        return least_updated_items
    }

}
