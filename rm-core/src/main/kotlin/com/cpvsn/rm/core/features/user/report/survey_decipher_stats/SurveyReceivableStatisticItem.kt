package com.cpvsn.rm.core.features.user.report.survey_decipher_stats

import com.cpvsn.rm.core.extensions.safe_change_precision_to_2digits
import com.cpvsn.rm.core.features.project.transaction.ReceivableTransaction

class SurveyReceivableStatisticItem {
    companion object {
        fun from_receivable_transaction(receivable: ReceivableTransaction): SurveyReceivableStatisticItem {
            return SurveyReceivableStatisticItem().apply {
                this.type = receivable.type.name
                this.transaction_date = receivable.transaction_date
                this.amount = receivable.amount.safe_change_precision_to_2digits().toPlainString()
                this.currency = receivable.currency.name
                this.notes = receivable.notes
                this.create_by = receivable.create_by?.name
            }
        }
    }

    var type: String? = null
    var transaction_date: String? = null
    var amount: String? = null
    var currency: String? = null
    var notes: String? = null
    var create_by: String? = null
}