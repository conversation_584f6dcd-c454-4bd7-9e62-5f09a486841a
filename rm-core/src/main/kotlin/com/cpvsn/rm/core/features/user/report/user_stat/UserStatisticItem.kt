package com.cpvsn.rm.core.features.user.report.user_stat

import com.cpvsn.core.base.model.BaseModelCompanion
import com.cpvsn.core.util.extension.BD
import com.cpvsn.rm.core.extensions.safe_change_precision_to_2digits
import com.cpvsn.rm.core.extensions.to_ids_list
import com.cpvsn.rm.core.extensions.to_percent_str
import com.fasterxml.jackson.annotation.JsonIgnore
import java.math.BigDecimal

class UserStatisticItem {
    companion object : BaseModelCompanion<UserStatisticItem>()

    var id: Int = 0
    var name: String = ""

    //region from sql
    @JsonIgnore
    var ids_project_as_support: String = ""

    @JsonIgnore
    var ids_project_as_manager: String = ""

    @JsonIgnore
    var ids_leads_contacted: String = ""

    @JsonIgnore
    var task_ids_leads_contacted: String = ""
    //endregion

    val ids_list_project_as_support: List<Int>
        get() = ids_project_as_support.to_ids_list()


    val ids_list_project_as_manager: List<Int>
        get() = ids_project_as_manager.to_ids_list()


    val ids_list_leads_contacted: List<Int>
        get() = ids_leads_contacted.to_ids_list()


    val task_ids_list_leads_contacted: List<Int>
        get() = task_ids_leads_contacted.to_ids_list()


    var ids_list_leads_sent_tc: List<Int> = emptyList()

    var ids_list_leads_accept_tc: List<Int> = emptyList()

    var ids_list_tc_consulted: List<Int> = emptyList()

    /**
     * So in total 6 fields:
     *
     * Total Advisors Consulted
     * Advisors Consulted (in-network)
     * Advisors Consulted (custom sourced)
     * Total Consultations Completed
     * Consultations Completed (in-network)
     * Consultations Completed (custom sourced)
     */
    @JsonIgnore
    var ids_list_advisor_consulted: List<Int> = emptyList()
    var ids_list_advisor_consulted_in_network: List<Int> = emptyList()
    var ids_list_advisor_consulted_custom_sourced: List<Int> = emptyList()

    @JsonIgnore
    var task_ids_list_consulted: List<Int> = emptyList()
    var task_ids_list_consulted_in_network: List<Int> = emptyList()
    var task_ids_list_consulted_custom_sourced: List<Int> = emptyList()

    var consultation_completed_hours_in_network: BigDecimal = BigDecimal.ZERO
    var consultation_completed_hours_custom_sourced: BigDecimal = BigDecimal.ZERO

    var avg_actual_expert_cost: BigDecimal = BigDecimal.ZERO
    var avg_effective_expert_cost: BigDecimal = BigDecimal.ZERO

    fun remember_counts() {
        count_project_as_support = ids_list_project_as_support.size
        count_project_as_manager = ids_list_project_as_manager.size
        count_leads_contacted = task_ids_list_leads_contacted.size

        count_leads_sent_tc = ids_list_leads_sent_tc.size
        count_leads_accept_tc = ids_list_leads_accept_tc.size

        count_advisor_consulted = ids_list_advisor_consulted.size
        count_advisor_consulted_in_network = ids_list_advisor_consulted_in_network.size
        count_advisor_consulted_custom_sourced = ids_list_advisor_consulted_custom_sourced.size
        count_task_consulted = task_ids_list_consulted.size
        count_task_consulted_in_network = task_ids_list_consulted_in_network.size
        count_task_consulted_custom_sourced = task_ids_list_consulted_custom_sourced.size
    }

    fun clear_ids() {
        ids_project_as_support = ""
        ids_project_as_manager = ""
        ids_leads_contacted = ""
        task_ids_leads_contacted = ""

        ids_list_leads_sent_tc = emptyList()
        ids_list_leads_accept_tc = emptyList()
        ids_list_tc_consulted = emptyList()

        ids_list_advisor_consulted = emptyList()
        ids_list_advisor_consulted_in_network = emptyList()
        ids_list_advisor_consulted_custom_sourced = emptyList()
        task_ids_list_consulted = emptyList()
        task_ids_list_consulted_in_network = emptyList()
        task_ids_list_consulted_custom_sourced = emptyList()
    }

    var count_project_as_support: Int = 0
    var count_project_as_manager: Int = 0
    var count_leads_contacted: Int = 0
    var count_leads_sent_tc: Int = 0
    var count_leads_accept_tc: Int = 0

    var count_advisor_consulted: Int = 0
    var count_advisor_consulted_in_network: Int = 0
    var count_advisor_consulted_custom_sourced: Int = 0
    var count_task_consulted: Int = 0
    var count_task_consulted_in_network: Int = 0
    var count_task_consulted_custom_sourced: Int = 0

    //endregion

    var avg_signed_tc_rate_usd: BigDecimal? = null

    //region percentages
    @JsonIgnore
    var pt_sent_contacted: BigDecimal? = null

    @JsonIgnore
    var pt_accept_sent: BigDecimal? = null

    /**
     * Conversion Rate (Consulted/Leads Accept TC)
     */
    @JsonIgnore
    var pt_conversion: BigDecimal? = null

    /**
     * Conversion Rate (Accept TC/Leads Contacted)
     */
    @JsonIgnore
    var pt_conversion_accept_tc_vs_contacted: BigDecimal? = null

    // % format
    val percentage_sent_contacted: String
        get() = pt_sent_contacted?.to_percent_str() ?: ""
    val percentage_accept_sent: String
        get() = pt_accept_sent?.to_percent_str() ?: ""
    val percentage_conversion: String
        get() = pt_conversion?.to_percent_str() ?: ""
    val percentage_accept_tc_vs_contacted: String
        get() = pt_conversion_accept_tc_vs_contacted
            ?.multiply(100.BD)
            ?.safe_change_precision_to_2digits()
            ?.let { "$it%" }
            .orEmpty()
    //endregion
}
