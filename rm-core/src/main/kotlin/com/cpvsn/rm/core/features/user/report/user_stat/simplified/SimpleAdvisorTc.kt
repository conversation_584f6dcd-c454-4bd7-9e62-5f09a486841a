package com.cpvsn.rm.core.features.user.report.user_stat.simplified

import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import java.math.BigDecimal
import java.time.Instant

class SimpleAdvisorTc {
    var id: Int = 0

    var advisor_id: Int = 0

    var rate: BigDecimal? = null

    var rate_currency: String? = null

    var approved: Boolean = false

    var create_time: Instant? = null

    var respond_time: Instant? = null

    var create_by_id: Int = 0

    var create_at: Instant? = null

    var task_id: Int? = null

    var project_id: Int? = null

    val rate_currency_enum: BuiltInCurrency?
        get() = BuiltInCurrency.values().firstOrNull { it.name == rate_currency }
}