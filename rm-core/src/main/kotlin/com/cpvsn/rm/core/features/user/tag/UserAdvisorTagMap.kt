package com.cpvsn.rm.core.features.user.tag

import com.cpvsn.core.base.entity.BaseQuery
import com.cpvsn.core.base.entity.UserAuditing
import com.cpvsn.crud.orm.annotation.Column
import com.cpvsn.crud.orm.annotation.Relation
import com.cpvsn.crud.orm.annotation.TableDefinition
import com.cpvsn.crud.query.Criteria
import com.cpvsn.rm.core.base.entity.RmCompanion
import com.cpvsn.rm.core.base.entity.RmEntity
import com.cpvsn.rm.core.features.advisor.Advisor
import com.fasterxml.jackson.annotation.JsonIgnore

@TableDefinition(
    indices = [
        TableDefinition.IndexDefinition(
            columns = ["tag_id", "advisor_id"],
            type = TableDefinition.IndexType.UNIQUE
        ),
    ],
)
class UserAdvisorTagMap : RmEntity(), UserAuditing {
    companion object : RmCompanion<UserAdvisorTagMap>() {
        override fun saveSql(): String {
            return super.saveSql()
                .plus(" on duplicate key update advisor_id=#{advisor_id}")
        }
    }

    @Column
    var tag_id: Int = 0

    @Column
    var advisor_id: Int = 0

    @Column
    override var create_by_id: Int = 0

    // no need to persist
    @get: JsonIgnore
    override var update_by_id: Int = 0

    @Relation
    var tag: UserAdvisorTag? = null

    @Relation
    var advisor: Advisor? = null

    data class Query(
        @Criteria.Eq
        val id: Int? = null,
        @Criteria.IdsIn
        val ids: Set<Int>? = null,
        @Criteria.Eq
        val tag_id: Int? = null,
        @Criteria.IdsIn
        val tag_ids: Set<Int>? = null,
        @Criteria.Eq
        val advisor_id: Int? = null,
        @Criteria.IdsIn
        val advisor_ids: Set<Int>? = null,
        @Criteria.Join
        val tag: UserAdvisorTag.Query? = null,
        @Criteria.Join
        val advisor: Advisor.Query? = null,
    ) : BaseQuery<UserAdvisorTagMap>()
}
