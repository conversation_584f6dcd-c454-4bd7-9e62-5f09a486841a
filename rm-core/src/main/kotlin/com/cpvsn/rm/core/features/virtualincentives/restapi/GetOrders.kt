package com.cpvsn.rm.core.features.virtualincentives.restapi

import com.cpvsn.rm.core.features.virtualincentives.restapi.base.VirtualIncentivesEndpoint
import com.cpvsn.rm.core.features.virtualincentives.restapi.common.Order
import org.springframework.http.HttpMethod

class GetOrders {

    data class Request(
        val number: String? = null,
        val status: String? = null,
        val programid: String? = null,
        val clientid: String? = null,
        val start: String? = null,
        val end: String? = null,
    )

    data class Response(
        val orders: List<Order>,
    )

    companion object {
        fun endPoint() = VirtualIncentivesEndpoint(
            url = "https://rest.virtualincentives.com/v6/json/orders",
            httpMethod = HttpMethod.GET,
        )
    }
}