package com.cpvsn.rm.core.features.virtualincentives.restapi.common

import com.cpvsn.rm.core.features.currency_conversion.ISOCurrency
import com.cpvsn.rm.core.features.virtualincentives.enums.VirtualIncentivesEnum

data class Program(
    val name: String? = null,
    val programid: String? = null,
    val currency: String? = null,
    val type: String? = null,
    val language: String? = null,
) {
    val currency_enum = currency?.let {
        try {
            ISOCurrency.valueOf(it)
        } catch (e: Exception) {
            null
        }
    }
}