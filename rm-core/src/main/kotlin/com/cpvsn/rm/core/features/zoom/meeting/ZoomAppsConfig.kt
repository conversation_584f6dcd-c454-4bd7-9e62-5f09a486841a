package com.cpvsn.rm.core.features.zoom.meeting

import com.cpvsn.core.svc.spring.Env
import com.cpvsn.rm.core.config.oversea.OverSeaEnvService
import com.cpvsn.rm.core.features.zoom.meeting.base.ZoomApp
import com.cpvsn.rm.core.features.zoom.meeting.base.ZoomAppEnum
import com.cpvsn.rm.core.features.zoom.meeting.entity.ZoomMeeting
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.stereotype.Component
import javax.annotation.PostConstruct

@Component
@EnableConfigurationProperties(ZoomProperties::class)
class ZoomAppsConfig {
    private var MAP_ZOOM_APP = mutableMapOf<ZoomAppEnum, ZoomApp>()

    @Autowired
    private lateinit var zoomProperties: ZoomProperties

    @Autowired
    private lateinit var overSeaEnvService: OverSeaEnvService

    @PostConstruct
    fun postConstruct() {
        zoomProperties.apps.forEach {
            MAP_ZOOM_APP[it.appEnum] = ZoomApp(it)
        }
    }

    fun getZoomApp(appEnum: ZoomAppEnum): ZoomApp {
        return MAP_ZOOM_APP[appEnum]!!
    }


    fun getAppByMeetingOrDefault(meeting: ZoomMeeting): ZoomApp {
        return if (meeting.app_enum != null) {
            MAP_ZOOM_APP[meeting.app_enum!!]!!
        } else {
            val is_production = overSeaEnvService.current() in setOf(
                Env.PRODUCTION,
                Env.DEFAULT
            )
            if (is_production) {
                MAP_ZOOM_APP[ZoomAppEnum.USDB_ENGINEER_S2S]!!
            } else {
                MAP_ZOOM_APP[ZoomAppEnum.USDB_ENGINEER_2_S2S]!!
            }
        }
    }
}