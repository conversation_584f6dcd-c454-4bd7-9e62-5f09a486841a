package com.cpvsn.rm.core.features.zoom.meeting

import com.cpvsn.core.util.extension.assert_exist
import com.cpvsn.core.util.extension.remove_prefix
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.base.service.RmBaseRepository
import com.cpvsn.rm.core.features.zoom.meeting.entity.*
import com.cpvsn.rm.core.features.zoom.meeting.pojo.ReadableMeetingEvent
import com.cpvsn.rm.core.features.zoom.meeting.pojo.ScheduledCallTabParticipantZoom
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.stereotype.Repository
import org.springframework.transaction.annotation.Transactional
import kotlin.Exception

@Repository
class ZoomMeetingRepository : RmBaseRepository<ZoomMeeting>() {

    @Autowired
    private lateinit var timerService: ZoomMeetingTimerService

    @Transactional
    fun save_cascade(
        meeting: ZoomMeeting,
    ): ZoomMeeting {
        meeting.save(
            cascades = setOf(
                Cascade.oneToMany(
                    ZoomMeeting::participants,
                    callback = Cascade.Callback.prePersist { participant, m ->
                        participant.meeting_entity_id = m.id
                        participant.meeting_id = m.meeting_id
                    }
                )
            )
        )
        return meeting
    }

    override fun handleJoin(
        list: List<ZoomMeeting>,
        include: Set<String>
    ): List<ZoomMeeting> {
        val res = super.handleJoin(list, include)

        // manully join here to ensure sort
        if (include.contains(ZoomMeeting::instances.name)) {
            val map = ZoomMeetingInstance.findAll(
                ZoomMeetingInstance.Query(
                    meeting_entity_ids = res.ids()
                ), include.remove_prefix(ZoomMeeting::instances)
            ).sortedBy {
                it.id
            }.groupBy { it.meeting_entity_id }
            res.forEach {
                it.instances = map[it.id].orEmpty()
            }
        }

        if (include.contains(ZoomMeeting::latest_instance.name)) {
            val map = ZoomMeetingInstance.findAll(
                ZoomMeetingInstance.Query(
                    meeting_entity_ids = res.ids(),
                ), include.remove_prefix(ZoomMeeting::latest_instance)
            )
                .groupBy { it.meeting_entity_id }
                .mapValues { instance ->
                    instance.value.maxByOrNull { it.id }
                }
            res.forEach {
                it.latest_instance = map[it.id]
            }
        }

        // manully join here to ensure sort
        if (include.contains(ZoomMeeting::meeting_events.name)) {
            val map = ZoomMeetingEvent.findAll(
                ZoomMeetingEvent.Query(
                    meeting_entity_ids = res.ids()
                ), include.remove_prefix(ZoomMeeting::meeting_events)
            ).sortedBy {
                it.timestamp
            }.groupBy { it.meeting_entity_id }

            res.forEach {
                it.meeting_events = map[it.id].orEmpty()
            }
        }

        if (include.contains(ZoomMeeting::scheduled_call_tab_participants.name)) {
            val map = ZoomMeeting.findAll(
                ZoomMeeting.Query(
                    ids = res.ids()
                ), Includes.setOf(
                    ZoomMeeting::participants dot ZoomMeetingParticipant::participant_entries
                )
            ).associateBy {
                it.id
            }
            res.forEach { meeting ->
                val self = map[meeting.id]!!
                meeting.scheduled_call_tab_participants = self.participants.orEmpty().map {
                    ScheduledCallTabParticipantZoom(
                        participant = it,
                        entries = it.participant_entries.orEmpty(),
                        meeting_link = meeting.join_url
                    )
                }
            }
        }

        if (include.contains(ZoomMeeting::readable_events.name)) {
            val ids = res.ids()

            val map_events = ZoomMeetingEvent.findAll(
                ZoomMeetingEvent.Query(meeting_entity_ids = ids)
            ).sortedBy {
                it.timestamp
            }.groupBy { it.meeting_entity_id }
            val map_instances = ZoomMeetingInstance.findAll(
                ZoomMeetingInstance.Query(meeting_entity_ids = ids)
            ).associateBy { it.meeting_uuid }
            val map_entries = ZoomMeetingEntry.findAll(
                ZoomMeetingEntry.Query(meeting_entity_ids = ids),
                Includes.setOf(ZoomMeetingEntry::participant)
            ).associateBy { it.participant_uuid }

            res.forEach { meeting ->
                // sometimes instance record could lost
                try {
                    val events = map_events[meeting.id].orEmpty()
                    meeting.readable_events = events.map { e ->
                        ReadableMeetingEvent(
                            meeting = meeting,
                            instance = map_instances[e.meeting_uuid],
                            event = e,
                            entry = map_entries[e.participant_uuid],
                            participant = map_entries[e.participant_uuid]?.participant
                        )
                    }
                } catch (e: Exception) {
                    return@forEach
                }
            }
        }

        if (include.contains(ZoomMeeting::state_time_travel.name)) {
            if (res.any { it.readable_events == null }) {
                handleJoin(res, Includes.setOf(ZoomMeeting::readable_events))
            }
            res.forEach {
                it.state_time_travel = timerService.time_travel(it, it.readable_events!!)
            }
        }

        return res
    }
}