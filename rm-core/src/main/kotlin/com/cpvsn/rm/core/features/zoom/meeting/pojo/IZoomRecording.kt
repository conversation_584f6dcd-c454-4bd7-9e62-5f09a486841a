package com.cpvsn.rm.core.features.zoom.meeting.pojo

interface IZoomRecording {
    val id: Long
    val recording_files: List<IRecordingFile>
    val recording_play_passcode: String
    val start_time: String
    val uuid: String

    interface IRecordingFile {
        val download_url: String
        val file_type: String
        val id: String
        val play_url: String
        val recording_type: String
        val file_size: Long
        val recording_end: String
        val recording_start: String
    }
}