package com.cpvsn.rm.core.features.zoom.meeting.restapi

data class DashboardMeetingParticipantsResponse(
    val next_page_token: String = "",
    val page_count: Int = 0,
    val page_size: Int = 0,
    val total_records: Int = 0,
    val participants: List<Participant> = listOf()
) {
    data class Participant(
        val audio_quality: String = "",
        val camera: String = "",
        val connection_type: String = "",
        val customer_key: String = "",
        val data_center: String = "",
        val device: String = "",
        val domain: String = "",
        val email: String = "",
        val from_sip_uri: String = "",
        val full_data_center: String = "",
        val harddisk_id: String = "",
        val id: String = "",
        val in_room_participants: Int = 0,
        val internal_ip_addresses: List<String> = listOf(),
        val ip_address: String = "",
        val join_time: String = "",
        val leave_reason: String = "",
        val leave_time: String = "",
        val location: String = "",
        val mac_addr: String = "",
        val microphone: String = "",
        val network_type: String = "",
        val participant_user_id: String = "",
        val pc_name: String = "",
        val recording: Boolean = false,
        val registrant_id: String = "",
        val role: String = "",
        val screen_share_quality: String = "",
        val share_application: Boolean = false,
        val share_desktop: Boolean = false,
        val share_whiteboard: Boolean = false,
        val sip_uri: String = "",
        val speaker: String = "",
        val status: String = "",
        val user_id: String = "",
        val user_name: String = "",
        val version: String = "",
        val video_quality: String = "",
        val bo_mtg_id: String = "",
        val audio_call: List<AudioCall> = listOf(),
        //
        val os: String = ""
    ) {
        data class AudioCall(
            val call_number: String = "",
            val call_type: String = "",
            val zoom_number: String = ""
        )
    }
}