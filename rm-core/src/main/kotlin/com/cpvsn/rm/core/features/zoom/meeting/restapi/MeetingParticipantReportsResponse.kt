package com.cpvsn.rm.core.features.zoom.meeting.restapi

data class MeetingParticipantReportsResponse(
    val next_page_token: String?,
    val page_count: Int?,
    val page_size: Int?,
    val total_records: Int?,
    val participants: List<Participant>?
) {
    data class Participant(
        val customer_key: String?,
        val duration: Int?,
        val failover: Boolean?,
        val id: String?,
        val join_time: String?,
        val leave_time: String?,
        val name: String?,
        val registrant_id: String?,
        val status: String?,
        val user_email: String?,
        val user_id: String?,
        val bo_mtg_id: String?,
        val participant_user_id: String?
    )
}

