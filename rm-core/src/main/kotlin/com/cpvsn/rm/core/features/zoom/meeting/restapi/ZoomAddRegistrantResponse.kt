package com.cpvsn.rm.core.features.zoom.meeting.restapi

data class ZoomAddRegistrantResponse(
    val id: Long = 0,
    val join_url: String = "",
    val registrant_id: String = "",
    val start_time: String = "",
    val topic: String = "",
    val occurrences: List<Occurrence> = listOf(),
    val participant_pin_code: Int = 0
) {
    data class Occurrence(
        val duration: Int = 0,
        val occurrence_id: String = "",
        val start_time: String = "",
        val status: String = ""
    )
}