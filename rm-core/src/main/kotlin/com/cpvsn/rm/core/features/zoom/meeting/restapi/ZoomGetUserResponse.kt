package com.cpvsn.rm.core.features.zoom.meeting.restapi

data class ZoomGetUserResponse(
    val id: String?,
    val created_at: String?,
    val dept: String?,
    val email: String?,
    val first_name: String?,
    val last_client_version: String?,
    val last_login_time: String?,
    val last_name: String?,
    val pmi: Long?,
    val role_name: String?,
    val timezone: String?,
    val type: Int?,
    val use_pmi: Boolean?,
    val display_name: String?,
    val account_id: String?,
    val account_number: Int?,
    val cms_user_id: String?,
    val company: String?,
    val user_created_at: String?,
    val custom_attributes: List<CustomAttribute>?,
    val employee_unique_id: String?,
    val group_ids: List<String>?,
    val im_group_ids: List<String>?,
    val jid: String?,
    val job_title: String?,
    val cost_center: String?,
    val language: String?,
    val location: String?,
    val login_types: List<Int>?,
    val manager: String?,
    val personal_meeting_url: String?,
    val phone_country: String?,
    val phone_number: String?,
    val phone_numbers: List<PhoneNumber>?,
    val pic_url: String?,
    val plan_united_type: String?,
    val pronouns: String?,
    val pronouns_option: Int?,
    val role_id: String?,
    val status: String?,
    val cluster: String?,
    val zoom_one_type: Int?,
    val verified: Int?
) {
    data class CustomAttribute(
        val key: String?,
        val name: String?,
        val value: String?
    )

    data class PhoneNumber(
        val code: String?,
        val country: String?,
        val label: String?,
        val number: String?,
        val verified: Boolean?
    )
}
