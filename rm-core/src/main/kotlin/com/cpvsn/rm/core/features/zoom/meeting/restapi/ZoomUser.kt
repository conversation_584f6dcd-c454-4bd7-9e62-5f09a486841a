package com.cpvsn.rm.core.features.zoom.meeting.restapi

data class ZoomUser(
    val id: String = "",
    val first_name: String = "",
    val last_name: String = "",
    val display_name: String = "",
    val email: String = "",
    val type: Int = 0,
    val role_name: String = "",
    val pmi: Long = 0,
    val use_pmi: Boolean = false,
    val personal_meeting_url: String = "",
    val timezone: String = "",
    val verified: Int = 0,
    val dept: String = "",
    val created_at: String = "",
    val last_login_time: String = "",
    val pic_url: String = "",
    val cms_user_id: String = "",
    val jid: String = "",
    val group_ids: List<Any> = listOf(),
    val im_group_ids: List<Any> = listOf(),
    val account_id: String = "",
    val language: String = "",
    val phone_country: String = "",
    val phone_number: String = "",
    val status: String = "",
    val job_title: String = "",
    val location: String = "",
    val login_types: List<Int> = listOf(),
    val role_id: String = "",
    val account_number: Long = 0,
    val cluster: String = "",
    val user_created_at: String = ""
)