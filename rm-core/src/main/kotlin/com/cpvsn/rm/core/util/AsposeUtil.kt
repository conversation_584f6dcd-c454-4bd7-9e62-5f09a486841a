package com.cpvsn.rm.core.util

import com.aspose.words.Document
import com.aspose.words.FontSettings
import com.aspose.words.MemoryFontSource
import java.io.ByteArrayOutputStream

object AsposeUtil {

    fun word_to_pdf(word_bytes: ByteArray): ByteArray {
        val input_stream = word_bytes.inputStream()
        val output_stream = ByteArrayOutputStream()

        val font_file_names = listOf(
            "Arial.ttf",
            "Arial Bold.ttf",
            "Arial Bold Italic.ttf",
            "Arial Italic.ttf",
            "calibri.ttf",
            "calibri bold .ttf",
            "Calibri Bold Italic.ttf",
            "calibri italic.ttf",
            "Calibri Light Italic.TTF",
            "Calibri_Light.ttf",
            "SourceHanSansCN-VF.otf",
        )

        val font_sources = font_file_names.mapNotNull { file_name ->
            val resource_path = "templates/transcript/fonts/$file_name"

            this::class.java.classLoader.getResourceAsStream(resource_path)?.use { font_stream ->
                val font_bytes = font_stream.readBytes()
                MemoryFontSource(font_bytes)
            }
        }.toTypedArray()

        // 字体设置
        val font_settings = FontSettings()
        font_settings.setFontsSources(font_sources)

        input_stream.use { word_stream ->
            output_stream.use { pdf_stream ->
                val document = Document(word_stream)
                document.fontSettings = font_settings
                document.save(pdf_stream, com.aspose.words.SaveFormat.PDF)
            }
        }

        return output_stream.toByteArray()
    }
}