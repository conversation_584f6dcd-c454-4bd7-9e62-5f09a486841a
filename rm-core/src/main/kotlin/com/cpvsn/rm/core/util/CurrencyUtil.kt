package com.cpvsn.rm.core.util

import com.cpvsn.core.util.extension.BD
import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import java.math.BigDecimal

object CurrencyUtil {

    /**
     * 统一转换成USD 方便比较rate大小 (近似值)
     */
    fun to_usd_approximate(
        value: BigDecimal,
        currency: BuiltInCurrency
    ): BigDecimal {
        return when (currency) {
            BuiltInCurrency.USD -> value
            BuiltInCurrency.RMB -> value.div(6.5.BD)
            BuiltInCurrency.JPY -> value.multiply(0.0095.BD)
            BuiltInCurrency.GBP -> value.div(0.73.BD)
            BuiltInCurrency.EUR -> value.div(0.85.BD)
            BuiltInCurrency.SGD -> value.multiply(0.71.BD)
            BuiltInCurrency.MYR -> value.multiply(0.218409.BD)
        }
    }
}
