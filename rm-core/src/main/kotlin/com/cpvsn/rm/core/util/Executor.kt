package com.cpvsn.rm.core.util

import com.cpvsn.web.auth.async.DelegatingAuthContextExecutorService
import com.cpvsn.web.auth.async.DelegatingAuthContextScheduledExecutorService
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.ScheduledExecutorService

val global_executor: ExecutorService = DelegatingAuthContextExecutorService(
    Executors.newFixedThreadPool(2)!!
)

val global_scheduled_executor: ScheduledExecutorService = DelegatingAuthContextScheduledExecutorService(
    Executors.newScheduledThreadPool(1)
)
