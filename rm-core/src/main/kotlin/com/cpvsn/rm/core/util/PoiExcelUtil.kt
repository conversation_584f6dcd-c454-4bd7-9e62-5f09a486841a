package com.cpvsn.rm.core.util

import com.cpvsn.core.util.CoreJsonUtil
import org.apache.poi.openxml4j.opc.OPCPackage
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.xssf.usermodel.XSSFRow
import org.apache.poi.xssf.usermodel.XSSFSheet
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import java.io.File
import kotlin.reflect.KClass
import kotlin.reflect.KMutableProperty
import kotlin.reflect.full.createInstance
import kotlin.reflect.full.declaredMemberProperties
import kotlin.reflect.full.findAnnotation
import kotlin.reflect.full.hasAnnotation


/**
 * Suggestion: Use csv-formatted file instead while dealing with large size excel data.
 * @see ExcelColumn
 */
object PoiExcelUtil {

    private fun XSSFRow.getCellStringValue(cellnum: Int): String {
        val cell = this.getCell(cellnum) ?: return ""
        cell.cellType = CellType.STRING
        return cell.stringCellValue
    }

    fun getSheet(
            filePath: String,
            sheetIdx: Int,
    ): XSSFSheet {
        val file = File(filePath)
        val pkg = OPCPackage.open(file)
        val workbook = XSSFWorkbook(pkg)
        val sheet = workbook.getSheetAt(sheetIdx)
        pkg.close()
        return sheet
    }

    fun getSheets(workbook: XSSFWorkbook): List<XSSFSheet> {
        val sheets = mutableListOf<XSSFSheet>()
        var i = 0
        while (true) {
            try {
                sheets.add(workbook.getSheetAt(i))
            } catch (e: Exception) {
                break
            }
            i++
        }
        return sheets
    }

    @Suppress("DuplicatedCode")
    fun <T : Any> readModelsFromSheet(
            sheet: XSSFSheet,
            clazz: KClass<T>,
            natural_start_row: Int = 2,
            natural_end_row: Int? = null,
            enable_println: Boolean = false,
            force_keep_blank_row_nums: Set<Int> = emptySet(),
    ): List<T> {
        val column_map = clazz.declaredMemberProperties
            .filterIsInstance<KMutableProperty<*>>()
            .filter { it.hasAnnotation<ExcelColumn>() }
            .associateBy { it.findAnnotation<ExcelColumn>()!!.naturalIndex }
        val row_column = column_map[0]
        val commmon_columns = column_map.filterKeys { it > 0 }

        // range
        val last_row = natural_end_row ?: (sheet.lastRowNum + 1)
        val row_range = natural_start_row - 1 until last_row


        val list = mutableListOf<T>()
        var row: XSSFRow?
        var record: T?
        row_range.forEach { i ->
            row = sheet.getRow(i) ?: return@forEach

            // deal with blank
            if (i !in force_keep_blank_row_nums) {
                if (row!!.getCellStringValue(0).isBlank()) return@forEach
            }
            // assign row_num
            record = clazz.createInstance()
            row_column?.setter?.call(record, row!!.rowNum + 1)
            // assign fields
            commmon_columns.forEach { (column, prop) ->
                prop.setter.call(record, row!!.getCellStringValue(column - 1))
            }

            if (enable_println) {
                println("[Process] row $i : ${CoreJsonUtil.stringify(record)}")
            }
            list.add(record!!)
        }
        return list
    }

    fun readRows(
            sheet: XSSFSheet,
            natural_start_row: Int = 2,
            natural_end_row: Int? = null,
            force_keep_blank_row_nums: Set<Int> = emptySet(),
    ): List<List<String>> {
        val last_row = natural_end_row ?: (sheet.lastRowNum + 1)
        val row_range = natural_start_row - 1 until last_row
        val list = mutableListOf<List<String>>()
        row_range.forEach { i ->
            val row = sheet.getRow(i) ?: return@forEach
            if (i + 1 !in force_keep_blank_row_nums) {
                if (row.getCellStringValue(0).isBlank()) return@forEach
            }
            val c = row.lastCellNum
            val columns = (0 until c).map {
                row.getCellStringValue(it)
            }
            list.add(columns)
        }
        return list
    }

    fun <T : Any> writeModelsToWorkbook(
            clazz: KClass<T>,
            records: List<T>,
    ): XSSFWorkbook {
        val columnProps = clazz.declaredMemberProperties
            .filterIsInstance<KMutableProperty<*>>()
            .filter { it.hasAnnotation<ExcelColumn>() }
            .filter { it.findAnnotation<ExcelColumn>()!!.naturalIndex > 0 }
            .sortedBy { it.findAnnotation<ExcelColumn>()!!.naturalIndex }

        val workbook = XSSFWorkbook()
        val sheet = workbook.createSheet()

        // 第一行标题
        val header_values = columnProps.map {
            it.findAnnotation<ExcelColumn>()!!.description
                .takeIf { s -> s.isNotBlank() }
                ?: it.name
        }

        val header_row = sheet.createRow(0)
        header_values.forEachIndexed { idx, name ->
            header_row.createCell(idx).setCellValue(name)
        }

        // body
        records.forEachIndexed { idx, record ->
            val row = sheet.createRow(idx + 1)
            columnProps.forEachIndexed { columnIdx, prop ->
                row.createCell(columnIdx).setCellValue(prop.getter.call(record).toString())
            }
        }
        return workbook
    }

    fun <T : Any> writeModelsToExcel(
            clazz: KClass<T>,
            records: List<T>,
            filePath: String,
    ) {
        val workbook = writeModelsToWorkbook(clazz, records)
        try {
            val file = File(filePath)
            file.createNewFile()
            val outputStream = org.apache.commons.io.FileUtils.openOutputStream(file)
            workbook.write(outputStream)
            outputStream.close()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

}