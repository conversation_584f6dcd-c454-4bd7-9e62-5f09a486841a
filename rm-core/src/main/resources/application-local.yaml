server:
  port: 80

spring:
  profiles:
    active: ${ENV}
  rabbitmq:
    host: ***********
    port: 30674
    password: mq@2021!
    username: mq
  redis:
    host: ***********
    port: 31380
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    password: qa@2024!
    url: jdbc:mysql://*************:3306/us_db_qa?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
    username: qa

es:
  host: ***********
  port: 31210
  cluster_name: elasticsearch
  fingerprint: 17:9F:E7:E9:6F:8F:64:4F:63:B7:4B:B9:5D:87:CB:40:09:C2:8F:19:35:C2:06:4E:1F:FE:EB:CC:F1:01:3E:4C
  user_name: elastic
  password: ApMEKSgUvwOqJK6eZbN
logging:
  level:
    com.cpvsn.rm.core.experimental: debug
    com.cpvsn.rm.core.features: debug
    serviceStatsLog: warn
  path: /data/logs/rm_server

# ---------------------------------------------------------------------------------------------------------------------
oauth2-client:
  facebook:
    client-id: 602030593862496
    client-secret: fbacc45af4b5c524ad939221e1afdc95
    proxy:
      host: proxy.capvision.com
      port: 8080
  google:
    client-id: 68925922578-tf33991pd4lm86nvla1vcr36eg9ubk87.apps.googleusercontent.com
    client-secret: eGlG-nIqKVP0FJbph89425ru
    proxy:
      host: proxy.capvision.com
      port: 8080
  linkedin:
    client-id: 863ecc7ahyz9a5
    client-secret: ukwwisrJL8dfc5xH

auth:
  jwt:
    iss: us-db
    secret: JrvSR/LoyA9jzc7rE82wuWc6YPw7u/a34ctAAPbG3dQ=
# ---------------------------------------------------------------------------------------------------------------------
com:
  cpvsn:
    portal:
      advisorPreCallUrl: https://qa-udb2.capvision.com/portal/#/b
      advisorPostCallUrl: https://qa-udb2.capvision.com/portal/#/after-task
      contactPreCallUrl: https://qa-udb2.capvision.com/portal/#/pick
      contactPostCallUrl: https://qa-udb2.capvision.com/portal/#/afeedback
    conf:
      db_domain: https://qa-udb2.capvision.com
      locations_version: 4
      cors_origin: "*"
    es:
      indice:
        - index_enum: ADVISOR
          index_name: advisor
        - index_enum: PROJECT
          index_name: usdb_project
        - index_enum: COMPANY
          index_name: usdb_company
    data:
      teams:
        - leader: abarroukh
          members: anju,akeene,sparacionick,jbuen
        - leader: jluc
          members: swge,iscott,srogalski,mstockdale
    env:
      module: WEB


slack:
  enable: true
  apps:
    # test (workspace:Capvision Platform)
    - app_enum: EXPERT_ACTIVITY_BOT
      id: A03CDBR3LMQ
      slack_bot_token: ********************************************************
      slack_signing_secret: 053fcaaf2e6c947b9b8f64c8d7064246
      client_secret: 22df79cf754e6d6412efa8d2dc62d9d1

#slack:
#  apps:
#    # prod (workspace:Capvision)
#    - app_enum: EXPERT_ACTIVITY_BOT
#      id: A03DGNMM0QH
#      slack_bot_token: ********************************************************
#      slack_signing_secret: 17c2e5311b78f587194289ba24ff6106
#      client_secret: e816396f4ee01872f628856bee64fe6a
