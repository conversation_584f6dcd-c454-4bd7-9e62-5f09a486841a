<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cpvsn.rm.core.features.task.TaskMapper">
    <select id="task_count_by_client_and_capvision_compliance_status"
            resultType="com.cpvsn.rm.core.features.task.pojo.TaskComplianceStatusCountResult">
        SELECT t.client_compliance_status ,t.capvision_compliance_status ,count(*) FROM
        task t
        <if test="client_id != null">WHERE t.client_id = #{client_id}</if>
        group by t.client_compliance_status , t.capvision_compliance_status
    </select>
</mapper>
