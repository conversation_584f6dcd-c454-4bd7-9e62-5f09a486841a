package com.cpvsn.rm.core.features.advisor

import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.misc.company.Company
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class AdvisorUtilTest {

    @Nested
    inner class handle_date_test {
        @Test
        @DisplayName("basic case")
        fun test100() {
            val res = AdvisorUtil.handle_date("2015-01")
            assertThat(res).isEqualTo("Jan 2015")
        }
    }

    @Nested
    inner class compute_auto_bg_test {
        @Test
        @DisplayName("basic case")
        fun test100() {
            val advisor = Advisor().apply {
                firstname = "foo"
                lastname = "bar"
            }
            val jobs = listOf(
                    AdvisorJob().apply {
                        position = "software engineer"
                        is_current = true
                        start_date = "2019-01"
                        company = Company().apply {
                            id = 4
                            name = "capvision"
                        }
                    },
                    Advisor<PERSON>ob().apply {
                        position = "software engineer"
                        start_date = "2016-01"
                        end_date = "2019-01"
                        company = Company().apply {
                            id = 3
                            name = "bar"
                        }
                    },
                    AdvisorJob().apply {
                        position = "operator"
                        start_date = "2015-01"
                        end_date = "2016-01"
                        company = Company().apply {
                            id = 2
                            name = "foo"
                        }
                    }
            )
            val res = AdvisorUtil.compute_auto_bg(advisor, jobs)
//            println(res)
            val expected = """
                |Since Jan 2019, this advisor is the software engineer at capvision. 
                |From Jan 2016 to Jan 2019, this advisor was the software engineer at bar. 
                |From Jan 2015 to Jan 2016, this advisor was the operator at foo. 
            """.trimIndent().trimMargin().replace("\n", "")
//            println(expected)
            assertThat(res).isEqualTo(expected)
        }

        @Test
        @DisplayName("job in the same company should have specific text")
        fun test2() {
            val advisor = Advisor().apply {
                firstname = "foo"
                lastname = "bar"
            }
            val jobs = listOf(
                    AdvisorJob().apply {
                        position = "KM"
                        start_date = "2019-01"
                        end_date = "2020-01"
                        company = Company().apply {
                            id = 4
                            name = "capvision"
                        }
                    },
                    AdvisorJob().apply {
                        position = "KA"
                        start_date = "2018-01"
                        end_date = "2019-01"
                        company = Company().apply {
                            id = 4
                            name = "capvision"
                        }
                    }
            )
            val res = AdvisorUtil.compute_auto_bg(advisor, jobs)
//            println(res)
            val expected = """
                |From Jan 2018 to Jan 2020, the advisor held numerous positions at capvision -- 
                |KM Jan 2019 - Jan 2020; 
                |KA Jan 2018 - Jan 2019
            """.trimIndent().trimMargin().replace("\n", "")
//            println(expected)
            assertThat(res).isEqualTo(expected)
        }
    }
}
