package com.cpvsn.rm.core.features.advisor.bank_account

import com.cpvsn.core.util.delegates.AesDelegates
import com.cpvsn.core.util.delegates.JsonDelegates
import com.cpvsn.core.util.security.aes.AesCbcBase64
import com.cpvsn.rm.core.features.finance.advisor_payment.local.LocalAdvisorPayment
import com.cpvsn.rm.core.testbase.DbTest
import com.cpvsn.rm.core.util.JacksonUtil
import org.junit.jupiter.api.*
import org.springframework.test.annotation.Commit


class AdvisorBankAccountTest {
    @Nested
    inner class encrypt {
        @BeforeEach
        fun setUp() {
            BankAccountUtils.aesDelegates = AesDelegates.instance(
                AesCbcBase64.Companion.instance(
                    secretKeyBase64 = "VewPBCAZ1DFx1m5BxAHxeA=="
                )
            )
        }

        @Test
        @DisplayName("stringify")
        fun test100() {
            val account = AdvisorBankAccount {
                account2 = BankAccountV1()
            }
            println(account.to_json(true))
        }

        @Test
        @DisplayName("parse")
        fun test200() {
            val json = """{
                  "account2" : {
                    "bank_name" : "whatever",
                    "account_name" : "whatever",
                    "account_number" : "whatever"
                  }
                }
            """.trimIndent()
            val account = JsonDelegates.parse<AdvisorBankAccount>(json)
            println(account.account2)
        }
    }

}
