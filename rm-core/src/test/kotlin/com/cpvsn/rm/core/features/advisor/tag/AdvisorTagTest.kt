package com.cpvsn.rm.core.features.advisor.tag

import com.cpvsn.rm.core.testbase.DbTest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant

class AdvisorTagTest {

    @Nested
    inner class evaluating_test {
        @Test
        @DisplayName("should be false if start_at is null")
        fun test100() {
            val tag = AdvisorTag {
                project_sq_evaluate_start_at = null
            }
            assertThat(tag.project_sq_evaluating).isFalse()
        }

        @Test
        @DisplayName("should be true if end_at < start_at and timeout not exceeds")
        fun test200() {
            val now = Instant.now()
            val start = now.minusSeconds(AdvisorTag.EVALUATE_TIME_OUT.toLong())
            val end = start.minusSeconds(1)
            val tag = AdvisorTag {
                project_sq_evaluate_start_at = start
                project_sq_evaluate_end_at = end
            }
            assertThat(tag.project_sq_evaluating).isTrue()
        }

        @Test
        @DisplayName("should be false if end_at < start_at and timeout exceeds")
        fun test210() {
            val now = Instant.now()
            val start = now
                .minusSeconds(AdvisorTag.EVALUATE_TIME_OUT.toLong() + 1)
            val end = start.minusSeconds(1)
            val tag = AdvisorTag {
                project_sq_evaluate_start_at = start
                project_sq_evaluate_end_at = end
            }
            assertThat(tag.project_sq_evaluating).isFalse()
        }

        @Test
        @DisplayName("should be true if end_at is null and timeout not exceeds")
        fun test300() {
            val now = Instant.now()
            val start = now.minusSeconds(AdvisorTag.EVALUATE_TIME_OUT.toLong())
            val tag = AdvisorTag {
                project_sq_evaluate_start_at = start
                project_sq_evaluate_end_at = null
            }
            assertThat(tag.project_sq_evaluating).isTrue()
        }

        @Test
        @DisplayName("should be true if end_at is null and timeout exceeds")
        fun test310() {
            val now = Instant.now()
            val start = now.minusSeconds(AdvisorTag.EVALUATE_TIME_OUT.toLong() + 1)
            val tag = AdvisorTag {
                project_sq_evaluate_start_at = start
                project_sq_evaluate_end_at = null
            }
            assertThat(tag.project_sq_evaluating).isFalse()
        }

        @Test
        @DisplayName("should be false if end_at > start_at")
        fun test400() {
            val now = Instant.now()
            val end = now.minusSeconds(2)
            val start = now.minusSeconds(1)
            val tag = AdvisorTag {
                project_sq_evaluate_start_at = start
                project_sq_evaluate_end_at = end
            }
            assertThat(tag.project_sq_evaluating).isTrue()
        }

    }

    @DbTest
    @Nested
    inner class evaluating_db_test {
        @Test
        @DisplayName("smoke test")
        fun test100() {
            AdvisorTag.findAll(
                AdvisorTag.Query(
                    project_sq_stale = true,
                    project_sq_evaluating = true,
                    job_exp_evaluating = true,
                    job_exp_stale = true,
                )
            )
        }

    }

    @DbTest
    @Nested
    inner class dbTest {
        @Test
        fun test100() {
            val res = AdvisorTag.save(
                listOf(
                    AdvisorTag {
                        name = "foo"
                    },
                    AdvisorTag {
                        name = "foo"
                    },
                )
            )
            println(res)
        }

        /**
         * this test past as we have added a rand_col to advisor tag.
         */
        @Test
        fun test200() {
            val list = listOf(
                AdvisorTag {
                    // get id = 969
                    name = "WeWork Inc"
                },
                AdvisorTag {
                    // get id = 0
                    name = "WeWork Inc"
                },
            )
            AdvisorTag.save(list[0])
            AdvisorTag.save(list[1])
            println(list[0].id)
            println(list[1].id)
            assertThat(list[0].id).isGreaterThan(0)
            assertThat(list[1].id).isGreaterThan(0)
        }
    }
}
