package com.cpvsn.rm.core.features.compliance

import com.cpvsn.rm.core.features.compliance.tc.Tc
import com.cpvsn.rm.core.testbase.LegacyDbTest
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class TcMapperTest : LegacyDbTest() {
    @Test
    @DisplayName("save")
    fun test100() {
        val model = Tc().apply {
            locale_template_id_map = mapOf("foo" to 1)
        }
        Tc.save(model)
    }

    @Test
    @DisplayName("fetch")
    fun test200() {
        val map = mapOf("foo" to 1)
        val model = Tc().apply {
            locale_template_id_map = map
        }
        Tc.save(model)
        val res = Tc.get(model.id)
        Assertions.assertThat(res.locale_template_id_map).isEqualTo(map)
    }

}
