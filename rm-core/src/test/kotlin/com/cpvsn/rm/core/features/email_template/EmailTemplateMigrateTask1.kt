package com.cpvsn.rm.core.features.email_template

import com.cpvsn.rm.core.features.email_template.constant.EmailTemplateBuiltInTag
import com.cpvsn.rm.core.features.email_template.constant.EmailTemplatePlaceholder
import com.cpvsn.rm.core.testbase.LegacyDbTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test

class EmailTemplateMigrateTask1 : LegacyDbTest() {

    @DisplayName("migrate script")
    @Disabled
//    @Test
//    @Commit
    fun test100() {
        val templates = EmailTemplate.findAll()

        templates.forEach {
            // update_template(it)
            it.update()
        }
    }

    fun EmailTemplatePlaceholder.wrap(): String {
        return "{$name}"
    }

    //
//    private fun update_template(template: EmailTemplate) {
//        with(template) {
//            when (template.content_type) {
//                EmailContentType.PROJECT_OUTREACH -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.ADVISOR_EMAIL.wrap()
//                }
//                EmailContentType.PORTAL_ADVISOR_PRE_CALL -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.ADVISOR_EMAIL.wrap()
//                }
//                EmailContentType.PORTAL_ADVISOR_POST_CALL -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.ADVISOR_EMAIL.wrap()
//                }
//
//                EmailContentType.PORTAL_CONTACT_PRE_CALL -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.PROJECT_COMMON_CONTACT_EMAILS.wrap()
//                }
//                EmailContentType.PORTAL_CONTACT_POST_CALL -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.TASK_CONTACT_EMAIL.wrap()
//                }
//                EmailContentType.PORTAL_COMPLIANCE -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.PROJECT_CLIENT_COMPLIANCE_OFFICERS_EMAILS.wrap()
//                    cc_template = EmailTemplatePlaceholder.CAPVISION_LEGAL_EMAIL_GROUP.wrap()
//                }
//
//                EmailContentType.TASK_CLIENT_SCHEDULED_TO_ADVISOR -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.ADVISOR_EMAIL.wrap()
//                    cc_template = EmailTemplatePlaceholder.PROJECT_MEMBER_EMAILS.wrap()
//                }
//                EmailContentType.CALENDAR_TASK_ARRANGE_ADVISOR -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.ADVISOR_EMAIL.wrap()
//                    cc_template = EmailTemplatePlaceholder.PROJECT_MEMBER_EMAILS.wrap()
//                }
//                EmailContentType.CALENDAR_TASK_ARRANGE_CONTACT -> {
//                    from_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.ADVISOR_EMAIL.wrap()
//                    cc_template = EmailTemplatePlaceholder.PROJECT_MEMBER_EMAILS.wrap()
//                }
//
//                EmailContentType.NOTIFY_KM_TC_ACCEPTED,
//                EmailContentType.NOTIFY_KM_TC_SQ_RESPONDED,
//                EmailContentType.NOTIFY_KM_SQ_RESPONDED,
//                EmailContentType.NOTIFY_KM_CA_AG_RESPONDED,
//                EmailContentType.NOTIFY_CONTACT_APPROVED,
//                EmailContentType.NOTIFY_CONTACT_REJECTED,
//                EmailContentType.NOTIFY_CONTACT_AG_MATCHED,
//                EmailContentType.NOTIFY_CONTACT_AG_NOT_MATCHED -> {
//                    from_template = EmailTemplatePlaceholder.DB_SENDER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.RECRUITER_EMAIL.wrap()
//                    cc_template = EmailTemplatePlaceholder.PROJECT_MEMBER_EMAILS.wrap()
//                }
//                EmailContentType.NOTIFY_KM_CONTACT_FEEDBACK_RESPONDED -> {
//                    from_template = EmailTemplatePlaceholder.DB_SENDER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.PROJECT_MEMBER_EMAILS.wrap()
//                }
//
//                EmailContentType.NOTIFY_KM_CAP_LEGAL_APPROVED,
//                EmailContentType.NOTIFY_KM_CAP_LEGAL_DIRECT_APPROVED,
//                EmailContentType.NOTIFY_KM_CAP_LEGAL_REJECTED -> {
//                    from_template = EmailTemplatePlaceholder.DB_SENDER_EMAIL.wrap()
//                    to_template = EmailTemplatePlaceholder.PROJECT_MANAGER_EMAIL.wrap()
//                    cc_template = EmailTemplatePlaceholder.PROJECT_MEMBER_EMAILS.wrap()
//                }
//
//                EmailContentType.NOTIFY_CLIENT_CLIENT_LEGAL_APPROVED,
//                EmailContentType.NOTIFY_CLIENT_CLIENT_LEGAL_REJECTED -> {
//                    from_template = EmailTemplatePlaceholder.DB_SENDER_EMAIL.wrap()
//                    to_template = listOfNotNull(
//                            EmailTemplatePlaceholder.PROJECT_CLIENT_COMPLIANCE_OFFICERS_EMAILS.wrap(),
//                            EmailTemplatePlaceholder.CAPVISION_LEGAL_EMAIL_GROUP.wrap()
//                    ).joinToString(",")
//                    cc_template = EmailTemplatePlaceholder.PROJECT_MEMBER_EMAILS.wrap()
//                }
//                else -> {
//                }
//            }
//        }
//    }

    @Test
    @DisplayName("remove html a tag")
    @Disabled
//    @Commit
    fun test200() {
        val templates = EmailTemplate.findAll(EmailTemplate.Query())
                .filter { it.tags.orEmpty().contains(EmailTemplateBuiltInTag.EXTERNAL_USER_INVOLVED.name) }

        templates.forEach { template ->
            template.content_template = template.content_template.replace("""<a.*?>(.*?)</a>""".toRegex()) {
                val matched = it.value
                val res = it.groupValues[1]
                println("matched: $matched")
                println("res: $res")
                res
            }
            template.update()
        }
    }

}
