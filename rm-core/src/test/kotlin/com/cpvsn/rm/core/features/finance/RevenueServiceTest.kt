package com.cpvsn.rm.core.features.finance

import com.cpvsn.rm.core.features.finance.cronjob.LockRevenueCronjob
import com.cpvsn.rm.core.features.finance.revenue.Revenue

import com.cpvsn.rm.core.features.finance.revenue.RevenueService
import com.cpvsn.rm.core.testbase.ServiceTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired


class RevenueServiceTest : ServiceTest() {
    @Autowired
    private lateinit var revenueService: RevenueService
    @Autowired
    private lateinit var lockRevenueCronjob: LockRevenueCronjob

    @Nested
    inner class BillingJob {
        @Test
        fun test100() {
            revenueService.invoice_billing_job()
        }
    }

    @Test
    fun test_list() {
        val unbound_revenues = Revenue.findAll(Revenue.Query(
                create_type_in = setOf(Revenue.CreateType.UNRESOLVED, Revenue.CreateType.OVERFLOW)
        )).sortedBy { it.revenue_time }
        println()
    }

    @Test
    @Disabled
    fun test_calculate_task() {
        revenueService.calculate_task(389)
        println()
    }

    @Test
    fun test_lock_cronjob() {
        lockRevenueCronjob.cut_off_revenue_monthly()
    }

}
