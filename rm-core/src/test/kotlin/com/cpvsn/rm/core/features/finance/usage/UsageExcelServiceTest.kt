package com.cpvsn.rm.core.features.finance.usage

import com.cpvsn.rm.core.extensions.write_to_file
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.template.FileTemplateService
import com.cpvsn.rm.core.testbase.DbTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Import
import java.time.ZoneId

@DbTest
@Import(UsageExcelService::class, FileTemplateService::class)
class UsageExcelServiceTest {

    @Autowired
    private lateinit var usageExcelService: UsageExcelService

    @Test
    fun test100() {
        val file = usageExcelService.generate_usage_excel(
            Revenue.Query(
                ids = setOf(
                    18493, 18494, 18492, 18491, 18498, 19352
                )
            ),
            columns = UsageExcelModel.AvailableFields.values().toList()
        )
        file.content.write_to_file("build/${file.file_name}")
    }

    @Test
    @DisplayName("generate excel with specified columns")
    fun test200() {
        val file = usageExcelService.generate_usage_excel(
            Revenue.Query(
                ids = setOf(
                    18493, 18494, 18492, 18491, 18498
                )
            ),

            listOf(
                UsageExcelModel.AvailableFields.ADVISOR_POSITION,
                UsageExcelModel.AvailableFields.ADVISOR_COMPANY,
                UsageExcelModel.AvailableFields.REVENUE_TIME,
            )
        )
        file.content.write_to_file("out/${file.file_name}")
    }

    @Test
    fun test300() {
        val res = usageExcelService.generate_usage_excel(
            Revenue.Query(invoice_id = 62),
        )
        println(res.file_name)
        res.content.write_to_file("out/${res.file_name}")
    }

    @Nested
    inner class zone_id_test {
        @Test
        fun test100() {
            val NewYork = ZoneId.of("America/New_York")
            val Shanghai = ZoneId.of("Asia/Shanghai")
            val file1 = usageExcelService.generate_usage_excel(
                Revenue.Query(invoice_id = 62),
                columns = UsageExcelModel.AvailableFields.values().toList(),
                zoneId = NewYork,
            )
            val file2 = usageExcelService.generate_usage_excel(
                Revenue.Query(invoice_id = 62),
                columns = UsageExcelModel.AvailableFields.values().toList(),
                zoneId = Shanghai,
            )
            file1.content.write_to_file("build/new_york.xlsx")
            file2.content.write_to_file("build/shanghai.xlsx")
        }
    }

    @Nested
    @Disabled
    inner class group_revenues_test {
        @Test
        fun test100() {
            val file = usageExcelService.generate_usage_excel(
                Revenue.Query(
                    ids = setOf(
                        2932, 2936,            // task_id = 7
                        2949,                   // task_id = 78
                    )
                ),
                columns = UsageExcelModel.AvailableFields.values().toList(),
            )
            file.content.write_to_file("build/${file.file_name}")
        }
    }

}
