package com.cpvsn.rm.core.features.misc.company

import com.cpvsn.rm.core.testbase.DbTest
import org.apache.commons.lang3.StringUtils
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class CompanyNameUtilsTest {
    val samples = listOf(
        "Apple Inc",
        "Bank of America Corp. (North Carolina National Bank)",
        "Taiwan Semiconductor Manufacturing Co Ltd ADR",
        "StoneCo Ltd",
        "Rogers Communications Inc. Class B (NYSE)",
        "Ginkgo Bioworks Holdings, Inc",
        "Coca Cola Co.",
        "KKR & Co. Inc",
        "Alibaba Group Holding Ltd ADR",
    )

    @Test
    fun test100() {
        val res = samples.associateWith {
            CompanyNameUtils.extract_keyword_from_stock_name(it)
        }
        res.forEach { (k, v) ->
            println("|$k| -> |$v|")
        }
    }

    @Nested
    @DbTest
    inner class nameTest {
        @Test
        fun test100() {
            val names = ThirdPartyCompany.findAll().map { it.name }
            val res = names.associateWith {
                CompanyNameUtils.extract_keyword_from_stock_name(it)
            }
            res.forEach { (k, v) ->
                val diff = StringUtils.difference(v, k)
                println("|$k| -> |$v| diff=|$diff|")
            }
        }
    }
}
