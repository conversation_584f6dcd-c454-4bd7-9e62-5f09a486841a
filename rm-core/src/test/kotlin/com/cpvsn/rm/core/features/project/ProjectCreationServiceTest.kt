package com.cpvsn.rm.core.features.project

import com.cpvsn.rm.core.config.googlesdk.GoogleSdkAutoConfiguration
import com.cpvsn.rm.core.features.core.app_config.AppConfigEntryService
import com.cpvsn.rm.core.testbase.DbTest
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.ImportAutoConfiguration
import org.springframework.context.annotation.Import

@DbTest
@ImportAutoConfiguration(GoogleSdkAutoConfiguration::class)
@Import(ProjectCreationService::class, AppConfigEntryService::class)
class ProjectCreationServiceTest {

    @Autowired
    private lateinit var projectCreationService: ProjectCreationService

    @Test
    @Disabled
    @DisplayName("test create document")
    fun test100() {
        val project = Project.get(72)
        val res = projectCreationService.create_google_sheet(project)
        println(res)
    }
}
