package com.cpvsn.rm.core.features.report

import com.cpvsn.rm.core.testbase.ServiceTest
import kotlinx.coroutines.GlobalScope
import kotlinx.coroutines.async
import kotlinx.coroutines.delay
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.slf4j.LoggerFactory
import java.time.Instant
import java.time.ZoneId
import java.time.format.DateTimeFormatter

class CoroutineTest : ServiceTest() {
    private val logger = LoggerFactory.getLogger(this::class.java)

    /*
    2024-04-26 09:37:12.993  INFO 60031 --- [er @coroutine#1] c.c.r.c.features.report.CoroutineTest    : start: 2024-04-26 09:37:12.993

    2024-04-26 09:37:13.004  INFO 60031 --- [-2 @coroutine#3] c.c.r.c.features.report.CoroutineTest    : 2 start: 2024-04-26 09:37:13.003
    2024-04-26 09:37:13.003  INFO 60031 --- [-1 @coroutine#2] c.c.r.c.features.report.CoroutineTest    : 1 start: 2024-04-26 09:37:13.002
    2024-04-26 09:37:13.006  INFO 60031 --- [-3 @coroutine#4] c.c.r.c.features.report.CoroutineTest    : 3 start: 2024-04-26 09:37:13.006
    2024-04-26 09:37:13.006  INFO 60031 --- [-4 @coroutine#5] c.c.r.c.features.report.CoroutineTest    : 4 start: 2024-04-26 09:37:13.006

    2024-04-26 09:37:15.014  INFO 60031 --- [-1 @coroutine#3] c.c.r.c.features.report.CoroutineTest    : 2 end: 2024-04-26 09:37:15.014
    2024-04-26 09:37:15.514  INFO 60031 --- [-1 @coroutine#5] c.c.r.c.features.report.CoroutineTest    : 4 end: 2024-04-26 09:37:15.514
    2024-04-26 09:37:16.015  INFO 60031 --- [-1 @coroutine#2] c.c.r.c.features.report.CoroutineTest    : 1 end: 2024-04-26 09:37:16.015
    2024-04-26 09:37:17.014  INFO 60031 --- [-1 @coroutine#4] c.c.r.c.features.report.CoroutineTest    : 3 end: 2024-04-26 09:37:17.013
    Query 1 result: Result from query 1
    Query 2 result: Result from query 2
    Query 3 result: Result from query 3
    Query 4 result: Result from query 4
    2024-04-26 09:37:17.022  INFO 60031 --- [er @coroutine#1] c.c.r.c.features.report.CoroutineTest    : end: 2024-04-26 09:37:17.020
     */
    @Test
    fun test01() {
        runBlocking {
            query_all()
        }
        println()
    }

    private fun current_ts(): String {
        return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS")
            .withZone(ZoneId.systemDefault())
            .format(Instant.now())
    }

    suspend fun query_all() {
        logger.info("start: ${current_ts()}")
        val result1 = GlobalScope.async { query1() }
        val result2 = GlobalScope.async { query2() }
        val result3 = GlobalScope.async { query3() }
        val result4 = GlobalScope.async { query4() }

        // 等待所有查询完成
        val finalResult1 = result1.await()
        val finalResult2 = result2.await()
        val finalResult3 = result3.await()
        val finalResult4 = result4.await()

        // 现在你可以处理所有查询的结果了
        println("Query 1 result: $finalResult1")
        println("Query 2 result: $finalResult2")
        println("Query 3 result: $finalResult3")
        println("Query 4 result: $finalResult4")
        logger.info("end: ${current_ts()}")
    }

    suspend fun query1(): String {
        // 模拟查询1
        logger.info("1 start: ${current_ts()}")
        delay(3000) // 休眠 3 秒，模拟耗时操作
        logger.info("1 end: ${current_ts()}")
        return "Result from query 1"
    }

    suspend fun query2(): String {
        // 模拟查询2
        logger.info("2 start: ${current_ts()}")
        delay(2000) // 休眠 2 秒，模拟耗时操作
        logger.info("2 end: ${current_ts()}")
        return "Result from query 2"
    }

    suspend fun query3(): String {
        // 模拟查询3
        logger.info("3 start: ${current_ts()}")
        delay(4000) // 休眠 4 秒，模拟耗时操作
        logger.info("3 end: ${current_ts()}")
        return "Result from query 3"
    }

    suspend fun query4(): String {
        // 模拟查询4
        logger.info("4 start: ${current_ts()}")
        delay(2500) // 休眠 2.5 秒，模拟耗时操作
        logger.info("4 end: ${current_ts()}")
        return "Result from query 4"
    }
}