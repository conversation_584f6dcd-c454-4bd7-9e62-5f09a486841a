package com.cpvsn.rm.core.features.report

import com.cpvsn.poiext.excel.extWrite
import com.cpvsn.rm.core.testbase.DbTest
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Import
import java.time.LocalDate

@DbTest
@Import(SurveyTaskReportService::class)
class SurveyTaskReportServiceTest {

    @Autowired
    private lateinit var surveyTaskReportService: SurveyTaskReportService

    @Test
    fun test100() {
        val s = LocalDate.of(2023, 11, 1)
        val e = LocalDate.of(2023, 12, 1)
        val res = surveyTaskReportService
            .getData(s, e)
        println(res)
    }

    @Test
    fun test200() {
        val s = LocalDate.of(2023, 9, 1)
        val e = LocalDate.of(2024, 1, 1)
        val workbook = surveyTaskReportService.workbook(
            s, e
        )
        workbook.extWrite("data($s-${e.minusDays(1)}).xlsx")
    }
}
