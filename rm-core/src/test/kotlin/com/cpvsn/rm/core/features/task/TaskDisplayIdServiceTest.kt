package com.cpvsn.rm.core.features.task

import com.cpvsn.rm.core.features.task.angle.TaskAngle
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class TaskDisplayIdServiceTest {
    private val service = TaskDisplayIdService()

    @Nested
    inner class reset_order_test {
        @Test
        @DisplayName("should not change rank if task doesn't allow to change")
        fun test100() {
            val tasks = listOf(
                    Task { id = 1; rank = 2; general_status = TaskStatus.General.RECOMMENDED },
            )
            val angle = TaskAngle {
                this.tasks = tasks
            }
            service.reorder_task_rank(angle)
            println(angle.tasks?.map { it.rank })
            assertThat(tasks[0].rank).isEqualTo(2)
        }

        @Test
        @DisplayName("should change rank if task allow to change")
        fun test200() {
            val tasks = listOf(
                    Task { id = 1; rank = 2; general_status = TaskStatus.General.INITIAL },
            )
            val angle = TaskAngle {
                this.tasks = tasks
            }
            service.reorder_task_rank(angle)
            println(angle.tasks?.map { it.rank })
            assertThat(tasks[0].rank).isEqualTo(1)
        }

        @Test
        @DisplayName("simple mix case 1")
        fun test300() {
            val tasks = listOf(
                    Task { id = 1; rank = 1; general_status = TaskStatus.General.INITIAL },
                    Task { id = 2; rank = 3; general_status = TaskStatus.General.RECOMMENDED },
                    Task { id = 3; rank = 4; general_status = TaskStatus.General.INITIAL },
            )
            val angle = TaskAngle {
                this.tasks = tasks
            }
            service.reorder_task_rank(angle)
            println(angle.tasks?.map { it.rank })
            assertThat(tasks[0].rank).isEqualTo(1)
            assertThat(tasks[1].rank).isEqualTo(3)
            assertThat(tasks[2].rank).isEqualTo(2)
        }

        @Test
        @DisplayName("simple mix case 2")
        fun test400() {
            val tasks = listOf(
                    Task { id = 1; rank = 1; general_status = TaskStatus.General.INITIAL },
                    Task { id = 2; rank = 2; general_status = TaskStatus.General.RECOMMENDED },
                    Task { id = 3; rank = 4; general_status = TaskStatus.General.INITIAL },
            )
            val angle = TaskAngle {
                this.tasks = tasks
            }
            service.reorder_task_rank(angle)
            println(angle.tasks?.map { it.rank })
            assertThat(tasks[0].rank).isEqualTo(1)
            assertThat(tasks[1].rank).isEqualTo(2)
            assertThat(tasks[2].rank).isEqualTo(3)
        }

        @Test
        @DisplayName("mix case 3")
        fun test500() {
            val tasks = listOf(
                    Task { id = 1; rank = 10; general_status = TaskStatus.General.INITIAL },
                    Task { id = 2; rank = 20; general_status = TaskStatus.General.RECOMMENDED },
                    Task { id = 3; rank = 30; general_status = TaskStatus.General.RECOMMENDED },
                    Task { id = 4; rank = 40; general_status = TaskStatus.General.INITIAL },
                    Task { id = 5; rank = 50; general_status = TaskStatus.General.INITIAL },
                    Task { id = 6; rank = 60; general_status = TaskStatus.General.RECOMMENDED },
                    Task { id = 7; rank = 70; general_status = TaskStatus.General.RECOMMENDED },
                    Task { id = 8; rank = 80; general_status = TaskStatus.General.INITIAL },
                    Task { id = 9; rank = 90; general_status = TaskStatus.General.INITIAL },
            )
            val angle = TaskAngle {
                this.tasks = tasks
            }
            service.reorder_task_rank(angle)
            println(angle.tasks?.map { it.rank })
            assertThat(tasks[0].rank).isEqualTo(1)
            assertThat(tasks[1].rank).isEqualTo(20)
            assertThat(tasks[2].rank).isEqualTo(30)
            assertThat(tasks[3].rank).isEqualTo(2)
            assertThat(tasks[4].rank).isEqualTo(3)
            assertThat(tasks[5].rank).isEqualTo(60)
            assertThat(tasks[6].rank).isEqualTo(70)
            assertThat(tasks[7].rank).isEqualTo(4)
            assertThat(tasks[8].rank).isEqualTo(5)
        }
    }

    @Nested
    inner class sort_test{
        @Test
        @DisplayName("insert test")
        fun test300() {
            val tasks = listOf(
                    Task { id = 1; rank = 1; general_status = TaskStatus.General.INITIAL },
                    Task { id = 2; rank = 2; general_status = TaskStatus.General.INITIAL },
                    Task { id = 3; rank = 3; general_status = TaskStatus.General.INITIAL },
            )
            val angle = TaskAngle {
                this.tasks = tasks
            }
            service.reorder_task_rank(angle, mapOf(3 to 1.5))
            println(angle.tasks?.map { it.rank })
            assertThat(tasks[0].rank).isEqualTo(1)
            assertThat(tasks[1].rank).isEqualTo(3)
            assertThat(tasks[2].rank).isEqualTo(2)
        }

        @Test
        @DisplayName("move to last test")
        fun test400() {
            val tasks = listOf(
                    Task { id = 1; rank = 1; general_status = TaskStatus.General.INITIAL },
                    Task { id = 2; rank = 2; general_status = TaskStatus.General.INITIAL },
                    Task { id = 3; rank = 3; general_status = TaskStatus.General.INITIAL },
            )
            val angle = TaskAngle {
                this.tasks = tasks
            }
            service.reorder_task_rank(angle, mapOf(1 to 3.5))
            println(angle.tasks?.map { it.rank })
            assertThat(tasks[0].rank).isEqualTo(3)
            assertThat(tasks[1].rank).isEqualTo(1)
            assertThat(tasks[2].rank).isEqualTo(2)
        }
    }
}
