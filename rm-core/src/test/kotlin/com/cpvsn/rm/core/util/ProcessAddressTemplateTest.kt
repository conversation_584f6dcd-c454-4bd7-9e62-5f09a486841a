package com.cpvsn.rm.core.util

import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.email_template.EmailTemplateEngine
import com.cpvsn.rm.core.features.email_template.constant.EmailAddressPlaceHolder
import com.cpvsn.rm.core.features.email_template.models.PlaceholderBasedModel
import com.cpvsn.rm.core.features.user.User
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class ProcessAddressTemplateTest {
    @Nested
    inner class SplitterTest {
        @Test
        @DisplayName("split by ,")
        fun test100() {
            val template = "<EMAIL>,<EMAIL>"
            val model = PlaceholderBasedModel()
            val res = EmailTemplateEngine.process_address_template(template, model)
                    .map { it.email }
            Assertions.assertThat(res).containsExactly("<EMAIL>", "<EMAIL>")
        }

        @Test
        @DisplayName("split by ;")
        fun test200() {
            val template = "<EMAIL>;<EMAIL>"
            val model = PlaceholderBasedModel()
            val res = EmailTemplateEngine.process_address_template(template, model)
                    .map { it.email }
            Assertions.assertThat(res).containsExactly("<EMAIL>", "<EMAIL>")
        }

        @Test
        @DisplayName("should trim blank")
        fun test300() {
            val template = "   <EMAIL>  ;  <EMAIL>   "
            val model = PlaceholderBasedModel()
            val res = EmailTemplateEngine.process_address_template(template, model)
                    .map { it.email }
            Assertions.assertThat(res).containsExactly("<EMAIL>", "<EMAIL>")
        }
    }

    @Test
    @DisplayName("empty template should return empty list")
    fun test100() {
        val res = EmailTemplateEngine.process_address_template("", PlaceholderBasedModel())
        Assertions.assertThat(res).isEmpty()
    }

    @Test
    @DisplayName("basic case: should reolve email address")
    fun test200() {
        val template = "{{${EmailAddressPlaceHolder.ADVISOR_EMAIL}}}"
        val model = PlaceholderBasedModel(
                advisor = Advisor {
                    email = "<EMAIL>"
                }
        )
        val res = EmailTemplateEngine.process_address_template(template, model)
                .map { it.email }
        Assertions.assertThat(res).containsExactly("<EMAIL>")
    }

    @Test
    @DisplayName("should resolve multiple email address")
    fun test300() {
        val template = "{{${EmailAddressPlaceHolder.ADVISOR_EMAIL}}}, {{${EmailAddressPlaceHolder.RECRUITER_EMAIL}}}"
        val model = PlaceholderBasedModel(
                advisor = Advisor {
                    email = "<EMAIL>"
                },
                user = User {
                    email = "<EMAIL>"
                }
        )
        val res = EmailTemplateEngine.process_address_template(template, model)
                .map { it.email }
        Assertions.assertThat(res).containsExactly("<EMAIL>", "<EMAIL>")
    }

    @Test
    @DisplayName("should keep raw string")
    fun test400() {
        val template = "<EMAIL>, <EMAIL>"
        val model = PlaceholderBasedModel()
        val res = EmailTemplateEngine.process_address_template(template, model)
                .map { it.email }
        Assertions.assertThat(res).containsExactly("<EMAIL>", "<EMAIL>")
    }

    @Test
    @DisplayName("mix case")
    fun test500() {
        val template = "{{${EmailAddressPlaceHolder.ADVISOR_EMAIL}}}, <EMAIL>"
        val model = PlaceholderBasedModel(
                advisor = Advisor {
                    email = "<EMAIL>"
                },
        )
        val res = EmailTemplateEngine.process_address_template(template, model)
                .map { it.email }
        Assertions.assertThat(res).containsExactly("<EMAIL>", "<EMAIL>")
    }
}
