package com.cpvsn.rm.core.util

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import java.time.Instant
import java.time.format.DateTimeFormatter

class UrlUtilTest {
    @Nested
    inner class joinTest{
        @Test
        fun test100() {
            val res1 = UrlUtil.join("capvision.com", "foo")
            val res2 = UrlUtil.join("capvision.com/", "foo")
            assertThat(res1).isEqualTo("capvision.com/foo")
            assertThat(res2).isEqualTo("capvision.com/foo")
        }

        @Test
        fun test200() {
            val res1 = UrlUtil.join("capvision.com", "foo", "bar")
            val res2 = UrlUtil.join("capvision.com/", "foo/", "bar")
            assertThat(res1).isEqualTo("capvision.com/foo/bar")
            assertThat(res2).isEqualTo("capvision.com/foo/bar")
        }
    }

    @Nested
    inner class addParamTest{
        @Test
        fun test100() {
            val res1 = UrlUtil.addParam("capvision.com", "bar", "bar")
            val res2 = UrlUtil.addParam("capvision.com?foo=foo", "bar", "bar")
            assertThat(res1).isEqualTo("capvision.com?bar=bar")
            assertThat(res2).isEqualTo("capvision.com?foo=foo&bar=bar")
        }
    }

    @Test
    fun test_instant() {
        val r = Instant.from(DateTimeFormatter.ISO_DATE_TIME.parse("2024-08-02T07:16:14.306213746"))
        println()
    }
}
