package com.cpvsn.rm.cron.action

import com.cpvsn.rm.core.features.core.app_config.AppConfigEntryService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.info.BuildProperties
import org.springframework.boot.info.GitProperties
import org.springframework.core.env.Environment
import org.springframework.core.env.get
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(produces = [MediaType.APPLICATION_JSON_VALUE])
class RmCronApi {
    @Autowired(required = false)
    private var buildProperties: BuildProperties? = null

    @Autowired(required = false)
    private var gitProperties: GitProperties? = null

    @Autowired
    private lateinit var environment: Environment

    @Autowired
    private lateinit var appConfigEntryService: AppConfigEntryService

    @GetMapping("/build_info")
    fun build_info(): BuildProperties? {
        return buildProperties
    }

    @GetMapping("/git_info")
    fun git_info(): GitProperties? {
        return gitProperties
    }

    @GetMapping("/env")
    fun env(): Map<String, String?> {
        return listOf(
            "TZ",
            "LOCALE"
        ).associateWith {
            environment[it]
        }
    }
}
