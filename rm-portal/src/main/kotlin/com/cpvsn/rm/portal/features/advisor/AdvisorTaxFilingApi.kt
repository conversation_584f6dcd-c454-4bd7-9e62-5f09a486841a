package com.cpvsn.rm.portal.features.advisor

import com.cpvsn.rm.core.features.tax.AdvisorTaxFilingService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping(path = ["/portal/tax"], produces = [MediaType.APPLICATION_JSON_VALUE])
class AdvisorTaxFilingApi {
    @Autowired
    private lateinit var advisorTaxFilingService: AdvisorTaxFilingService
}