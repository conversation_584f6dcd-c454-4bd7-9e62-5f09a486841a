package com.cpvsn.rm.portal.features.client_contact

import com.cpvsn.core.util.extension.add_prefix
import com.cpvsn.core.util.extension.assert_exist
import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.client.contact.ClientContact
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.portal.clientcontact.PortalClientContact
import com.cpvsn.rm.core.features.portal.clientcontact.PortalClientContactService
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.Task

import com.cpvsn.rm.core.features.task.advisor_profile.TaskAdvisorProfile
import com.cpvsn.rm.core.features.task.feedback.TaskContactFeedback
import com.cpvsn.rm.core.features.task.review.TaskContactReview

import com.cpvsn.rm.core.features.auth.portal.contact_portal
import com.cpvsn.rm.portal.util.AdvisorNameUtil
import com.cpvsn.web.auth.AuthContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*
import java.time.Instant

@RestController
@RequestMapping(path = ["/contact_portal"], produces = [MediaType.APPLICATION_JSON_VALUE])
class ContactPortalController {

    //region @
    @Autowired
    private lateinit var portalClientContactService: PortalClientContactService
    //endregion

    //region datas
    @GetMapping("/portal")
    fun portal(): PortalClientContact {
        return AuthContext.contact_portal
    }

    @GetMapping("/project")
    fun project(): Project {
        val res = Project.get(
                AuthContext.contact_portal.project_id,
                include = Includes.setOf(
                        Project::project_client_contacts,
                        Project::client,
                        Project::tasks,
                        Project::tasks dot Task::advisor,
                        Project::tasks dot Task::advisor dot Advisor::jobs,
                        Project::tasks dot Task::advisor_profile dot TaskAdvisorProfile::company,
                )
        )

        // 5.Client Profile:
        // b.Blinding Expert Name if the contract isn’t approved when sending bios – confirmed 2/17
        val client = Project.get(
                AuthContext.contact_portal.project_id,
                Includes.setOf(Project::client)
        ).client.assert_exist()
        if (!client.isExecuting) {
            res.tasks?.mapNotNull { it.advisor }?.forEachIndexed { index, advisor ->
                AdvisorNameUtil.hind_advisor_name(index, advisor)
            }
        }

        return res
    }

    @GetMapping("/tasks")
    fun list_task(
            query: Task.Query,
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            sort: Sort,
    ): List<Task> {
        val q = query.copy(
                project_id = AuthContext.contact_portal.project_id,
        )
        val res = Task.listAll(q,
                sort = sort,
                include = Includes.setOf(
                        Task::advisor,
                        Task::advisor dot Advisor::jobs,
                        Task::advisor_profile dot TaskAdvisorProfile::company,
                        Task::client_compliance_reviews,
                        Task::client_contact_reviews,
                        Task::latest_submitted_sq,
                        Task::latest_submitted_sq dot InquiryInstance::questions,
                        Task::latest_submitted_sq dot InquiryInstance::answers,
                )
        )

        // 5.Client Profile:
        // b.Blinding Expert Name if the contract isn’t approved when sending bios – confirmed 2/17
        val client = Project.get(
                AuthContext.contact_portal.project_id,
                Includes.setOf(Project::client)
        ).client.assert_exist()
        if (!client.isExecuting) {
            res.mapNotNull { it.advisor }.forEachIndexed { index, advisor ->
                AdvisorNameUtil.hind_advisor_name(index, advisor)
            }
        }

        return res
    }

    @GetMapping("/portal_tasks")
    fun list_portal_tasks(
            query: Task.Query,
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            sort: Sort,
    ): List<Task> {
        val q = query.copy(
                project_id = AuthContext.contact_portal.project_id,
                ids = AuthContext.contact_portal.task_id_set,
        )
        val res = Task.listAll(q, sort, include = Includes.setOf(
                Task::advisor,
                Task::advisor_profile dot TaskAdvisorProfile::company,
                Task::latest_submitted_sq,
        ) + Includes.setOf(
                InquiryInstance::questions,
                InquiryInstance::answers,
        ).add_prefix(Task::latest_submitted_sq.name))

        // 5.Client Profile:
        // b.Blinding Expert Name if the contract isn’t approved when sending bios – confirmed 2/17
        val client = Project.get(
                AuthContext.contact_portal.project_id,
                Includes.setOf(Project::client)
        ).client.assert_exist()
        if (!client.isExecuting) {
            res.mapNotNull { it.advisor }.forEachIndexed { index, advisor ->
                AdvisorNameUtil.hind_advisor_name(index, advisor)
            }
        }
        return res
    }

    @GetMapping("/task")
    fun task(): Task {
        val portal = AuthContext.contact_portal
        return portal.feedback_task_id!!.let {
            Task.get(it)
        }
    }

    @GetMapping("/project_compliance_officers")
    fun project_compliance_officers(): List<ClientContact> {
        val project = Project.get(
                AuthContext.contact_portal.project_id,
                include = Project.CLIENT_CONTACT_EXTRA
        )
        return project.client_compliance_officers.orEmpty()
    }

    @GetMapping("/project_contacts")
    fun project_contacts(): List<ClientContact> {
        val project = Project.get(
                AuthContext.contact_portal.project_id,
                include = Project.CLIENT_CONTACT_EXTRA
        )
        return project.client_common_contacts.orEmpty()
    }
    //endregion

    //region Picker (review)
    @GetMapping("/client_contact_review")
    fun list_client_contact_review(
            query: TaskContactReview.Query,
    ): List<TaskContactReview> {
        val project = Project.get(
                AuthContext.contact_portal.project_id,
                include = setOf(Project::tasks.name)
        )
        val q = query.copy(task_ids = project.tasks?.ids())
        val e = setOf(
                TaskContactReview::task.name,
                *setOf(
                        Task::advisor.name
                ).add_prefix(TaskContactReview::task.name)
        )
        return TaskContactReview.findAll(q, e)
    }

    data class TaskReviewRequest(
            val review_list: List<TaskContactReview>
    )

    @PostMapping("/client_contact_review/batch")
    fun submit_client_contact_review(
            @RequestBody request: TaskReviewRequest
    ): List<TaskContactReview> {
        val portal = AuthContext.contact_portal
        return portalClientContactService.submit_review(portal, request.review_list)
    }
    //endregion

    //region AG
    /**
     * show all task related schedule create by approved advisor
     */
    @GetMapping("/schedule")
    fun list_schedule(
            query: Schedule.Query,
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            sort: Sort,
    ): List<Schedule> {
//        val portal = AuthContext.contact_portal
        val q = query.copy(
//                project_id = portal.project_id,
//                task_ids = portal.task_id_set,
                start_time_gt = Instant.now(),
                creator_type = Schedule.Role.ADVISOR,
                param_preset = Schedule.Query.ParamPreset.CLIENT_CONTACT_PORTAL
        )
        val extra = Includes.setOf(
                Schedule::advisor,
                Schedule::task dot Task::project,
                Schedule::task dot Task::advisor_profile dot TaskAdvisorProfile::company,
        )
        return Schedule.listAll(q, sort, extra)
    }

    /**
     * show all schedule create by pm
     */
    @GetMapping("/pm_schedule")
    fun list_pm_schedule(
            query: Schedule.Query,
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            sort: Sort,
    ): List<Schedule> {
        query.client_contact_id.assert_valid_id { "query param: client_contact_id is required" }
        val q = query.copy(
                creator_type = Schedule.Role.PM
        )
        return Schedule.listAll(q, sort)
    }

    data class ContactScheduleRequest(
            val client_contact_id: Int?,
            val ranges: List<Schedule> = emptyList()
    )

    @PostMapping("/schedule")
    fun submit_schedule(
            @RequestBody request: ContactScheduleRequest
    ): List<Schedule> {
        val portal = AuthContext.contact_portal
        request.ranges.forEach {
            it.client_contact_id = request.client_contact_id
            it.project_id = portal.project_id
            it.task_id.assert_valid_id { "invalid task_id" }
            it.advisor_id.assert_valid_id { "invalid advisor_id" }
        }
        return portalClientContactService.submit_schedule(
                portal,
                request.client_contact_id,
                request.ranges
        )
    }
    //endregion

    //region post call (feedback)
    @PostMapping("/feedback")
    fun submit_feedback(
            @RequestBody feedback: TaskContactFeedback
    ): TaskContactFeedback {
        val portal = AuthContext.contact_portal
        val task = portal.feedback_task_id!!.let {
            Task.get(it).assert_exist(it)
        }
        feedback.task_id = task.id
        feedback.advisor_id = task.advisor_id.assert_valid_id()
        feedback.client_contact_id = task.client_contact_id!!
        return portalClientContactService.submit_feedback(
                portal,
                feedback.client_contact_id,
                feedback
        )
    }
    //endregion

}
