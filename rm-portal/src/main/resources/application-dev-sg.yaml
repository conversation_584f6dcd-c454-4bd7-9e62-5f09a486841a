spring:
  rabbitmq:
    host: rabbitmq
    password: mq@2021!
    username: mq
  redis:
    host: redis-master
  datasource:
    local:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: jdbc:mysql://************:3306/sg_db_qa?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
      username: qa
      password: qa@2024!
      type: com.zaxxer.hikari.HikariDataSource
      maximum-pool-size: 4
    bridge:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: *********************************************************************************************************
      username: qa
      password: qa@2024!
      type: com.zaxxer.hikari.HikariDataSource
      maximum-pool-size: 4

es:
  host: elasticsearch.es8-17-dev.svc.cluster.local
  port: 9200
  cluster_name: elasticsearch
  fingerprint: 17:9F:E7:E9:6F:8F:64:4F:63:B7:4B:B9:5D:87:CB:40:09:C2:8F:19:35:C2:06:4E:1F:FE:EB:CC:F1:01:3E:4C
  user_name: elastic
  password: ApMEKSgUvwOqJK6eZbN
# ---------------------------------------------------------------------------------------------------------------------

auth:
  jwt:
    iss: us-db
    secret: JrvSR/LoyA9jzc7rE82wuWc6YPw7u/a34ctAAPbG3dQ=

oauth2-client:
  facebook:
    client-id: 602030593862496
    client-secret: fbacc45af4b5c524ad939221e1afdc95
    proxy:
      host: proxy.capvision.com
      port: 8080
  google:
    client-id: 68925922578-tf33991pd4lm86nvla1vcr36eg9ubk87.apps.googleusercontent.com
    client-secret: eGlG-nIqKVP0FJbph89425ru
    proxy:
      host: proxy.capvision.com
      port: 8080
  linkedin:
    client-id: 863ecc7ahyz9a5
    client-secret: ukwwisrJL8dfc5xH
# ---------------------------------------------------------------------------------------------------------------------
com:
  cpvsn:
    portal:
      advisorPreCallUrl: https://qa-udb3.capvision.com/portal/#/b
      advisorPostCallUrl: https://qa-udb3.capvision.com/portal/#/after-task
      contactPreCallUrl: https://qa-udb3.capvision.com/portal/#/pick
      contactPostCallUrl: https://qa-udb3.capvision.com/portal/#/afeedback
      clientComplianceLoginUrl: https://qa-compliance3.capvision.com/us/
    conf:
      db_domain: https://qa-udb3.capvision.com
      locations_version: 4
      bank_account_secret_key: VewPBCAZ1DFx1m5BxAHxeA==
      cors_origin: https://qa-udb3.capvision.com,https://qa-compliance3.capvision.com,http://localhost:9529,http://localhost:9530
    es:
      indice:
        - index_enum: ADVISOR
          index_name: advisor_sg
        - index_enum: PROJECT
          index_name: project_sg
        - index_enum: COMPANY
          index_name: company_sg
logging:
  level:
    com.cpvsn.rm.core:
      features:
        task:
          TaskEventService: debug
        portal: debug
        revenue:
          RevenueService: debug
          cronjob: debug

slack:
  enable: false
  apps:
    # test (workspace:Capvision Platform)
    - app_enum: EXPERT_ACTIVITY_BOT
      id: A03CDBR3LMQ
      slack_bot_token: ********************************************************
      slack_signing_secret: 053fcaaf2e6c947b9b8f64c8d7064246
      client_secret: 22df79cf754e6d6412efa8d2dc62d9d1

cap.svc:
  rabbitmq:
    email:
      exchange: 'mail_sg.direct'
      routing-key: 'mail_sg'
    outreach-email:
      exchange: 'mail_outreach_sg.direct'
      routing-key: 'mail_outreach_sg'
    bulk_outreach-email:
      exchange: 'mail_batch_sg.direct'
      routing-key: 'mail_batch_sg'
  env:
    module: rm-portal
  hashid:
    salt: 'rm-server-sg'
    min-hash-length: 6

cap.bridge:
  us:
    base-url: https://qa-api.capvision.com/bridge-server-preview/api/
  cn:
    base-url: https://qa-api.capvision.com/bridge-server-preview/api/
  sea:
    base-url: https://qa-api.capvision.com/bridge-server-preview/api/
  default_conf:
    base-url: https://qa-api.capvision.com/bridge-server-preview/api/

virtual-incentives:
  user_name: capvision8260
  password: hQ4RprlxqCpxJVmOUzZ5ejWBqd7CysoLiCdtnNG84w9Ts7jkHX1loAfyutbBjZzx

exchange-rate:
  key: ************************

contact-out:
  token: 'zNUMoYOfxc7FJMQVRd5C18uE'
  url: 'https://api.contactout.com/v1'

rocket_reach:
  token: '175eb89k4d55864800cc5ba8f5316868dd0a1821'
  url: 'https://api.rocketreach.co/api/v2'