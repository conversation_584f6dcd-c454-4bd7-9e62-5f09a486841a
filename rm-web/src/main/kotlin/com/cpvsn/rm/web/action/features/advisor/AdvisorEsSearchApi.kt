package com.cpvsn.rm.web.action.features.advisor

import com.cpvsn.core.model.PageData
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.search.EsSearchService
import com.cpvsn.rm.core.features.search.pojo.AdvisorNetworkSearchInput
import com.cpvsn.rm.core.features.search.pojo.EsSearchResponse
import com.cpvsn.rm.web.annotation.LogResponse
import org.elasticsearch.action.search.SearchPhaseExecutionException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/advisors"], produces = [MediaType.APPLICATION_JSON_VALUE])
class AdvisorEsSearchApi {

    @Autowired
    private lateinit var service: EsSearchService

    @LogResponse(false)
    @PostMapping("/es/search")
    fun search_advisor(
        @RequestBody map: Map<String, Any?>,
        extra: Includes?,
    ): Page<Advisor> {
        val es_advisors = try {
            service.advisor_search(AdvisorNetworkSearchInput.byMap(map))
        } catch (e: SearchPhaseExecutionException) {
            EsSearchResponse(
                list = emptyList(),
                total = 0L,
            )
        }
        val advisors = Advisor.findAll(
            Advisor.Query(
                ids = es_advisors.list.mapNotNull { it.source?.id }.toSet()
            ), extra.orEmpty()
        )
        return PageData(advisors, es_advisors.total)
    }
}
