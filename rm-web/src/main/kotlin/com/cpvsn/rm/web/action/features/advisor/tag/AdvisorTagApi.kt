package com.cpvsn.rm.web.action.features.advisor.tag

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.spring.query.CommaSplitCollection
import com.cpvsn.crud.spring.util.ext.explicitFields
import com.cpvsn.rm.core.features.advisor.tag.AdvisorTag
import com.cpvsn.rm.core.features.advisor.tag.AdvisorTagReevaluateService
import com.cpvsn.rm.core.features.advisor.tag.AdvisorTagService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/advisors/tags"])
class AdvisorTagApi {

    @Autowired
    private lateinit var service: AdvisorTagService
    @Autowired
    private lateinit var advisorTagReevaluateService: AdvisorTagReevaluateService

    @GetMapping("/{id}")
    fun getById(
        @PathVariable id: Int,
        extra: Includes?,
        @RequestParam(required = false, defaultValue = "false")
        reevaluate_stale: Boolean
    ): AdvisorTag? {
        val res = AdvisorTag.find(id, extra.orEmpty())
        res?.takeIf { reevaluate_stale }
            ?.let {
                advisorTagReevaluateService.reevaluate_all_map_types(listOf(it))
            }
        return res
    }

    @GetMapping
    fun list(
        query: AdvisorTag.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        pageRequest: PageRequest,
        @RequestParam(required = false, defaultValue = "false")
        reevaluate_stale: Boolean,
    ): Page<AdvisorTag> {
        return AdvisorTag.list(query, pageRequest, extra.orEmpty())
            .apply {
                if (reevaluate_stale)
                    advisorTagReevaluateService.reevaluate_all_map_types(this.items)
            }
    }

    @PostMapping("/search")
    fun list2(
        @RequestBody query: AdvisorTag.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        pageRequest: PageRequest,
        @RequestParam(required = false, defaultValue = "false")
        reevaluate_stale: Boolean,
    ): Page<AdvisorTag> {
        return AdvisorTag.list(query, pageRequest, extra.orEmpty())
            .apply {
                if (reevaluate_stale)
                    advisorTagReevaluateService.reevaluate_all_map_types(this.items)
            }
    }

    @GetMapping(params = ["pagination=false"])
    fun listAll(
        query: AdvisorTag.Query,
        extra: Includes?,
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        sort: Sort,
        @RequestParam(required = false, defaultValue = "false")
        reevaluate_stale: Boolean,
    ): List<AdvisorTag> {
        return AdvisorTag.listAll(query, sort, extra.orEmpty())
            .apply {
                if (reevaluate_stale)
                    advisorTagReevaluateService.reevaluate_all_map_types(this)
            }
    }

    @PostMapping
    fun save(
        @RequestBody
        entity: AdvisorTag
    ): AdvisorTag {
        return service.save_cascade(entity)
    }

    @PutMapping("/{id}")
    fun update(
        @PathVariable id: Int,
        @RequestBody patch: JacksonPatch<AdvisorTag>,
        @CommaSplitCollection fields: Set<String>?,
        extra: Includes?,
    ): AdvisorTag {
        patch.entity.id = id
        return service.patch_cascade(
            patch.explicitFields(fields),
            include = extra ?: fields ?: patch.fields,
        )
    }

    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: Int
    ) {
        return AdvisorTag.delete(id)
    }

}
