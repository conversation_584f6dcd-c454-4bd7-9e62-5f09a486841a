package com.cpvsn.rm.web.action.features.app

import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.ModelAndView

@RestController
@RequestMapping(produces = [MediaType.APPLICATION_JSON_VALUE])
class HelloController {

    @GetMapping("")
    fun index(): String {
        return "Greetings from rm-server!"
    }

    @GetMapping("/hello")
    fun hello(): String {
        return "Hello!"
    }

}
