package com.cpvsn.rm.web.action.features.client

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.spring.query.CommaSplitCollection
import com.cpvsn.rm.core.features.client.accountnote.ClientAccountNote
import com.cpvsn.rm.core.features.client.accountnote.ClientAccountNoteService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/client/account_notes"])
class ClientAccountNoteApi {
    @Autowired
    private lateinit var service: ClientAccountNoteService

    @GetMapping("/{id}")
    fun getById(
        @PathVariable id: Int,
        extra: Includes?
    ): ClientAccountNote? {
        return ClientAccountNote.find(id, extra.orEmpty())
    }

    @GetMapping
    fun list(
        query: ClientAccountNote.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ]
        )
        pageRequest: PageRequest
    ): Page<ClientAccountNote> {
        return ClientAccountNote.list(query, pageRequest, extra.orEmpty())
    }

    @PostMapping
    fun save(
        @RequestBody
        entity: ClientAccountNote
    ): ClientAccountNote {
        return service.save_cascade(entity)
    }

    @PutMapping("/{id}")
    fun patch(
        @PathVariable id: Int,
        @RequestBody patch: JacksonPatch<ClientAccountNote>,
        @CommaSplitCollection fields: Set<String>?,
        extra: Includes?,
    ): ClientAccountNote {
        return service.patch_cascade(
            patch,
            extra ?: fields ?: patch.fields
        )
    }

    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: Int
    ) {
        return ClientAccountNote.delete(id)
    }
}