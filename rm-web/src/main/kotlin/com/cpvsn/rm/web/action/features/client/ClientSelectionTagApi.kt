package com.cpvsn.rm.web.action.features.client

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.spring.query.CommaSplitCollection
import com.cpvsn.crud.spring.util.ext.explicitFields
import com.cpvsn.rm.core.features.misc.client_selection_tag.ClientSelectionTag
import com.cpvsn.rm.core.features.misc.client_selection_tag.ClientSelectionTagService
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.client_selection.ClientSelectionTagRef
import com.cpvsn.rm.core.util.biz_require
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(value = ["/client_selection_tag"], produces = [(MediaType.APPLICATION_JSON_VALUE)])
class ClientSelectionTagApi {
    @Autowired
    private lateinit var service: ClientSelectionTagService

    //region crud
    @GetMapping
    fun list(
            query: ClientSelectionTag.Query,
            extra: Includes?,
            @PageRequestDefault(page = 1, size = 20)
            @SortDefault(
                    orders = [
                        SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
                    ]
            )
            pageRequest: PageRequest,
    ): Page<ClientSelectionTag> {
        return ClientSelectionTag.list(query, pageRequest, extra.orEmpty())
    }

    @GetMapping("/project_available")
    fun listProjectAvailableTags(
            @RequestParam project_id: Int
    ): List<ClientSelectionTag> {
        return service.listProjectAvailableTags(project_id)
    }

    @PostMapping
    fun save(
            @RequestBody
            entity: ClientSelectionTag,
    ): ClientSelectionTag {
        return ClientSelectionTag.save(entity)
    }

    @PutMapping("/{id}")
    fun update(
        @PathVariable id: Int,
        @RequestBody patch: JacksonPatch<ClientSelectionTag>,
        @CommaSplitCollection fields: Set<String>?,
        extra: Includes?,
    ): ClientSelectionTag {
        val tag = ClientSelectionTag.get(id)
        biz_require(tag.scope != ClientSelectionTag.Scope.GLOBAL)
        return ClientSelectionTag.patchThenGet(
            patch.explicitFields(fields),
            extra ?: fields ?: patch.fields
        )
    }

    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: Int,
    ) {
        val tag = ClientSelectionTag.get(id)
        biz_require(tag.scope != ClientSelectionTag.Scope.GLOBAL)
        ClientSelectionTag.delete(id)
        ClientSelectionTagRef.findAll(
            ClientSelectionTagRef.Query(
                tag_id = id
            )
        ).forEach {
            ClientSelectionTagRef.delete(it)
        }
    }
    //endregion

    //region ref
    @GetMapping("/refs")
    fun list_refs(
            query: ClientSelectionTagRef.Query,
            extra: Includes?,
            @PageRequestDefault(page = 1, size = 20)
            @SortDefault(
                    orders = [
                        SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
                    ]
            )
            pageRequest: PageRequest,
    ): Page<ClientSelectionTagRef> {
        return ClientSelectionTagRef.list(query, pageRequest, extra.orEmpty())
    }

    @PostMapping("/refs")
    fun save_ref(
            @RequestBody
            entity: ClientSelectionTagRef,
    ): ClientSelectionTagRef {
        return ClientSelectionTagRef.save(entity)
    }

    @PatchMapping("/{task_id}/refs/update_batch")
    fun patch(
        @PathVariable task_id: Int,
        @RequestBody patch: JacksonPatch<Task>,
        @CommaSplitCollection fields: Set<String>?,
        extra: Includes?,
    ): Task {
        return Task.patchThenGet(
            patch = patch,
            cascades = setOf(
                Cascade.oneToMany(Task::client_selection_tag_refs)
            ),
            include = extra ?: fields ?: patch.fields
        )
    }

    @DeleteMapping("/refs/{id}")
    fun delete_ref(
            @PathVariable id: Int,
    ) {
        return ClientSelectionTagRef.delete(id)
    }

    //endregion
}