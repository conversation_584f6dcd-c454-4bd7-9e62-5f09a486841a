package com.cpvsn.rm.web.action.features.email

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.spring.query.CommaSplitCollection
import com.cpvsn.crud.spring.util.ext.explicitFields
import com.cpvsn.rm.core.features.email.EmailAddressScore
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/email_address_score"])
class EmailAddressScoreApi {

    @GetMapping("/{id}")
    fun getById(
            @PathVariable id: Int,
            extra: Includes?
    ): EmailAddressScore? {
        return EmailAddressScore.find(id, extra.orEmpty())
    }

    @GetMapping
    fun list(
            query: EmailAddressScore.Query,
            extra: Includes?,
            @PageRequestDefault(page = 1, size = 20)
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            pageRequest: PageRequest
    ): Page<EmailAddressScore> {
        return EmailAddressScore.list(query, pageRequest, extra.orEmpty())
    }

    @PutMapping("/{id}")
    fun update(
            @PathVariable id: Int,
            @RequestBody patch: JacksonPatch<EmailAddressScore>,
            @CommaSplitCollection fields: Set<String>?,
            extra: Includes?,
    ): EmailAddressScore {
        patch.entity.id = id
        return EmailAddressScore.patchThenGet(patch.explicitFields(fields), extra ?: fields ?: patch.fields)
    }

}
