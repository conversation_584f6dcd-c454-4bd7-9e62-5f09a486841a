package com.cpvsn.rm.web.action.features.email

import com.cpvsn.core.util.extension.assert_not_null
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.rm.core.features.email.validation.EmailValidationRecord
import com.cpvsn.rm.core.features.email.validation.EmailValidationRecordService
import com.cpvsn.rm.core.features.email.validation.EmailValidationRequest
import com.cpvsn.rm.core.features.email.validation.EmailValidationResult
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/email_validation_records"])
class EmailValidationRecordApi {

    @Autowired
    private lateinit var service: EmailValidationRecordService

    @GetMapping("/{id}")
    fun getById(
        @PathVariable id: Int,
        extra: Includes?
    ): EmailValidationRecord? {
        return EmailValidationRecord.find(id, extra.orEmpty())
    }

    @GetMapping
    fun list(
        query: EmailValidationRecord.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        pageRequest: PageRequest
    ): Page<EmailValidationRecord> {
        return EmailValidationRecord.list(query, pageRequest, extra.orEmpty())
    }

    @GetMapping(params = ["pagination=false"])
    fun listAll(
        query: EmailValidationRecord.Query,
        extra: Includes?,
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        sort: Sort
    ): List<EmailValidationRecord> {
        return EmailValidationRecord.listAll(query, sort, extra.orEmpty())
    }

    @PatchMapping("/validate")
    fun validate(
        @RequestBody request: EmailValidationRequest
    ): EmailValidationRecord {
        return service.validate(request)
    }

    @PatchMapping("/validate/batch")
    fun validate_batch(
        @RequestBody requests: List<EmailValidationRequest>
    ): List<EmailValidationRecord> {
        return service.legacy_batch_validate(requests)
    }

    @PatchMapping("/validate/batch/v2")
    fun validate_batch_v2(
        @RequestBody requests: List<EmailValidationRequest>
    ): List<EmailValidationResult> {
        return service.batch_validate(requests)
    }

    data class ManuallyValidateRequest(
        val email_info_id: Int,
        val provider: EmailValidationRecord.Provider = EmailValidationRecord.Provider.LIST_WISE
    )

    @PatchMapping("/validate/manually")
    fun validte_list_wise_manually(
        @RequestBody
        request: ManuallyValidateRequest
    ): EmailValidationRecord {
        val email = ContactInfo.firstOrNull(
            query = ContactInfo.Query(
                id = request.email_info_id,
                owner_type = ContactInfo.OwnerType.ADVISOR,
                type = ContactInfo.Type.EMAIL,
            )
        )?.value.assert_not_null { "can't find advisor email by this id ${request.email_info_id}" }
        return service.manually_pass_list_wise(email, request.provider)
    }

    data class ManuallyValidateRequestAdvisorId(
        val advisor_id: Int,
        val provider: EmailValidationRecord.Provider = EmailValidationRecord.Provider.LIST_WISE
    )

    @PatchMapping("/validate/manually/by_advisor_id")
    fun validate_list_wise_manually_by_advisor_id(
        @RequestBody
        request: ManuallyValidateRequestAdvisorId
    ): EmailValidationRecord {
        val email = ContactInfo.firstOrNull(
            query = ContactInfo.Query(
                owner_type = ContactInfo.OwnerType.ADVISOR,
                owner_id = request.advisor_id,
                type = ContactInfo.Type.EMAIL,
            )
        )?.value.assert_not_null { "can't find email by this advisor id ${request.advisor_id}" }
        return service.manually_pass_list_wise(email, request.provider)
    }

    @PostMapping
    fun save(
        @RequestBody
        entity: EmailValidationRecord
    ): EmailValidationRecord {
        return EmailValidationRecord.save(entity)
    }

//    @PutMapping("/{id}")
//    fun update(
//        @PathVariable id: Int,
//        @RequestBody patch: JacksonPatch<EmailValidationRecord>,
//        @CommaSplitCollection fields: Set<String>?,
//        extra: Includes?,
//    ): EmailValidationRecord {
//        patch.entity.id = id
//        return EmailValidationRecord.patchThenGet(
//            patch.explicitFields(fields),
//            extra ?: fields ?: patch.fields
//        )
//    }

    @DeleteMapping("/{id}")
    fun delete(
        @PathVariable id: Int
    ) {
        return EmailValidationRecord.delete(id)
    }

}
