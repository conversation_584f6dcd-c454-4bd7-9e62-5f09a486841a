package com.cpvsn.rm.web.action.features.finance

import com.cpvsn.rm.core.features.finance.usage.CommonUsageExcelModel
import com.cpvsn.rm.core.features.finance.usage.UsageExcelModel
import com.cpvsn.rm.core.features.project.ProjectWorkflow
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(path = ["/usage"], produces = [MediaType.APPLICATION_JSON_VALUE])
class UsageApi {
    @GetMapping("/excel_template/common/available_fields")
    fun common_template_available_fields(): List<Map<String, Any?>> {
        return handle_response(
            CommonUsageExcelModel.computedFields
        )
    }

    @GetMapping("/excel_template/all/available_fields")
    fun all_template_available_fields(): Map<ProjectWorkflow, List<Map<String, Any?>>> {
        return UsageExcelModel.AvailableFields.WORKFLOW_MAP.mapValues {
            handle_response(it.value)
        }
    }

    private fun handle_response(available_field_list: List<UsageExcelModel.AvailableFields>): List<Map<String, Any?>> {
        return available_field_list
            .map { value ->
                listOf(
                    UsageExcelModel.AvailableFields::name,
                    UsageExcelModel.AvailableFields::displayName,
                    UsageExcelModel.AvailableFields::template_column_index,
                    UsageExcelModel.AvailableFields::is_standard,
                ).associate {
                    it.name to it.get(value)
                }
            }
    }
}
