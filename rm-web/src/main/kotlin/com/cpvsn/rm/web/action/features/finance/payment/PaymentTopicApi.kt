package com.cpvsn.rm.web.action.features.finance.payment

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.rm.core.features.finance.payment.PaymentTopic
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/payments/topics"])
class PaymentTopicApi {

    @GetMapping("/{id}")
    fun getById(
        @PathVariable id: String,
        extra: Includes?
    ): PaymentTopic? {
        return PaymentTopic.find(id, extra.orEmpty())
    }

    @GetMapping
    fun list(
        query: PaymentTopic.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        pageRequest: PageRequest
    ): Page<PaymentTopic> {
        return PaymentTopic.list(query, pageRequest, extra.orEmpty())
    }

    @PostMapping("/search")
    fun list2(
        @RequestBody query: PaymentTopic.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        pageRequest: PageRequest
    ): Page<PaymentTopic> {
        return PaymentTopic.list(query, pageRequest, extra.orEmpty())
    }

    @GetMapping(params = ["pagination=false"])
    fun listAll(
        query: PaymentTopic.Query,
        extra: Includes?,
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        sort: Sort
    ): List<PaymentTopic> {
        return PaymentTopic.listAll(query, sort, extra.orEmpty())
    }

//    @PostMapping
//    fun save(
//            @RequestBody
//            entity: PaymentTopic
//    ): PaymentTopic {
//        return PaymentTopic.save(entity)
//    }
//
//    @PutMapping("/{id}")
//    fun update(
//            @PathVariable id: String,
//            @RequestBody patch: JacksonPatch<PaymentTopic>,
//            @CommaSplitCollection fields: Set<String>?,
//            extra: Includes?,
//    ): PaymentTopic {
//        patch.entity.id = id
//        return PaymentTopic.patchThenGet(patch.explicitFields(fields), extra ?: fields ?: patch.fields)
//    }
//
//    @DeleteMapping("/{id}")
//    fun delete(
//            @PathVariable id: String
//    ) {
//        return PaymentTopic.delete(id)
//    }

}
