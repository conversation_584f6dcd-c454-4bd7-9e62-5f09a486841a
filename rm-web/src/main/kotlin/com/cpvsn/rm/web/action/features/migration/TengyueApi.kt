package com.cpvsn.rm.web.action.features.migration

import com.cpvsn.rm.core.features.task.custom.TengyueService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(path = ["/migrations/tengyue"], produces = [MediaType.APPLICATION_JSON_VALUE])
class TengyueApi {

    @Autowired
    private lateinit var tengyueService: TengyueService

    @PatchMapping("/init_redis")
    fun init_redis() {
        tengyueService.set_init()
    }

    @PatchMapping("/get_key")
    fun get_key(): Map<String, String?> {
        val v = tengyueService.get_current()
        return mapOf(
            "current" to v
        )
    }
}