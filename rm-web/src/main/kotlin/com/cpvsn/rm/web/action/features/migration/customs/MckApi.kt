package com.cpvsn.rm.web.action.features.migration.customs

import com.cpvsn.crud.model.Patch
import com.cpvsn.rm.core.features.project.Project
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(path = ["migrations/customs/mck"], produces = [MediaType.APPLICATION_JSON_VALUE])
class MckApi {

    companion object {
        private const val MCK_ID = 115
    }

    //region @
    //endregion

    @PatchMapping("/migrates_project_workstreams")
    fun migrates_project_workstreams(
            query: Project.Query,
            @RequestParam dryRun: Boolean?
    ): List<Project> {
        val q = query.copy(client_id = MCK_ID)
        val projects = Project.findAll(q)
        return projects.mapNotNull {
            val workstreams = it.client_custom_fields?.get("workstreams")
                    as? String ?: return@mapNotNull null
            val list = workstreams.split("\\s*;\\s*".toRegex())

            val before = it.client_custom_fields_json
            val patch = Patch.fromMutator(it) {
                it.client_custom_fields = it.client_custom_fields.orEmpty() + ("workstreams" to list)
            }
            println("""migrate project with id = ${it.id}
                |before: $before
                |after: ${patch.entity.client_custom_fields_json}
            """.trimMargin())
            if (dryRun != true) {
                patch.patch()
            }
            it
        }
    }
}
