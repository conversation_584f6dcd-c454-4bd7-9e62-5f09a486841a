package com.cpvsn.rm.web.action.features.migration.displayid

import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.angle.TaskAngle
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(
    path = ["migrations/display_id"],
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class MigrationDisplayIdApi {


    @GetMapping("/incorrect_projects")
    fun get_incorrect_projects(
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        pageRequest: PageRequest
    ): Page<Project> {
        return Project.list(DuplicateRankProjectQuery(), pageRequest)
    }

    @GetMapping("/incorrect_angles")
    fun get_incorrect_angles(
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        pageRequest: PageRequest
    ): Page<TaskAngle> {
        return TaskAngle.list(DuplicateRankAngleQuery(), pageRequest)
    }

    @GetMapping("/incorrect_angles_exclude_sent_to_client_tasks")
    fun get_incorrect_angles_exclude_sent_to_client_tasks(
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(
                    name = "id",
                    direction = Sort.Direction.DESC
                )
            ]
        )
        pageRequest: PageRequest
    ): Page<TaskAngle> {
        return TaskAngle.list(
            DuplicateRankAngleExcludeSentToClientQuery(),
            pageRequest
        )
    }
}
