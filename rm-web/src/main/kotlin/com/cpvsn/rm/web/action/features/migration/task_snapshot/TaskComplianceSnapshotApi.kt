package com.cpvsn.rm.web.action.features.migration.task_snapshot

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.model.dot
import com.cpvsn.rm.core.features.advisor.Advisor
import com.cpvsn.rm.core.features.advisor.job.AdvisorJob
import com.cpvsn.rm.core.features.compliance.snapshot.TaskLegalSnapshot
import com.cpvsn.rm.core.features.compliance.snapshot.task_legal_snapshot
import com.cpvsn.rm.core.features.compliance.tc.advisor.AdvisorTc
import com.cpvsn.rm.core.features.inquiry.model.InquiryInstance
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.advisor_profile.TaskAdvisorProfile
import com.cpvsn.rm.core.features.task.constant.TaskStatus
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController


@RestController
@RequestMapping(path = ["migrations/task_snapshot"], produces = [MediaType.APPLICATION_JSON_VALUE])
class TaskComplianceSnapshotApi {

    @PatchMapping("/compliance_portal")
    fun task_compliance_snapshot() {
        Task.findAll(
            query = Task.Query(
                client_compliance_status_in = setOf(
                    TaskStatus.ClientCompliance.SENT,
                    TaskStatus.ClientCompliance.REJECTED,
                    TaskStatus.ClientCompliance.APPROVED,
                    TaskStatus.ClientCompliance.HOLD,
                    TaskStatus.ClientCompliance.REQUEST_MORE_INFO,
                )
            ),
            include = Includes.setOf(
                Task::advisor dot Advisor::jobs dot AdvisorJob::company,
                Task::advisor dot Advisor::latest_sent_tc dot AdvisorTc::tc,
                Task::advisor_profile dot TaskAdvisorProfile::company,
                Task::latest_submitted_sq dot InquiryInstance::questions,
                Task::latest_submitted_sq dot InquiryInstance::answers,
                Task::task_legal_snapshot
            ),
        ).forEach { current_task ->
            if (current_task.task_legal_snapshot == null) {
                TaskLegalSnapshot.save(
                    TaskLegalSnapshot().apply {
                        this.task_id = current_task.id
                        this.advisor_id = current_task.advisor_id
                        this.task_info_snapshot = current_task.task_legal_snapshot()
                        this.version = 1
                    }
                )
            }
        }
    }
}

