package com.cpvsn.rm.web.action.features.misc

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.rm.core.features.misc.company.CompanyBlacklist
import com.cpvsn.rm.core.features.misc.company.CompanyBlacklistService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/companies/blacklist"])
class CompanyBlacklistApi {

    @Autowired
    private lateinit var companyBlacklistService: CompanyBlacklistService

    @GetMapping("/{id}")
    fun getById(
            @PathVariable id: Int,
            extra: Includes?
    ): CompanyBlacklist? {
        return CompanyBlacklist.find(id, extra.orEmpty())
    }

    @GetMapping
    fun list(
            query: CompanyBlacklist.Query,
            extra: Includes?,
            @PageRequestDefault(page = 1, size = 20)
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            pageRequest: PageRequest
    ): Page<CompanyBlacklist> {
        return CompanyBlacklist.list(query, pageRequest, extra.orEmpty())
    }

    @GetMapping(params = ["pagination=false"])
    fun listAll(
            query: CompanyBlacklist.Query,
            extra: Includes?,
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            sort: Sort
    ): List<CompanyBlacklist> {
        return CompanyBlacklist.listAll(query, sort, extra.orEmpty())
    }

//    @PostMapping
//    fun save(
//            @RequestBody
//            entity: CompanyBlacklist
//    ): CompanyBlacklist {
//        return companyBlacklistService.create(entity)
//    }
//
//    @PutMapping("/{id}")
//    fun update(
//            @PathVariable id: Int,
//            @RequestBody patch: JacksonPatch<CompanyBlacklist>,
//            @CommaSplitCollection fields: Set<String>?,
//            extra: Includes?,
//    ): CompanyBlacklist {
//        patch.entity.id = id
//        return CompanyBlacklist.patchThenGet(patch.explicitFields(fields), extra ?: fields ?: patch.fields)
//    }
//
//    @DeleteMapping("/{id}")
//    fun delete(
//            @PathVariable id: Int
//    ) {
//        return CompanyBlacklist.delete(id)
//    }

    // currently we already have delete API, in CompanyApi
//    @DeleteMapping("/blacklist", params = ["company_id"])
//    fun remove_from_blacklist(
//        @RequestParam company_id: Int
//    ) {
//        companyBlacklistService.delete_by_company_id(company_id)
//    }

}
