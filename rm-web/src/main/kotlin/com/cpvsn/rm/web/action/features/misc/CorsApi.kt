package com.cpvsn.rm.web.action.features.misc

import com.cpvsn.rm.core.features.thirdparty.ThirdPartyException
import com.cpvsn.rm.core.features.thirdparty.ThirdPartyVendor
import com.cpvsn.rm.core.util.biz_require
import kong.unirest.Unirest
import kong.unirest.UnirestException
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(path = ["/cors"], produces = [MediaType.APPLICATION_JSON_VALUE])
class CorsApi {

    enum class AllowPrefix(
        val urlPrefix: String
    ) {
        CONTACT_OUT("https://api.contactout.com"),
    }

    data class CorsRequest(
        val method: String,
        val url: String,
        val headers: Map<String, String?>?,
        val body: Any?,
    )

    /**
     * example usage:
     * ```curl
     * curl --location --request PATCH 'http://qa-udb2.capvision.com/rm-web-dev/cors/api-redirect' \
     * --header 'uid: 1' \
     * --header 'Content-Type: application/json' \
     * --data-raw '{
     * "method": "GET",
     * "url": "https://api.contactout.com/v1/people/linkedin?profile=https%3A%2F%2Fwww.linkedin.com%2Fin%2Falexander-mirescu-54042210",
     * "headers": {
     * "authorization": "basic",
     * "token": "zNUMoYOfxc7FJMQVRd5C18uE"
     * },
     * "body": null
     * }'
     * ```
     */
    @PatchMapping("/api-redirect", consumes = [MediaType.APPLICATION_JSON_VALUE])
    fun cors(
        @RequestBody request: CorsRequest
    ): String? {
        biz_require(
            AllowPrefix.values()
                .map { it.urlPrefix }
                .any {
                    request.url.startsWith(it)
                }
        ) {
            "Url '${request.url}' is not allowed currently"
        }

        val unirest_req = Unirest
            .request(request.method, request.url)

        request.body?.let {
            unirest_req.body(it)
        }

        request.headers?.forEach { (k, v) ->
            unirest_req.header(k, v)
        }

        return try {
            unirest_req.asString().body
        } catch (e: UnirestException) {
            if (request.url.startsWith(
                    AllowPrefix.CONTACT_OUT.urlPrefix
                )
            ) {
                throw ThirdPartyException(
                    vendor = ThirdPartyVendor.CONTACT_OUT,
                    cause = e,
                )
            } else {
                throw ThirdPartyException(
                    message = "Call '${request.url}' failed! (cause = '${e.message}')",
                    cause = e,
                )
            }
        }
    }
}
