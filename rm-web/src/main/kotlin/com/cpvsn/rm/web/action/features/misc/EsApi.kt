package com.cpvsn.rm.web.action.features.misc

import com.cpvsn.rm.core.features.search.EsSearchService
import com.cpvsn.rm.core.features.search.doctype.AdvisorDoc
import com.cpvsn.rm.core.features.search.doctype.ProjectDoc
import com.cpvsn.rm.core.features.search.pojo.AdvisorNetworkSearchInput
import com.cpvsn.rm.core.features.search.pojo.EsSearchResponse
import com.cpvsn.rm.core.features.search.pojo.ProjectSearchInput
import com.cpvsn.rm.core.features.search.pojo.ProjectSearchRequest
import com.cpvsn.rm.core.features.search.project.ProjectQueryBuilder
import com.cpvsn.rm.web.annotation.LogResponse
import org.elasticsearch.action.search.SearchPhaseExecutionException
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(produces = [MediaType.APPLICATION_JSON_VALUE])
class EsApi {

    @Autowired
    private lateinit var service: EsSearchService

    @LogResponse(false)
    @PostMapping("/advisors/search")
    fun search_advisor(
        @RequestBody map: Map<String, Any?>,
    ): EsSearchResponse<AdvisorDoc> {
        return try {
            service.advisor_search(AdvisorNetworkSearchInput.byMap(map))
        } catch (e: SearchPhaseExecutionException) {
            EsSearchResponse<AdvisorDoc>(
                    list = emptyList(),
                    total = 0L,
            )
        }
    }

    @LogResponse(false)
    @PostMapping("/projects/search")
    fun search_project(
        @RequestBody searchRequest: ProjectSearchRequest,
    ): EsSearchResponse<ProjectDoc> {
        return try {
            service.project_search_old(searchRequest)
        } catch (e: SearchPhaseExecutionException) {
            EsSearchResponse<ProjectDoc>(
                list = emptyList(),
                total = 0L,
            )
        }
    }

    @LogResponse(false)
    @PostMapping("/projects/search_new")
    fun search_project(
        @RequestBody map: Map<String, Any?>,
    ): EsSearchResponse<ProjectDoc> {
        return try {
            service.project_search(ProjectSearchInput.byMap(map))
        } catch (e: SearchPhaseExecutionException) {
            EsSearchResponse<ProjectDoc>(
                    list = emptyList(),
                    total = 0L,
            )
        }
    }

    @GetMapping("/project_search_angle_alias")
    fun project_angle_alias(): List<String> {
        val sorted_list = ProjectQueryBuilder.get_project_angle_alias_map().keys.sortedBy { it.first() }
        return sorted_list
    }
}
