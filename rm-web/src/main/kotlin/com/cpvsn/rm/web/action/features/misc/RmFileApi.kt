package com.cpvsn.rm.web.action.features.misc

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.spring.query.CommaSplitCollection
import com.cpvsn.crud.spring.util.ext.explicitFields
import com.cpvsn.rm.core.features.misc.file.RmFile
import com.cpvsn.rm.core.features.misc.file.RmFileService
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile

@RestController
@RequestMapping(path = ["/files"])
class RmFileApi {

    @Autowired
    private lateinit var service: RmFileService

    @GetMapping("/{id}")
    fun getById(
            @PathVariable id: Int,
            extra: Includes?
    ): RmFile? {
        return RmFile.find(id, extra.orEmpty())
    }

    @GetMapping
    fun list(
            query: RmFile.Query,
            extra: Includes?,
            @PageRequestDefault(page = 1, size = 20)
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            pageRequest: PageRequest
    ): Page<RmFile> {
        return RmFile.list(query, pageRequest, extra.orEmpty())
    }

    @PostMapping
    fun save(
            @RequestBody
            entity: RmFile
    ): RmFile {
        return RmFile.save(entity)
    }

    @PatchMapping(consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    fun upload(
            @RequestParam(required = false) file_name: String? = null,
            @RequestParam(required = false) dir: String? = null,
            @RequestParam(required = false) content_type: String? = null,
            @RequestParam("file") file: MultipartFile,
    ): RmFile {
        return service.create(
                file, file_name, dir, content_type
        )
    }

    @PutMapping("/{id}")
    fun update(
            @PathVariable id: Int,
            @RequestBody patch: JacksonPatch<RmFile>,
            @CommaSplitCollection fields: Set<String>?,
            extra: Includes?,
    ): RmFile {
        patch.entity.id = id
        return RmFile.patchThenGet(patch.explicitFields(fields), extra ?: fields ?: patch.fields)
    }

    @DeleteMapping("/{id}")
    fun delete(
            @PathVariable id: Int
    ) {
        return RmFile.delete(id)
    }

}
