package com.cpvsn.rm.web.action.features.misc

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.orm.cascade.Cascade
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.spring.query.CommaSplitCollection
import com.cpvsn.rm.core.extensions.user_id
import com.cpvsn.rm.core.features.misc.entity_update_log.EntityUpdateLog
import com.cpvsn.rm.core.features.misc.schedule.Schedule
import com.cpvsn.rm.core.features.misc.schedule.ScheduleService
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskRepository
import com.cpvsn.rm.core.features.user.User
import com.cpvsn.web.auth.AuthContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*


@RestController
@RequestMapping(path = ["/schedule"], produces = [MediaType.APPLICATION_JSON_VALUE])
class ScheduleApi {
    @Autowired
    private lateinit var scheduleService: ScheduleService
    @Autowired
    private lateinit var taskRepository: TaskRepository

    @GetMapping
    fun list_schedule(
            query: Schedule.Query,
            extra: Includes?,
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = com.cpvsn.crud.pagination.Sort.Direction.DESC)
            ])
            sort: com.cpvsn.crud.pagination.Sort,
    ): List<Schedule> {
        val q = when (query.param_preset) {
            Schedule.Query.ParamPreset.PM -> {
                query.copy(pm_id = AuthContext.get<User>().id)
            }

            Schedule.Query.ParamPreset.ARRANGE -> {
                scheduleService.show_all_available_and_scheduled_time_slots(query)
            }

            else -> query
        }
        return Schedule.listAll(q, sort, extra.orEmpty())
    }

    @PatchMapping("/cancel", params = ["task_id"])
    fun cancel_by_task(
            @RequestParam task_id: Int
    ): Schedule {
        return scheduleService.cancel_by_task(task_id)
    }

    @PatchMapping("/{id}/cancel")
    fun cancel_schedule(
            @PathVariable id: Int
    ): Schedule {
        return scheduleService.cancel(id)
    }

    @PatchMapping("/{task_id}/contact_schedules/user_edit")
    fun user_edit_batch(
            @PathVariable task_id: Int,
            @RequestBody patch: JacksonPatch<Task>,
            @CommaSplitCollection fields: Set<String>?,
            extra: Includes?,
    ): Task {
        val task = Task.get(task_id, Includes.setOf(Task::contact_schedules))
        // AbstractRepository.patch(com.cpvsn.crud.model.Patch<T>, com.cpvsn.crud.model.CrudOptions.PatchOption<T>)
        val res = Task.patchThenGet(
                patch = patch,
                cascades = setOf(
                        Cascade.oneToMany(
                                property = Task::contact_schedules,
                                callback = Cascade.Callback.prePersist { entity, parent ->
                                    entity.task_id = parent.id
                                    entity.project_id = parent.project_id
                                    entity.advisor_id = parent.advisor_id
                                    // entity.client_contact_id = parent.client_contact_id
                                    entity.creator_type = Schedule.Role.CLIENT_CONTACT
                                }
                        )
                ),
                include = Includes.setOf(Task::contact_schedules)
        )
        EntityUpdateLog.byEntityUpdate(task, res, range = setOf(Task::contact_schedules))
            ?.let {
                it.topic = EntityUpdateLog.Topic.USER_EDIT_CONTACT_SCHEDULE.name
                it.create_by_id = AuthContext.user_id
                it.save()
            }
        return Task.join(res, extra ?: fields ?: patch.fields)
    }

}
