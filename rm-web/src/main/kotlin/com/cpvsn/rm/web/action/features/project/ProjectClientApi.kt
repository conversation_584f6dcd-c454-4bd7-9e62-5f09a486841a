package com.cpvsn.rm.web.action.features.project

import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectChangeClientService
import com.cpvsn.rm.core.features.project.pojo.VerifyChangeClientResult
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/projects"], produces = [MediaType.APPLICATION_JSON_VALUE])
class ProjectClientApi {

    //region @
    @Autowired
    private lateinit var projectChangeClientService: ProjectChangeClientService
    //endregion

    data class ChangeClientRequest(
            val to_client_id: Int
    )

    @PatchMapping("/{project_id}/change_client/verify")
    fun verify_change_client(
            @PathVariable project_id: Int,
            @RequestBody request: ChangeClientRequest,
    ): VerifyChangeClientResult {
        return projectChangeClientService
                .verify_change_client(project_id, request.to_client_id)
    }

    @PatchMapping("/{project_id}/change_client")
    fun change_client(
            @PathVariable project_id: Int,
            @RequestBody request: ChangeClientRequest,
    ): Project {
        return projectChangeClientService
                .change_client(project_id, request.to_client_id)
    }

}
