package com.cpvsn.rm.web.action.features.project

import com.cpvsn.crud.model.Includes
import com.cpvsn.rm.core.features.decipher.ProjectDecipherService
import com.cpvsn.rm.core.features.decipher.base.DecipherSurveyStatus
import com.cpvsn.rm.core.features.task.Task
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/projects/decipher"], produces = [MediaType.APPLICATION_JSON_VALUE])
class ProjectDecipherApi {

    //region @
    @Autowired
    private lateinit var projectDecipherService: ProjectDecipherService
    //endregion

    //region crud
    /**
     * Updates the decipher survey statuses for each task in a given project.
     * @param id DB survey ID - will update all tasks in that project
     */
    @PatchMapping("/{id}/update")
    fun update(
        @PathVariable id: Int,
        extra: Includes?,
    ): List<Task>? {
        return projectDecipherService.update_tasks_decipher_survey_status(id)
    }

    data class ManuallyUpdateDecipherStatusRequest(
        val decipher_status: DecipherSurveyStatus,
        val task_id: Int
    )

    /**
     * Updates the decipher status of a single task manually
     *
     * @param request Contains decipher_status and task_id - sets task_id to a decipher_status.
     * The decipher_status should be contained in [DecipherSurveyStatus.manual_set_statuses], otherwise it'll get overwritten
     * the next time the statuses are updated.
     */
    @PatchMapping("/manual_update")
    fun manual_override(
        @RequestBody
        request: ManuallyUpdateDecipherStatusRequest
    ): List<Task> {
        return projectDecipherService.manual_update(
            decipher_status = request.decipher_status,
            task_id = request.task_id
        )
    }
}