package com.cpvsn.rm.web.action.features.project

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.rm.core.extensions.user_id
import com.cpvsn.rm.core.features.project.ClientProjectXlsDownloadRecord
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectService
import com.cpvsn.rm.core.features.project.ProjectStatisticService
import com.cpvsn.rm.core.features.project.pojo.ProjectExcelModelV1
import com.cpvsn.rm.core.features.project.pojo.ProjectModelV2
import com.cpvsn.rm.core.features.project.pojo.ProjectStatisticItem
import com.cpvsn.rm.core.features.search.EsSearchService
import com.cpvsn.rm.core.features.search.pojo.ProjectSearchInput
import com.cpvsn.rm.core.features.template.FileTemplate
import com.cpvsn.rm.core.features.template.FileTemplateService
import com.cpvsn.rm.core.util.biz_require
import com.cpvsn.web.auth.AuthContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(path = ["/projects"], produces = [MediaType.APPLICATION_JSON_VALUE])
class ProjectReportApi {
    //region @
    @Autowired
    private lateinit var service: ProjectService

    @Autowired
    private lateinit var esSearchService: EsSearchService

    @Autowired
    private lateinit var fileTemplateService: FileTemplateService

    @Autowired
    private lateinit var projectStatisticService: ProjectStatisticService
    //endregion

    //region Project Data
    fun pre_process_query(
            query: Project.Query,
            search_words: String?,
    ): Project.Query {
        val query_string = search_words?.takeIf {
            it.isNotBlank() && it != "*"
        }
        val limit_project_ids = query_string?.let {
            val projectSearchRequest = ProjectSearchInput().apply {
                this.search_words = query_string
                page = 1
                size = 9999  //fixme should search all here
            }

            val docs = esSearchService.project_search(projectSearchRequest).list
            docs.mapNotNull { it.source?.id }.toSet()
        }
        val q = when {
            !query_string.isNullOrBlank() && limit_project_ids.isNullOrEmpty() ->
                Project.Query(
                        id = 0
                )
            !query_string.isNullOrBlank() && !limit_project_ids.isNullOrEmpty() -> {
                val ids = query.ids?.let {
                    it.intersect(limit_project_ids)
                } ?: limit_project_ids
                query.copy(
                        ids = ids,
                        type = Project.Type.CONSULTATION
                )
            }
            else ->
                query.copy(type = Project.Type.CONSULTATION)
        }
        return q
    }

    @GetMapping("/list/project_data")
    fun list_project_data(
        query: Project.Query,
        @RequestParam search_words: String?,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        pageRequest: PageRequest,
    ): ProjectExcelModelV1 {
        val q = pre_process_query(query, search_words)
        val projects = Project.listAll(
            query = q,
            sort = pageRequest.sort ?: Sort.empty(),
            include = extra.orEmpty().plus(ProjectStatisticItem.require_extra)
        )
        return projectStatisticService.build_statistic_model(
            all_countable_projects = projects,
            page_request = pageRequest
        )
    }

    @GetMapping("/list/project_data/excel")
    fun download_project_data_excel(
        query: Project.Query,
        @RequestParam search_words: String?,
        extra: Includes?,
        sort: Sort?,
    ): ResponseEntity<ByteArrayResource> {
        val q = pre_process_query(query, search_words)
        val template = FileTemplate {
            name = "project-data-template"
            type = FileTemplate.Type.EXCEL
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            content_type = FileTemplate.ContentType.CUSTOM
            file_name_template = "Project Data.xlsx"
            template_url = "classpath:templates/report/project_data_v1.xlsx"
        }
        val model = projectStatisticService.build_statistic_model(
            all_countable_projects = Project.listAll(
                query = q,
                sort = sort ?: Sort.empty(),
                include = ProjectExcelModelV1.require_extra.plus(ProjectStatisticItem.require_extra)
            )
        )
        val file = fileTemplateService.process(template, model)
        return file.to_response_entity()
    }
    //endregion

    //region Client Page-Projects tab
    @GetMapping("/list/projects_tab")
    fun list_client_projects_tab(
        query: Project.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        pageRequest: PageRequest,
    ): ProjectModelV2 {
        return ProjectModelV2.createByQuery(query, extra, pageRequest)
    }

    @GetMapping("/list/projects_tab/excel")
    fun download_client_projects_tab(
        query: Project.Query,
        extra: Includes?,
        sort: Sort?,
    ): ResponseEntity<ByteArrayResource> {
        biz_require(query.client_id != null )
        val model = ProjectModelV2.createByQuery(query, extra, PageRequest(10000, 1, sort))
        val template = FileTemplate {
            name = "project-data-template"
            type = FileTemplate.Type.EXCEL
            media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
            content_type = FileTemplate.ContentType.CUSTOM
            file_name_template = "Account Project List.xlsx"
            template_url = "classpath:templates/report/project_data_v2.xlsx"
        }
        val file = fileTemplateService.process(template, model)

        val record = ClientProjectXlsDownloadRecord {
            client_id = query.client_id!!
            user_id = AuthContext.user_id
            project_ids_str = model.items.joinToString(",") { it.project_id.toString() }
        }
        ClientProjectXlsDownloadRecord.save(record)

        return file.to_response_entity()
    }
    //endregion

}
