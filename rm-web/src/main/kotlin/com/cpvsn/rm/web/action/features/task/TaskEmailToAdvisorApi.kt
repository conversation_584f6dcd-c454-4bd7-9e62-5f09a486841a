package com.cpvsn.rm.web.action.features.task

import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisorBatchRequest
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisorRequest
import com.cpvsn.rm.core.features.portal.advisor.PortalAdvisorResponse
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.TaskToAdvisorService
import com.cpvsn.web.log.annotation.LogAspect
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@LogAspect(
    maximumArgumentLength = 2000,
    maximumResultLength = 2000,
)
@RestController
@RequestMapping(produces = [MediaType.APPLICATION_JSON_VALUE])
class TaskEmailToAdvisorApi {
    //region @
    @Autowired
    private lateinit var taskToAdvisorService: TaskToAdvisorService
    //endregion

    @PatchMapping("/tasks/email_to_advisor")
    fun email_to_advisor(
        @RequestBody request: PortalAdvisorRequest
    ): Task {
        taskToAdvisorService.to_advisor(request.portal, request.email)
        return Task.get(request.portal.task_id.assert_valid_id())
    }

    @PatchMapping("/tasks/email_to_advisor/batch")
    fun email_to_advisor(
        @RequestBody requests: PortalAdvisorBatchRequest
    ): List<PortalAdvisorResponse> {
        return taskToAdvisorService.to_advisor_batch(requests.requests)
    }
}
