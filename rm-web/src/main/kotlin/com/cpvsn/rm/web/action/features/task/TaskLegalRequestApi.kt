package com.cpvsn.rm.web.action.features.task

import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.model.JacksonPatch
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.spring.query.CommaSplitCollection
import com.cpvsn.crud.spring.util.ext.explicitFields
import com.cpvsn.rm.core.features.task.approval.TaskLegalRequest
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(path = ["/tasks/legal_requests"])
class TaskLegalRequestApi {

    @GetMapping("/{id}")
    fun getById(
            @PathVariable id: Int,
            extra: Includes?
    ): TaskLegalRequest? {
        return TaskLegalRequest.find(id, extra.orEmpty())
    }

    @GetMapping
    fun list(
        query: TaskLegalRequest.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            pageRequest: PageRequest
    ): Page<TaskLegalRequest> {
        return TaskLegalRequest.list(query, pageRequest, extra.orEmpty())
    }

    @PostMapping("/search")
    fun list2(
        @RequestBody query: TaskLegalRequest.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
            @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            pageRequest: PageRequest
    ): Page<TaskLegalRequest> {
        return TaskLegalRequest.list(query, pageRequest, extra.orEmpty())
    }

    @GetMapping(params = ["pagination=false"])
    fun listAll(
        query: TaskLegalRequest.Query,
        extra: Includes?,
        @SortDefault(orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ])
            sort: Sort
    ): List<TaskLegalRequest> {
        return TaskLegalRequest.listAll(query, sort, extra.orEmpty())
    }

//    @PostMapping
//    fun save(
//            @RequestBody
//            entity: TaskLegalRequest
//    ): TaskLegalRequest {
//        return TaskLegalRequest.save(entity)
//    }
//
//    @PutMapping("/{id}")
//    fun update(
//        @PathVariable id: Int,
//        @RequestBody patch: JacksonPatch<TaskLegalRequest>,
//        @CommaSplitCollection fields: Set<String>?,
//        extra: Includes?,
//    ): TaskLegalRequest {
//        patch.entity.id = id
//        return TaskLegalRequest.patchThenGet(patch.explicitFields(fields), extra ?: fields ?: patch.fields)
//    }
//
//    @DeleteMapping("/{id}")
//    fun delete(
//            @PathVariable id: Int
//    ) {
//        return TaskLegalRequest.delete(id)
//    }

}
