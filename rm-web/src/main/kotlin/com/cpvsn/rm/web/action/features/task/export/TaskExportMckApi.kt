package com.cpvsn.rm.web.action.features.task.export

import com.cpvsn.core.svc.spring.ext.to_response_entity
import com.cpvsn.poiext.excel.extToByteArray
import com.cpvsn.rm.core.features.task.Task
import com.cpvsn.rm.core.features.task.export.MckTaskExportService
import com.cpvsn.web.log.annotation.LogAspect
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.ResponseEntity
import org.springframework.stereotype.Controller
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping

@LogAspect(
    enable = false
)
@Controller
@RequestMapping(path = ["/tasks/export/mck"])
class TaskExportMckApi {
    @Autowired
    private lateinit var service: MckTaskExportService

    @GetMapping("/survey_q1.xlsx")
    fun survey_q1(): ResponseEntity<ByteArrayResource> {
        val workbook = service.survey_q1()
        return workbook.extToByteArray()
            .to_response_entity(file_name = "mck_survey_q1.xlsx")
    }

    @GetMapping("/export.xlsx")
    fun export(
        query: Task.Query,
    ): ResponseEntity<ByteArrayResource> {
        return service.export(query)
            .extToByteArray()
            .to_response_entity(file_name = "export.xlsx")
    }
}
