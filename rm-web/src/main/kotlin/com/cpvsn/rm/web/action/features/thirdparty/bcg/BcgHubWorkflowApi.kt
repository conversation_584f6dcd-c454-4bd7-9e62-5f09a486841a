package com.cpvsn.rm.web.action.features.thirdparty.bcg

import com.cpvsn.core.util.CoreJsonUtil
import com.cpvsn.crud.model.Includes
import com.cpvsn.crud.pagination.Page
import com.cpvsn.crud.pagination.PageRequest
import com.cpvsn.crud.pagination.Sort
import com.cpvsn.crud.spring.pagination.PageRequestDefault
import com.cpvsn.crud.spring.pagination.SortDefault
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.features.email_template.EmailTemplate
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.task.TaskAdvisorRecommendService
import com.cpvsn.rm.core.features.thirdparty.bcg.BcgHubHelper
import com.cpvsn.rm.core.features.thirdparty.bcg.entity.BcgHubEmailTemplate
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.BcgHub
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.enums.BcgExpertHubModule
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.enums.BcgExpertProfileStatus
import com.cpvsn.rm.core.features.thirdparty.bcg.pojo.interop.*
import com.cpvsn.rm.core.features.thirdparty.bcg.vendor.*
import com.cpvsn.rm.core.util.biz_require
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping(
    produces = [MediaType.APPLICATION_JSON_VALUE]
)
class BcgHubWorkflowApi {

    //region @
    companion object {
        const val EEH_ROUTE: String = "/thirdparty/bcg_hub/"
    }

    @Autowired
    private lateinit var hubHelper: BcgHubHelper

    @Autowired
    private lateinit var hubProjectCreationService: BcgHubProjectCreationService

    @Autowired
    private lateinit var hubEmailTemplateService: BcgHubEmailTemplateService

    @Autowired
    private lateinit var hubSelectService: BcgHubClientSelectService

    @Autowired
    private lateinit var hubCallService: BcgHubClientCallService

    @Autowired
    private lateinit var hubResearchManagerService: BcgHubResearchManagerService

    @Autowired
    private lateinit var taskAdvisorRecommendService: TaskAdvisorRecommendService
    //endregion

    //region direct api

    @PostMapping("/eeh/fetch/{module}")
    fun trigger_poll_eeh_message(
        @PathVariable module: BcgExpertHubModule
    ) {
        when (module) {
            BcgExpertHubModule.INTERVIEW_REQUEST -> hubProjectCreationService.fetch_and_handle_expert_interview_request()
            BcgExpertHubModule.SURVEY_REQUEST -> hubProjectCreationService.fetch_and_handle_expert_survey_request()
            BcgExpertHubModule.EMAIL_TEMPLATE -> hubEmailTemplateService.fetch_and_update_template_approved_status()
            BcgExpertHubModule.EXPERT_PROFILE -> hubSelectService.poll_expert_profile_messages()
            BcgExpertHubModule.CALL_MANAGEMENT -> hubCallService.poll_and_handle_call_info_messages()
        }
    }

    @PostMapping("/eeh/get_request/expert_profile")
    fun get_request_expert_profile(
        @RequestBody request: RecommendExpertsRequest
    ): String {
        val res = hubSelectService.build_send_expert_profiles_request(request)
        return CoreJsonUtil.stringify(res)
    }

    @PostMapping("/eeh/get_request/calls")
    fun get_request_calls(
        @RequestBody request: ShareTasksStatusRequest
    ): String {
        val res = hubCallService.send_ag_or_schedule_or_completion_or_cancellation(request)
        return CoreJsonUtil.stringify(res)
    }

    //endregion

    //region test handlers (just for test can be removed)
    @PostMapping("$EEH_ROUTE/handle/interview_request")
    fun handle_project_interview_request(
        @RequestBody request: BcgHub.Project.ExpertInterviewRequest
    ): Project {
        val consultation_project = hubProjectCreationService.handle_interview_request(
            cur_request = request
        )
        return consultation_project
    }

    @PostMapping("$EEH_ROUTE/handle/survey_request")
    fun handle_project_survey_request(
        @RequestBody request: BcgHub.Project.ExpertSurveyRequest
    ) {
        val bcg_client = hubHelper.get_client_bcg()
        val survery_project = hubProjectCreationService.handle_survey_request(
            request,
            bcg_client
        )
        return survery_project
    }


    @PostMapping("$EEH_ROUTE/handle/template_approval")
    fun handle_template_approval_message(
        @RequestBody request: BcgHub.Template.ApprovedTemplateResponse
    ) {
        //fixme
        val project = Project.get(request.projectId.toInt())
        hubEmailTemplateService.handle_approved_message(request, project)
    }


    @PostMapping("$EEH_ROUTE/handle/expert/status_update")
    fun handle_expert_profile_status_update_message(
        @RequestBody requests: List<BcgHub.Profile.ExpertProfilesStatusUpdate>
    ) {
        requests.forEach {
            hubSelectService.handle_expert_profile_message(it)
        }
    }

    @PostMapping("$EEH_ROUTE/handle/call/update")
    fun handle_call_info_update_message(
        @RequestBody requests: List<BcgHub.Call.CallDetails>
    ) {
        requests.forEach {
            hubCallService.handle_single_bcg_call_info_request(it)
        }
    }
    //endregion


    //region Communication template

    @PostMapping("$EEH_ROUTE/send_template_to_bcg_for_approval")
    fun send_tempalte_to_bcg_for_approval(
        @RequestBody
        request: SendTemplateRequest
    ) {
        val project = Project.get(request.project_id)
        hubEmailTemplateService.send_template(
            project = project,
            template = request.template,
            expert_types = request.expert_types
        )
    }


    @GetMapping("$EEH_ROUTE/to_bcg_templates")
    fun list(
        query: BcgHubEmailTemplate.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ]
        )
        pageRequest: PageRequest
    ): Page<BcgHubEmailTemplate> {

        return BcgHubEmailTemplate.list(query, pageRequest, extra.orEmpty())
    }

    @GetMapping("/email_templates_with_to_bcg_info")
    fun list_project_email_templates_with_bcg_info(
        query: EmailTemplate.Query,
        extra: Includes?,
        @PageRequestDefault(page = 1, size = 20)
        @SortDefault(
            orders = [
                SortDefault.OrderDefault(name = "id", direction = Sort.Direction.DESC)
            ]
        )
        pageRequest: PageRequest,
        @RequestParam
        project_id: Int,
    ): Page<EmailTemplate> {

        val email_templates_page = EmailTemplate.list(query, pageRequest, extra.orEmpty())
        val email_templates = email_templates_page.items
        val bcg_template = BcgHubEmailTemplate.findAll(
            query = BcgHubEmailTemplate.Query(
                project_id = project_id,
                original_template_id_in = email_templates.ids(),
            )
        )
        val bcg_templates_associated_by_email_template = bcg_template.groupBy {
            it.original_template_id
        }
        email_templates.forEach {
            it.project_bcg_templates = bcg_templates_associated_by_email_template[it.id]
        }
        return email_templates_page

    }
    //endregion

    @Deprecated("just for test, use original recommend method instead")
    @PatchMapping("$EEH_ROUTE/tasks/experts/recomend")
    fun recommend_experts(
        @RequestBody request: RecommendExpertsRequest
    ) {
        taskAdvisorRecommendService.recommend_experts_for_bcg_eeh(request)
    }

    @Deprecated("just for test, use original task schedule/cancel/complete instead")
    @PatchMapping("$EEH_ROUTE/tasks/calls/send")
    fun send_call_infos(
        @RequestBody request: ShareTasksStatusRequest
    ) {
        hubCallService.send_ag_or_schedule_or_completion_or_cancellation(request)
    }

    @PatchMapping("$EEH_ROUTE/tasks/experts/revoke_sent")
    fun revoke_sent_experts(
        @RequestBody request: RevokeExpertsRequest
    ): BcgHub.Profile.ExpertProfileResponse {
        biz_require(request.task_status_updates.all {
            it.status in setOf(
                BcgExpertProfileStatus.NonConsiderable,
                BcgExpertProfileStatus.Deletion
            )
        }) {
            "Only non-considerable and deletion are supported"
        }
        return hubSelectService.revoke_sent_experts(request)
    }

    @PatchMapping("$EEH_ROUTE/tasks/send_advisor_availability")
    fun send_advisor_availability(
        @RequestBody request: SendAdvisorsAvailablitlyRequest
    ) {
        hubCallService.send_advisor_availability(request)
    }

    @PutMapping("$EEH_ROUTE/send_contacts_to_bcg")
    fun send_contacts_to_bcg(
        @RequestBody request: SendContactsToBcgRequest
    ) {
        hubResearchManagerService.send_contacts_to_bcg(request.project_id)
    }

}