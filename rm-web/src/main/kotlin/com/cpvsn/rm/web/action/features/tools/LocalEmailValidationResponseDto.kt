package com.cpvsn.rm.web.action.features.tools

import com.cpvsn.rm.core.base.pojo.BaseDto
import com.cpvsn.rm.core.base.pojo.BaseDtoCompanion
import com.cpvsn.rm.core.base.pojo.ToolsEmailValidationApiView
import com.cpvsn.rm.core.features.advisor.AdvisorDto
import com.cpvsn.rm.core.features.email.localvalid.LocalEmailValidationResponse
import com.fasterxml.jackson.annotation.JsonView
import io.swagger.v3.oas.annotations.media.Schema

@JsonView(ToolsEmailValidationApiView::class)
data class LocalEmailValidationResponseDto(
    @Schema(description = "email that has been validated")
    val email: String,
//        @Schema(
//            description = "Corresponding advisor information. " +
//                    "Note that it will be a large JSON object, " +
//                    "so that we IGNORE most info in our documentation."
//        )
    val advisor: AdvisorDto?,
    val tracking_status: LocalEmailValidationResponse.TrackingStatus,
    val decline_status: LocalEmailValidationResponse.DeclineStatus?,
) : BaseDto<LocalEmailValidationResponse>() {
    companion object :
        BaseDtoCompanion<LocalEmailValidationResponseDto, LocalEmailValidationResponse>()
}
