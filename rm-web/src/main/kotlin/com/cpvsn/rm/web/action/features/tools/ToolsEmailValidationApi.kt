package com.cpvsn.rm.web.action.features.tools

import com.cpvsn.core.util.extension.assert_valid_id
import com.cpvsn.core.util.extension.is_valid_id
import com.cpvsn.core.util.extension.toLocalDateTime
import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.base.pojo.ToolsEmailValidationApiView
import com.cpvsn.rm.core.features.email.localvalid.LocalEmailValidationResponse
import com.cpvsn.rm.core.features.email.localvalid.LocalEmailValidationService
import com.cpvsn.rm.core.features.finance.revenue.Revenue
import com.cpvsn.rm.core.features.misc.contact_info.ContactInfo
import com.cpvsn.rm.core.features.project.pojo.ProjectDto
import com.cpvsn.rm.core.features.user.TokenType
import com.cpvsn.rm.core.util.biz_require
import com.cpvsn.rm.web.auth.PreAuth
import com.fasterxml.jackson.annotation.JsonView
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.*
import java.time.Year

@PreAuth(allow_token_types = [TokenType.USER_ACCESS_TOKEN, TokenType.USER_SESSION_ID])
@RestController
@RequestMapping(path = ["/email_validation/local"])
class ToolsEmailValidationApi {
    @Autowired
    private lateinit var localEmailValidationService: LocalEmailValidationService

    @JsonView(ToolsEmailValidationApiView::class)
    @Operation(
        description = "Validate a specific email or an advisor's 'main' email"
//        responses = [ApiResponse(
//            content = [Content(
//                schema = Schema(
//                    implementation = LocalEmailValidationResponseDto::class,
//                )
//            )]
//        )]
    )
    @PatchMapping("/validate")
    fun validate(
        @RequestBody request: LocalEmailValidationRequest,
    ): LocalEmailValidationResponseDto {
        biz_require(request.advisor_id.is_valid_id || !request.email.isNullOrBlank()) {
            "Either 'advisor_id' or 'email' is required"
        }
        val res = if (request.advisor_id.is_valid_id) {
            localEmailValidationService.validate_by_advisor_id(
                request.advisor_id.assert_valid_id(),
                request.project_ids,
                request.extra.orEmpty()
            )
        } else {
            localEmailValidationService.validate_by_email(
                email = request.email.orEmpty(),
                project_ids = request.project_ids,
                include = request.extra.orEmpty(),
            )
        }

        return LocalEmailValidationResponseDto.fromModel(res)
    }

    @JsonView(ToolsEmailValidationApiView::class)
    @Operation(
        description = "validate the client_contact info by email",
//        responses = [ApiResponse(
//            content = [Content(
//                schema = Schema(
//                    implementation = ClientContactDto::class,
//                )
//            )],
//        )]
    )
    @PatchMapping("/validate/client_contact")
    fun validate_client_contact(
        @RequestBody request: LocalEmailValidationRequestContact,
    ): LocalEmailValidationResponseContactDto {
        biz_require(request.client_contact_id.is_valid_id || !request.email.isNullOrBlank()) {
            "Either 'client_contact_id' or 'email' is required"
        }
        val res = if (request.client_contact_id.is_valid_id) {
            localEmailValidationService.validate_by_client_contact_id(
                contact_id = request.client_contact_id.assert_valid_id(),
                project_ids = null,
                include = request.extra.orEmpty()
            )
        } else {
            localEmailValidationService.validate_by_email(
                email = request.email.orEmpty(),
                owner_type = ContactInfo.OwnerType.CLIENT_CONTACT,
                project_ids = null,
                include = request.extra.orEmpty(),
            )
        }
        return convert_dto(res)
    }

    private fun convert_dto(
        origin: LocalEmailValidationResponse
    ): LocalEmailValidationResponseContactDto {
        val dto = LocalEmailValidationResponseContactDto
            .fromModel(origin)
        val client_contact_dto = dto.client_contact ?: return dto
        val origin_client_contact = origin.client_contact ?: return dto

        //for computing the client_contact's billable hours
        val client_contact_revenues = Revenue.findAll(
            query = Revenue.Query(
                client_contact_id = origin_client_contact.id
            )
        )

        client_contact_dto.annual_billable_hours = client_contact_revenues
            .filter {
                // this year
                it.revenue_time.toLocalDateTime().year == Year.now().value
            }.sumOf {
                it.billing_hours
            }

        val projects = origin_client_contact.project_client_contacts.orEmpty()
            .distinctBy {
                it.project_id
            }.mapNotNull {
                it.project
            }

        //for computing the project's billable hours
        val project_revenue_map = Revenue.findAll(
            query = Revenue.Query(
                project_ids = projects.ids()
            )
        ).groupBy {
            it.project_id
        }
        client_contact_dto.projects = projects.map { project ->
            val project_dto = ProjectDto.fromModel(project)

            project_dto.billable_hours =
                project_revenue_map[project_dto.id].orEmpty()
                    .sumOf {
                        it.billing_hours
                    }

            project_dto
        }
        return dto
    }
}
