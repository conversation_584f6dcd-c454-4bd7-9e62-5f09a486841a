package com.cpvsn.rm.web.action.features.tools.company

import com.cpvsn.rm.core.base.pojo.ToolsApiView
import com.cpvsn.rm.core.features.misc.company.CompanyDto
import com.cpvsn.rm.core.features.misc.company.es.CompanyDoc
import com.cpvsn.rm.core.features.misc.company.es.CompanyNameSearchInput
import com.cpvsn.rm.core.features.misc.company.es.EsCompanyService
import com.cpvsn.rm.core.features.search.ElasticSearchHelper
import com.cpvsn.rm.core.features.user.TokenType
import com.cpvsn.rm.web.auth.PreAuth
import com.fasterxml.jackson.annotation.JsonView
import io.swagger.v3.oas.annotations.Operation
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@PreAuth(allow_token_types = [TokenType.USER_ACCESS_TOKEN, TokenType.USER_SESSION_ID])
@RestController
@RequestMapping(path = ["/tools/company"])
class ToolsCompanyApi {

    @Autowired
    private lateinit var elasticSearchHelper: ElasticSearchHelper

    @Autowired
    private lateinit var esCompanyService: EsCompanyService

    @JsonView(ToolsApiView::class)
    @Operation(
        description = "Searches companies using elasticsearch keywords. Keyword can be either company name or stock code."
    )
    @PostMapping("/search/es")
    fun search_companies(
        @RequestBody
        input_query: CompanyNameSearchInput
    ): List<CompanyDto> {
        val es_response =
            elasticSearchHelper.process_response(esCompanyService.company_search(input_query), CompanyDoc::class.java)
        return es_response.list.map {
            val company_doc = it.source
            CompanyDto().apply {
                name = company_doc?.name ?: ""
                id = company_doc?.id ?: 0
                stock_code = company_doc?.stock_code ?: ""
                exchange = company_doc?.exchange ?: ""
            }
        }
    }
}