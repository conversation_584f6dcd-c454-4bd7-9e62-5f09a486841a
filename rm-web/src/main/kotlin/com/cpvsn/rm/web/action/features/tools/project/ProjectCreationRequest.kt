package com.cpvsn.rm.web.action.features.tools.project

import com.cpvsn.rm.core.features.misc.constant.BuiltInCurrency
import com.cpvsn.rm.core.features.misc.constant.Industry
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectClientContact
import com.cpvsn.rm.core.features.project.ProjectMember
import com.cpvsn.rm.web.action.features.tools.inquiry.InquiryBranchRequest
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDate
import javax.validation.constraints.Min
import javax.validation.constraints.NotNull

class ProjectCreationRequest(
//    var ndb_id: Int? = null,

    var name: String? = null,

    var code: String = "",

    var client_id: Int? = null,

    @Schema(description = "Currently this is fixed to 'CONSULTATION'")
    var type: Project.Type = Project.Type.CONSULTATION,

    @Schema(description = "Usually 'Consultation' or 'Survey'")
    var sub_type: Project.SubType = type.available_sub_types.first(),

    var case_type: Project.CaseType? = null,

    var industry: Industry? = null,

    var request_content: String = "",
    var sq_id: Int? = null,
    var contract_id: Int? = null,
    var status: Project.Status = Project.Status.ACTIVE,

    var is_proactive: Boolean = false,
    var start_date: LocalDate? = null,
    var end_date: LocalDate? = null,

    @Schema(description = "If the project is a survey project, here is where we store 'Survey honorarium'")
    var rate: BigDecimal? = null,

    @Schema(description = "Currency of 'rate' field")
    var rate_currency: BuiltInCurrency? = null,

    @Schema(description = "If the project is a survey project, here is where we store 'client charge per complete'")
    var client_charge_per_complete: BigDecimal? = null,

    @Schema(description = "Currency of 'client charge per complete' field")
    var client_charge_per_complete_currency: BuiltInCurrency? = null,

    var zone_id_string: String? = null,

    /**
     * third party survey url
     *
     * see also:
     * https://www.notion.so/Survey-MVP-DB-Decipher-a86813cccdc34d37ab81f06da36a26b2
     * https://decipher.zendesk.com/hc/en-us/articles/360010274273-Getting-Started-with-Decipher
     */
    var third_party_survey_url: String? = null,

    /**
     *     This is Decipher's internal project ID.
     */
    var decipher_id: String? = null, //This is Decipher's internal project ID.

    var google_sheet_id: String? = null,

    var clone_from_id: Int? = null,

    var survey_total_charge: BigDecimal? = null,
    var survey_total_charge_currency: BuiltInCurrency? = null,

    /**
     * Note that this only make sense when project is consultation project
     */
    @Schema(description = "Note that this only make sense when project is consultation project")
    var sent_close_email_advisor_ids_str: String? = null,

    /**
     * Note that currently only consultation project has such relation
     */
    @Schema(
        description = "We use this field to bind client contacts\n" +
                "Note that currently only consultation project has such relation"
    )
    var project_client_contacts: List<ProjectClientContactRequest>? = null,

    @Schema(description = "Project Members")
    var members: List<ProjectMemberRequest>? = null,

    @Schema(description = "Task Angles")
    var angles: List<TaskAngleRequest>? = null,

    @Schema(description = "Lead Groups")
    var lead_groups: List<LeadGroupRequest>? = null,

    @Schema(description = "Enable Lead Groups")
    var enable_lead_group: Boolean = false,

    @Schema(description = "Investment Target IDs")
    var investment_target_company_ids: List<Int>? = null,

    @Schema(description = "If not null, will create a slack channel for this project.")
    var slack_channel_name: String? = null,

    var expected_n: Int? = null
) {
    data class ProjectClientContactRequest(
        @NotNull
        @Min(1)
        var client_contact_id: Int = 0,

        @NotNull
        var client_contact_type: ProjectClientContact.Type? = null,
    )

    data class ProjectMemberRequest(

        @NotNull
        @Min(1)
        var uid: Int? = null,

        @NotNull
        var role: ProjectMember.Role? = null,

        )

    data class TaskAngleRequest(

        var name: String = "",

        var rank: Int = 0,

        var next_task_rank: Int = 0,

        var inquiry_branch_id: Int = 0,

        var topic: String? = null,

        // https://www.notion.so/capvision/Angles-Two-new-data-points-when-create-edit-angle-f1336a8fcfc54285a3c6ffacc5d0fcad
        var discussion_topic: String? = null,

        @Schema(description = "Note that rate and rate_currency can only either be all null or all not null!")
        var rate: BigDecimal? = null,

        var rate_currency: BuiltInCurrency? = null,

        var inquiry_branch: InquiryBranchRequest? = null
    )

    data class LeadGroupRequest(
        var project_id: Int = 0,
        var name: String = "",
        var rank: Int = 0,
        var outreach_template_id: Int? = null,
        var honorarium_amount: BigDecimal? = null,
        var honorarium_currency: String? = null
    )

    data class TaskAngleMemberRequest(
        var angle_id: Int = 0,
        var user_id: Int = 0
    )
}
