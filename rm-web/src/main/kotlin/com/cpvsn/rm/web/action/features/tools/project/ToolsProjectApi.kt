package com.cpvsn.rm.web.action.features.tools.project

import com.cpvsn.core.util.experimental.ExperimentalBeanUtil
import com.cpvsn.rm.core.base.pojo.ToolsApiView
import com.cpvsn.rm.core.features.project.Project
import com.cpvsn.rm.core.features.project.ProjectCreationService
import com.cpvsn.rm.core.features.project.ProjectMember
import com.cpvsn.rm.core.features.project.pojo.ProjectDto
import com.cpvsn.rm.core.features.slack.SlackService
import com.cpvsn.rm.core.features.task.angle.TaskAngleMember
import com.cpvsn.rm.core.features.user.TokenType
import com.cpvsn.rm.web.auth.PreAuth
import com.fasterxml.jackson.annotation.JsonView
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@PreAuth(allow_token_types = [TokenType.USER_ACCESS_TOKEN, TokenType.USER_SESSION_ID])
@RestController
@RequestMapping(path = ["/tools/projects"])
class ToolsProjectApi {

    @Autowired
    private lateinit var projectCreationService: ProjectCreationService

    @Autowired
    private lateinit var slackService: SlackService

    @JsonView(ToolsApiView::class)
    @Operation(
        description = "Create a project",
        responses = [ApiResponse(
            content = [Content(
                schema = Schema(
                    implementation = ProjectDto::class,
                )
            )],
            description = "The response object has been simplified, the actual response might be more complicated",
        )]
    )
    @PostMapping
    fun save(
        @RequestBody project: ProjectCreationRequest
    ): ProjectDto {
        val slack_channel_name = project.slack_channel_name
        project.slack_channel_name = null
        val entity = ExperimentalBeanUtil.convert(project, Project::class)
        val project_dto =  ProjectDto.fromModel(
            projectCreationService.save_cascade(entity)
        )

        val angle_members = project_dto.angles?.mapNotNull {angle ->
            val pm_id = project.members?.find { it.role == ProjectMember.Role.PROJECT_MANAGER }?.uid
            pm_id?.let {
                TaskAngleMember().apply {
                    user_id = it
                    angle_id = angle.id
                }
            }
        }
        angle_members?.let{ TaskAngleMember.save(it) }

        slack_channel_name?.let {
            slackService.create_or_update_project_slack_channel(
                project_id = project_dto.id,
                channel_name = "${it}_id${project_dto.id}"
            )
        }
        
        return project_dto
    }
}
