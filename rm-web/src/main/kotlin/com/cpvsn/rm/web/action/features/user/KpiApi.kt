package com.cpvsn.rm.web.action.features.user

import com.cpvsn.rm.core.extensions.user_id
import com.cpvsn.rm.core.features.user.kpi.client_hour.ClientHourIndicator1
import com.cpvsn.rm.core.features.user.kpi.client_hour.ClientHourIndicatorService
import com.cpvsn.web.auth.AuthContext
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping(path = ["/users/kpi"], produces = [MediaType.APPLICATION_JSON_VALUE])
class KpiApi {
    @Autowired
    private lateinit var clientHourIndicatorService: ClientHourIndicatorService

    @GetMapping("/client_hour_indicator1/me")
    fun me(): ClientHourIndicator1 {
        return clientHourIndicatorService.get_client_hour_indicator(AuthContext.user_id)
    }
}
