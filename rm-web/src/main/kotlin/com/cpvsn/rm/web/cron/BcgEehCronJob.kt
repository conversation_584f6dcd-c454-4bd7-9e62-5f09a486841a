package com.cpvsn.rm.web.cron

import com.cpvsn.core.svc.spring.Env
import com.cpvsn.rm.core.config.oversea.OverSeaEnvService
import com.cpvsn.rm.core.config.schedule.CronJobWeb
import com.cpvsn.rm.core.features.bridge.Region
import com.cpvsn.rm.core.features.thirdparty.bcg.vendor.BcgHubClientCallService
import com.cpvsn.rm.core.features.thirdparty.bcg.vendor.BcgHubEmailTemplateService
import com.cpvsn.rm.core.features.thirdparty.bcg.vendor.BcgHubProjectCreationService
import com.cpvsn.rm.core.features.thirdparty.bcg.vendor.BcgHubClientSelectService
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@CronJobWeb
@Component
class BcgEehCronJob {

    //region @
    @Autowired
    private lateinit var bcgHubProjectCreationService: BcgHubProjectCreationService

    @Autowired
    private lateinit var bcgHubEmailTemplateService: BcgHubEmailTemplateService

    @Autowired
    private lateinit var bcgHubClientSelectService: BcgHubClientSelectService

    @Autowired
    private lateinit var bcgHubClientCallService: BcgHubClientCallService

    @Autowired
    private lateinit var envService: OverSeaEnvService

    private val logger = LoggerFactory.getLogger(this::class.java)
    //endregion

    private val should_run by lazy {
        envService.current() in setOf(
            Env.DEFAULT,
            Env.PRODUCTION,
        ) && Region.current == Region.US
    }

    @Scheduled(cron = "0 */3 * * * *")
    @SchedulerLock(name = "fetch_interview_request")
    fun fetch_interview_request() {
        if (should_run) {
            logger.info("start fetching bcg interview request")
            bcgHubProjectCreationService.fetch_and_handle_expert_interview_request()
        }
    }

    @Scheduled(cron = "0 */3 * * * *")
    @SchedulerLock(name = "fetch_template_approval")
    fun fetch_template_approval_message() {
        if (should_run) {
            logger.info("start fetching bcg template approval message")
            bcgHubEmailTemplateService.fetch_and_update_template_approved_status()
        }
    }

    @Scheduled(cron = "0 */3 * * * *")
    @SchedulerLock(name = "fetch_survey_request")
    fun fetch_survey_request() {
        if (should_run) {
            logger.info("start fetching bcg survey request")
            bcgHubProjectCreationService.fetch_and_handle_expert_survey_request()
        }
    }

    @Scheduled(cron = "0 */3 * * * *")
    @SchedulerLock(name = "eeh_fetch_expert_profile_update")
    fun eeh_fetch_expert_profile_update() {
        if (should_run) {
            logger.info("start eeh_fetch_expert_profile_update")
            bcgHubClientSelectService.poll_expert_profile_messages()
        }
    }

    @Scheduled(cron = "0 */3 * * * *")
    @SchedulerLock(name = "eeh_fetch_call_info_update")
    fun eeh_fetch_call_info_update() {
        if (should_run) {
            logger.info("start eeh_fetch_call_info_update")
            bcgHubClientCallService.poll_and_handle_call_info_messages()
        }
    }
}