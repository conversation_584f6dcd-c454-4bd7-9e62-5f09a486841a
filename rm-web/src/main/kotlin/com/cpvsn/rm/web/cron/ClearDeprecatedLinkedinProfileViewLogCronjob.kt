package com.cpvsn.rm.web.cron

import com.cpvsn.crud.util.ids
import com.cpvsn.rm.core.base.AbstractJob
import com.cpvsn.rm.core.config.schedule.CronJobWeb
import com.cpvsn.rm.core.extensions.toDefaultInstant
import com.cpvsn.rm.core.features.misc.LinkedinProfileViewLog
import net.javacrumbs.shedlock.spring.annotation.SchedulerLock
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.scheduling.annotation.Scheduled
import java.time.LocalDateTime

@CronJobWeb
class ClearDeprecatedLinkedinProfileViewLogCronjob : AbstractJob() {

    private val logger = LoggerFactory.getLogger(this.javaClass)

    @Autowired
    private lateinit var repo: LinkedinProfileViewLog.Repo

    @Scheduled(cron = "0 30 1 ? * *")
    @SchedulerLock(name = "clear_deprecated_linkedin_profile_view_log")
    fun clear_deprecated_linkedin_profile_view_log() {
        if (!should_run) return
        logger.info("enter ClearDeprecatedLinkedinProfileViewLogCronjob.clear_deprecated_linkedin_profile_view_log job")

        val logs = LinkedinProfileViewLog.findAll(
            LinkedinProfileViewLog.Query(
                timestamp_lte = LocalDateTime.now().minusDays(2)
                    .withHour(0).withMinute(0).withSecond(0).toDefaultInstant()
            )
        )

        repo.batchDelete(logs.ids().toList())
    }
}