spring:
  rabbitmq:
    host: rabbitmq
    password: mq@2021!
    username: mq
  redis:
    host: redis-master
  datasource:
    local:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: jdbc:mysql://************:3306/udb?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
      username: udb_aws
      password: DrP9RT03l6tNm5GfBoE=
      maximum-pool-size: 60
    #      type: com.zaxxer.hikari.HikariDataSource
    #      leak-detection-threshold: 3000
    #      max-life-time: 28740000
    #      idle-timeout: 28740000
    bridge:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: ******************************************************************************************************
      username: us_bridge_aws
      password: aDwpD5KC8AhrL3QUGLA=
      maximum-pool-size: 30
  #      type: com.zaxxer.hikari.HikariDataSource
  #      leak-detection-threshold: 3000
  #      max-life-time: 28740000
  #      idle-timeout: 28740000
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 30MB
  flyway:
    enabled: true
    placeholder-replacement: false
    out-of-order: true
    ignore-missing-migrations: true
  task:
    scheduling:
      pool:
        size: 5

es:
  host: elasticsearch.es8-17.svc.cluster.local
  port: 9200
  cluster_name: elasticsearch
  fingerprint: 09:48:D8:8C:F0:45:29:53:CC:FF:C2:EA:8A:A9:81:18:0C:B7:43:23:73:F0:8F:50:97:64:59:0A:8B:33:B3:65
  user_name: elastic
  password: YjNDLTgZxzRaSZ2pUaM

# ---------------------------------------------------------------------------------------------------------------------
com:
  cpvsn:
    portal:
      advisorSurveyUrl: https://compliance2.capvision.com/portal/#/expert_survey
      advisorPreCallUrl: https://compliance2.capvision.com/portal/#/b
      advisorPostCallUrl: https://compliance2.capvision.com/portal/#/after-task
      advisorBankAccountUrl: https://compliance2.capvision.com/portal/#/advisor/payment_info
      advisorUnsubscribeOutreachUrl: https://compliance2.capvision.com/portal/#/advisor/unsubscribe
      advisorUnsubscribeOutreachClickOnceUrl: https://api-compliance2.capvision.com/rm-portal-release/path_auth/advisor_portal/unsubscribe/outreach
      advisor3rd-party-survey-url: https://compliance2.capvision.com/portal/#/expert_survey/3rd_party
      advisorDeclineUrl: https://compliance2.capvision.com/portal/#/advisor/decline
      advisorPaymentConfirmUrl: https://compliance2.capvision.com/portal/#/advisor/payment_confirm
      contactPreCallUrl: https://compliance2.capvision.com/portal/#/pick
      contactPostCallUrl: https://compliance2.capvision.com/portal/#/afeedback
      clientComplianceLoginUrl: https://compliance2.capvision.com
      client-user-register-url: https://compliance2.capvision.com/client-portal/#/register
      # twilio begin
      conference-dial-url: https://compliance2.capvision.com/portal/#/conf-dial
      client-portal-conference-dial-url: https://compliance2.capvision.com/client-portal/#/conf-dial
      client-portal-conference-asset-url: https://compliance2.capvision.com/client-portal/#/conf-asset
      advisor-recording-consent-url: https://compliance2.capvision.com/portal/#/record-consent
      # twilio end
      client-portal-advisor-recommendation-url: https://compliance2.capvision.com/client-portal/#/recommendation
      client-portal-advisor-recommendation-internal-url: https://db2.capvision.com/client-portal/#/recommendation
      client-portal-aes-secret-key: gTRpAzm6Pn3xbrrKG+QqNg==
    es:
      indice:
        - index_enum: ADVISOR
          index_name: advisor
        - index_enum: PROJECT
          index_name: usdb_project
        - index_enum: COMPANY
          index_name: usdb_company
    conf:
      db_domain: https://db2.capvision.com
      db_api_endpoint: https://api-db2.capvision.com/rm-web-release
      w9_service_base_url: http://api.capvision.us:32080/py-track1099
      locations_version: 4
      client_account_note_default_notification_emails: <EMAIL>,<EMAIL>
      bank_account_secret_key: qq0ptNWRFy7KZq6g2havhQ==
      cors_origin: https://db2.capvision.com,chrome-extension://aegkhhhcfcoilbmaepiaeiocopichkgg,http://localhost:9528

oauth2-client:
  facebook:
    client-id: ***************
    client-secret: fbacc45af4b5c524ad939221e1afdc95
  google:
    client-id: ***********-tf33991pd4lm86nvla1vcr36eg9ubk87.apps.googleusercontent.com
    client-secret: eGlG-nIqKVP0FJbph89425ru
  linkedin:
    client-id: 863ecc7ahyz9a5
    client-secret: ukwwisrJL8dfc5xH

springdoc:
  api-docs:
    enabled: true
    groups:
      enabled: true
  packages-to-scan: com.cpvsn.rm.web.action.features.tools
  swagger-ui:
    enabled: false

py-uploader:
  host: 'https://api-db2.capvision.com/uploader'

cap:
  svc:
    rabbitmq:
      email:
        exchange: 'mail_us.direct'
        routing-key: 'mail_us'
      outreach-email:
        exchange: 'mail_outreach_us.direct'
        routing-key: 'mail_outreach_us'
      bulk_outreach-email:
        exchange: 'mail_batch_us.direct'
        routing-key: 'mail_batch_us'
    env:
      module: rm-web
    ds-proxy:
      enable-query-logger: false
      enable-slow-query-logger: true
      slow-query-threshold: 2000
    hashid:
      salt: 'rm-server-us'
      min-hash-length: 6
  bridge:
    us:
      base-url: https://bridge.capvision.com/bridge-release/api/
    cn:
      base-url: https://bridge.capvision.com/bridge-release/api/
    sea:
      base-url: https://bridge.capvision.com/bridge-release/api/
    default_conf:
      base-url: https://bridge.capvision.com/bridge-release/api/

    client_secret: MD4CAQAwEAYHKoZIzj0CAQYFK4EEAAoEJzAlAgEBBCCgoLYMEN84Xr09qtzNGDY3HqBv+nX5Yn+whPUOxLPZ8w==

logging:
  level:
    com.cpvsn.rm.core.aspect.PerformanceMonitorAdvice: debug
    com.cpvsn.rm.core.features.search.EsSearchService: debug
    com.zaxxer.hikari: TRACE
#    com.cpvsn.rm.core.features: debug
#    org.springframework.jdbc.core:
#      JdbcTemplate: DEBUG
#      StatementCreatorUtils: TRACE

log-aspect:
  enabled: true
  module: web
  maximumArgumentLength: 3000
  maximumResultLength: 3000
  es:
    host: elasticsearch-master.pod-log
    port: 9200
    cluster_name: elasticsearch
    fingerprint: 80:C4:95:12:64:12:FB:D1:C7:2C:ED:38:E8:E5:2D:D9:80:C3:BE:76:33:6F:12:CB:1F:AC:46:F2:94:DA:07:61
    user_name: elastic
    password: bHLbJl4UrkyBVJrL
    index-template-name-prefix: rm-server
    index-template-version: 0.0.1

slack:
  enable: true
  apps:
    # prod (workspace:Capvision)
    - app_enum: EXPERT_ACTIVITY_BOT
      id: A03DGNMM0QH
      slack_bot_token: ********************************************************
      slack_signing_secret: 17c2e5311b78f587194289ba24ff6106
      client_secret: e816396f4ee01872f628856bee64fe6a

google.sdk:
  credentials-file-location: 'google/credentials.json'
  greenoaks-credentials-file-location: 'google/greenoaks-credentials.json'

sendgrid:
  api-key: '*********************************************************************'
  email-validation-api-key: '*********************************************************************'

list-wise:
  api-key: 'c549698d01864e924631a8a261c0cbb1'
  endpoint: 'http://***********:29910'

whale-wisdom:
  shared-access-key: f5c7PbdwaqWMPfYyB593
  secret-access-key: LgFPrVznywSSuG0liT8vQlV6UCU1lurPo1wrDtJn

decipher:
  key: 'earsdp4bp9atzezxve6ha25gv9zt7av0pwqnp56mpn6smfppat9e68hjh8hgnu96'

# sub account (US DB - Prod)
twilio:
  enable: true
  account_email: '<EMAIL>'
  account_sid: '**********************************'
  account_auth_token: 'dab78a5d818688b827c5b43dd6883c2f'
  client_dial_app_sid: 'AP638f85bff764eb40096b26dd825e09fe'
  client_dial_api_key: '**********************************'
  client_dial_api_secret: 'Xzijh4JU8R9tmCD8HbNdDvKFxW5Ykm55'
  verify_service_sid_default: 'VAbc0771da57f07b4ad5010664fff8a6f4'
  verify_service_sid_for_db_login: 'VAbc0771da57f07b4ad5010664fff8a6f4'

zoom:
  apps:
    - app-enum: USDB_ENGINEER_S2S
      app-id: 5xp6LPWUSb21m2IKxV-sWw
      account-email: <EMAIL>
      client-id: DCOqRqlHQWyxYJ23bn2U0w
      client-secret: 4WIHc22a2Kuwj9Y1o1cegUiKBgUNQZjY
      account-id: NiI6koipQqCezK4k1rVhiA
      webhook-secret-token: DuiQdZt2Rs6B4RQxciOvXw
      host-id: 51D4rSEoSqOwYfOLHDtTMA
    - app-enum: USDB_ENGINEER_2_S2S
      app-id: Bp0d2DRFR863CrDd9cUFqw
      account-email: <EMAIL>
      client-id: yxFTBjQHRmef4v4OSkCgJA
      client-secret: WjAgThTJzKBwCSDiFCJgMxK9w5GTg0vL
      account-id: NiI6koipQqCezK4k1rVhiA
      webhook-secret-token: zHDzAW-UStudJ-2h5HIukA
      host-id: 7OmkaWrOQlGqthx4AhRUNg
    - app-enum: USDB_ENGINEER_3_CONFERENCE_MANAGER
      app-id: _ti7OE8kSDa8eU3GB2R7Qw
      account-email: <EMAIL>
      client-id: lTBD_ZmQwSINbHYC6NXzQ
      client-secret: UiO6kEiKArZpSjhb3ZqjlicKSMkK5FGv
      account-id: NiI6koipQqCezK4k1rVhiA
      webhook-secret-token: Xj-sFhYcRZeMKiC2iI9SnA
      host-id: UjbwEBLqREmF4rJHI2YYZg

virtual-incentives:
  user_name: capvision8260
  password: NoyJJcTAwfRabn29pTqYWq1IlZlzGGCpUqZmodgyWwtiaL8EONENuJ0XmgYjqUuf

exchange-rate:
  key: ************************

deepgram:
  base_url: https://api.deepgram.com
  listen_endpoint: /v1/listen
  token: 09628448414dddad5688873e0e1a994cc7e550f5

bcg:
  base-url: https://api-ec1-kd-public.production.smp.bcg.com
  token-url: https://logon.bcg.com/oauth2/ausjts5bcpVHomPJF1t7/v1
  client-secret: 'Basic MG9hMTRkN3d5Mm5MN2xlY1AxdDg6b0E3bkpGR2xRYlN2dnhTYjZVbFhmQlhwYjd6N0NJVWJfMXg0aVdlYmlMSUJ5ckI3Y2xJeVhacHhuNWJyQl9OVg'
  api-key: '1dZzc8BTe5smyDeBzkoCCB3cq7aSUXqs'

s3:
  sdk:
    clients:
      - client_name: WarburgPincus
        access_key_id: ********************
        secret_access_key: 5p5BkPg4f0O1SZR3riXM4DC138gVsnhm3dJ4gpUm
        bucket: capvision
        region: us-east-1

trolley:
  # live account
  access-key: 'ALPLwp5nIEQ2G6OHONECQT43'
  secret-key: 'X7PFILYLV4F4WBOU6U44C3D5CURYXHU5G6QKRDA5'

contact-out:
  token: 'zNUMoYOfxc7FJMQVRd5C18uE'
  url: 'https://api.contactout.com/v1'

rocket_reach:
  token: '1978c82k13605003d239c5f210343fe0fdedfa92'
  url: 'https://api.rocketreach.co/api/v2'

expert-rag:
  base-url: 'https://api-db2.capvision.com/expert-rag-release/'