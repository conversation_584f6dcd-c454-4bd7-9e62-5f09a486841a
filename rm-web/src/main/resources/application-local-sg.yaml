spring:
  datasource:
    local:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: jdbc:mysql://*************:3306/sg_db_qa?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
      username: qa
      password: qa@2024!
      type: com.zaxxer.hikari.HikariDataSource
      leak-detection-threshold: 3000
    bridge:
      driver-class-name: com.mysql.cj.jdbc.Driver
      jdbc-url: jdbc:mysql://*************:3306/sg_cn_bridge_qa?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
      username: qa
      password: qa@2024!
  #    local:
  #      driver-class-name: com.mysql.cj.jdbc.Driver
  #      jdbc-url: jdbc:mysql://*************:3306/udb?characterEncoding=UTF-8&zeroDateTimeBehavior=convertToNull
  #      username: udb
  #      password: udb@2021!
  #    bridge:
  #      driver-class-name: com.mysql.cj.jdbc.Driver
  #      jdbc-url: *******************************************************************************************************
  #      username: udb
  #      password: udb@2021!


  rabbitmq:
    host: ***********
    port: 30674
    password: mq@2021!
    username: mq
  redis:
    host: ***********
    port: 31380
  servlet:
    multipart:
      max-file-size: 2MB
      max-request-size: 3MB
  flyway:
    #    enabled: false
    enabled: true
    placeholder-replacement: false
    out-of-order: true
    ignore-missing-migrations: true
  # https://flywaydb.org/documentation/configuration/configfile
  sendgrid:
    api-key: '*********************************************************************'

es:
  host: ***********
  port: 31210
  cluster_name: elasticsearch
  fingerprint: 17:9F:E7:E9:6F:8F:64:4F:63:B7:4B:B9:5D:87:CB:40:09:C2:8F:19:35:C2:06:4E:1F:FE:EB:CC:F1:01:3E:4C
  user_name: elastic
  password: ApMEKSgUvwOqJK6eZbN
# ---------------------------------------------------------------------------------------------------------------------
logging:
  level:
    #    com.cpvsn.crud.orm.relation: debug
    com.cpvsn.core.svc.rpc.http.unirest.UnirestService: trace
    com.cpvsn.rm.core.svc.aop.ResponseDataExceptionAdvice: debug
    com.cpvsn.rm.core.features.project.ProjectService: debug
    com.cpvsn.rm.core.features.task.TaskCreationService: debug
    com.cpvsn.rm.core.features.client.accountnote.ClientAccountNoteService: debug
    com.cpvsn.rm.core.aspect.PerformanceMonitorAdvice: debug
    com.cpvsn.rm.core.features.misc.company.CompanyBlacklistService: debug
    com:
      cpvsn:
        core:
          svc:
            dsproxy:
              listener:
                DefaultQueryLogger: off

cap.svc:
  rabbitmq:
    email:
      exchange: 'mail_us.direct'
      routing-key: 'mail_us'
    outreach-email:
      exchange: 'mail_outreach_us.direct'
      routing-key: 'mail_outreach_us'
    bulk_outreach-email:
      exchange: 'mail_batch_sg.direct'
      routing-key: 'mail_batch_sg'
  env:
    module: rm-web
  ds-proxy:
    enable-query-logger: true
    enable-slow-query-logger: true
    slow-query-threshold: 2000
  hashid:
    salt: 'rm-server-sg'
    min-hash-length: 6

auth:
  jwt:
    iss: us-db
    secret: JrvSR/LoyA9jzc7rE82wuWc6YPw7u/a34ctAAPbG3dQ=

oauth2-client:
  facebook:
    client-id: ***************
    client-secret: fbacc45af4b5c524ad939221e1afdc95
    proxy:
      host: proxy.capvision.com
      port: 8080
  google:
    client-id: ***********-tf33991pd4lm86nvla1vcr36eg9ubk87.apps.googleusercontent.com
    client-secret: eGlG-nIqKVP0FJbph89425ru
    proxy:
      host: proxy.capvision.com
      port: 8080
  linkedin:
    client-id: 863ecc7ahyz9a5
    client-secret: ukwwisrJL8dfc5xH
# ---------------------------------------------------------------------------------------------------------------------
com:
  cpvsn:
    portal:
      advisorSurveyUrl: https://qa-udb2.capvision.com/portal/#/expert_survey
      advisorPreCallUrl: https://qa-udb2.capvision.com/portal/#/b
      advisorPostCallUrl: https://qa-udb2.capvision.com/portal/#/after-task
      advisorBankAccountUrl: https://qa-udb2.capvision.com/portal/#/advisor/payment_info
      advisorUnsubscribeOutreachUrl: https://qa-udb2.capvision.com/portal/#/advisor/unsubscribe
      advisorUnsubscribeOutreachClickOnceUrl: https://qa-udb2.capvision.com/api/rm-portal-preview/path_auth/advisor_portal/unsubscribe/outreach
      advisor3rd-party-survey-url: https://qa-udb2.capvision.com/portal/#/expert_survey/3rd_party
      advisorDeclineUrl: https://qa-udb2.capvision.com/portal/#/advisor/decline
      advisorPaymentConfirmUrl: https://qa-udb2.capvision.com/portal/#/advisor/payment_confirm
      contactPreCallUrl: https://qa-udb2.capvision.com/portal/#/pick
      contactPostCallUrl: https://qa-udb2.capvision.com/portal/#/afeedback
      client-user-register-url: https://qa-compliance2.capvision.com/client-portal/#/register
      client-portal-advisor-recommendation-url: https://qa-compliance2.capvision.com/client-portal/#/recommendation
      client-portal-advisor-recommendation-internal-url: https://qa-udb2.capvision.com/client-portal/#/recommendation
      client-portal-aes-secret-key: gTRpAzm6Pn3xbrrKG+QqNg==
    es:
      indice:
        - index_enum: ADVISOR
          index_name: advisor
        - index_enum: PROJECT
          index_name: usdb_project
        - index_enum: COMPANY
          index_name: usdb_company
    conf:
      db_domain: https://qa-udb2.capvision.com
      db_api_endpoint: https://qa-udb2.capvision.com/api/rm-web-sg-preview
      w9_service_base_url: http://qa.api.capvision.com/py-track1099
      locations_version: 4
      client_account_note_default_notification_emails: <EMAIL>
      cors_origin: "*"
    data:
      teams:
        - leader: abarroukh
          members: anju,akeene,sparacionick,jbuen
        - leader: jluc
          members: swge,iscott,srogalski,mstockdale

slack:
  enable: true
  apps:
    # test (workspace:Capvision Platform)
    - app_enum: EXPERT_ACTIVITY_BOT
      id: A03CDBR3LMQ
      slack_bot_token: ********************************************************
      slack_signing_secret: 053fcaaf2e6c947b9b8f64c8d7064246
      client_secret: 22df79cf754e6d6412efa8d2dc62d9d1

google.sdk:
  credentials-file-location: 'google/credentials.json'
  proxy:
    type: HTTP
    host: 127.0.0.1
    port: 1081
#    host: proxy.capvision.com
#    port: 8080

sendgrid:
  api-key: '*********************************************************************'
  email-validation-api-key: '*********************************************************************'

list-wise:
  api-key: 'c549698d01864e924631a8a261c0cbb1'

whale-wisdom:
  shared-access-key: f5c7PbdwaqWMPfYyB593
  secret-access-key: LgFPrVznywSSuG0liT8vQlV6UCU1lurPo1wrDtJn

cap:
  bridge:
    us:
      base-url: https://qa-api.capvision.com/bridge-server-preview/api/
    cn:
      base-url: https://qa-api.capvision.com/bridge-server-preview/api/
    sea:
      base-url: https://qa-api.capvision.com/bridge-server-preview/api/
    default_conf:
      base-url: https://qa-api.capvision.com/bridge-server-preview/api/


virtual-incentives:
  user_name: capvision8260
  password: hQ4RprlxqCpxJVmOUzZ5ejWBqd7CysoLiCdtnNG84w9Ts7jkHX1loAfyutbBjZzx

exchange-rate:
  key: ************************

deepgram:
  base_url: https://api.deepgram.com
  listen_endpoint: /v1/listen
  token: abf322f0e05323371c93e0d85240f325cef9bdf8

contact-out:
  token: 'zNUMoYOfxc7FJMQVRd5C18uE'
  url: 'https://api.contactout.com/v1'

rocket_reach:
  token: '1978c82k13605003d239c5f210343fe0fdedfa92'
  url: 'https://api.rocketreach.co/api/v2'
