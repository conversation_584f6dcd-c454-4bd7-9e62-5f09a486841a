drop table if exists `client_file_template`;
CREATE TABLE if not exists `client_file_template` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `client_id` int(11) NOT NULL DEFAULT 0,
      `file_template_id` int(11) NOT NULL DEFAULT 0,
      `name` varchar(100) NOT NULL DEFAULT '',
      `type` varchar(50) NOT NULL DEFAULT '',
      `content_type` varchar(50) NOT NULL DEFAULT '',
      `create_by_id` int(11) NOT NULL DEFAULT 0,
      `update_by_id` int(11) NOT NULL DEFAULT 0,
      `delete_by_id` int(11) NOT NULL DEFAULT 0,
      `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
      `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      `delete_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
      PRIMARY KEY (`id`),
      KEY `idx_client_file_template_client_id` (`client_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- insert file template
INSERT INTO `file_template` (`name`, `type`, `content_type`, `media_type`, `file_name_template`, `template_url`, `create_by_id`) VALUES ('advisor_recommend_generation_im','EXCEL','ADVISOR_RECOMMEND','application/vnd.openxmlformats-officedocument.spreadsheetml.sheet','task_list(generation_im).xlsx','classpath:templates/advisor_recommend/generation_im_v1.xlsx',0);

-- insert client_file_template for client 'Generation IM' (id = 52)
INSERT INTO `client_file_template` (`client_id`, `file_template_id`, `name`, `type`, `content_type`, `create_by_id`, `update_by_id`) VALUES (52,LAST_INSERT_ID(),'advisor_recommend_generation_im','EXCEL','ADVISOR_RECOMMEND',1,1);

