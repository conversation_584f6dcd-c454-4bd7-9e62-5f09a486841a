drop table if exists `client_preference`;
CREATE TABLE if not exists `client_preference`
(
    `id`                     int(11)      NOT NULL AUTO_INCREMENT,
    `client_id`              int(11)      NOT NULL DEFAULT 0,
    `usage_excel_fields_str` varchar(500) NULL     DEFAULT NULL,
    `create_by_id`           int(11)      NOT NULL DEFAULT 0,
    `update_by_id`           int(11)      NOT NULL DEFAULT 0,
    `create_at`              timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at`              timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    KEY `idx_client_preference_client_id` (`client_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
