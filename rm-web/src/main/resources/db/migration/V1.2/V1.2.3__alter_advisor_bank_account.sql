ALTER TABLE `advisor_bank_account`
    ADD COLUMN `bank_name` VARCHAR(191) NOT NULL DEFAULT '' AFTER `advisor_id`,
    ADD COLUMN `account_name` VARCHAR(120) NOT NULL DEFAULT '' AFTER `bank_name`,
    ADD COLUMN `account_number` VARCHAR(191) NOT NULL DEFAULT '' AFTER `account_name`;

INSERT INTO `email_template` (`name`, `display_name`, `rank`, `content_type`, `is_system_template`, `is_private`, `reference_id`, `reference_type`, `subject_template`, `content_template`, `from_template`, `from_name_template`, `to_template`, `cc_template`, `bcc_template`, `tag_json_str`, `placeholder_categories_json_str`, `should_apply_thymeleaf_engine_to_content`, `create_by_id`, `update_by_id`) VALUES ('NOTIFY_DUPLICATE_ADVISOR_BANK_ACCOUNT', 'NOTIFY_DUPLICATE_ADVISOR_BANK_ACCOUNT', '0', 'NOTIFY_DUPLICATE_ADVISOR_BANK_ACCOUNT', '1', '0', '0', 'DEFAULT', 'Duplicated bank account was detected', '<div style=\"font-size: 11pt; font-family: Calibri;\">Hi,<br /><br />The bank account(with id = ${bank_account.id}}) of advisor <a href=\"${advisor.db_page_url_jit}\" target=\"_blank\" rel=\"noopener\">${advisor.full_name}</a> was just updated,</div>\n<div style=\"font-size: 11pt; font-family: Calibri;\">we believe this it is a duplicate of the bank account(with id = ${another_bank_account.id}), which belongs to <a href=\"${another_advisor.db_page_url_jit}\" target=\"_blank\" rel=\"noopener\">${another_advisor.full_name}</a>.</div>', '{{DB_SENDER_EMAIL}}', 'DB Sender', '{{CAPVISION_LEGAL_EMAIL_GROUP}}', '', '', '[]', '[\"ADVISOR\"]', '1', '0', '0');
