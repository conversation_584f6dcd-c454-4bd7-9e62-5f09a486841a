drop table if exists `currency`;
CREATE TABLE if not exists `currency`
(
    `id`           int(11)        NOT NULL AUTO_INCREMENT,
    `abbreviation` varchar(10)    NULL     DEFAULT NULL,
    `name`         varchar(50)    NOT NULL DEFAULT '',
    `symbol`       varchar(4)     NULL     DEFAULT NULL,
    `ratio_to_usd` decimal(10, 5) NOT NULL DEFAULT 0,
    `create_by_id` int(11)        NOT NULL DEFAULT 0,
    `update_by_id` int(11)        NOT NULL DEFAULT 0,
    `create_at`    timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at`    timestamp      NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

INSERT INTO `currency` (`name`, `abbreviation`, `symbol`, `ratio_to_usd`, `create_by_id`, `update_by_id`) VALUES ('USD', 'USD', '$', '1', 1, 1);
INSERT INTO `currency` (`name`, `abbreviation`, `symbol`, `ratio_to_usd`, `create_by_id`, `update_by_id`) VALUES ('RMB', 'RMB', null, '0.1539', 1, 1);
INSERT INTO `currency` (`name`, `abbreviation`, `symbol`, `ratio_to_usd`, `create_by_id`, `update_by_id`) VALUES ('JPY', 'JPY', null, '0.0095', 1, 1);
INSERT INTO `currency` (`name`, `abbreviation`, `symbol`, `ratio_to_usd`, `create_by_id`, `update_by_id`) VALUES ('GBP', 'GBP', null, '1.3697', 1, 1);
INSERT INTO `currency` (`name`, `abbreviation`, `symbol`, `ratio_to_usd`, `create_by_id`, `update_by_id`) VALUES ('EUR', 'EUR', null, '1.1765', 1, 1);
INSERT INTO `currency` (`name`, `abbreviation`, `symbol`, `ratio_to_usd`, `create_by_id`, `update_by_id`) VALUES ('SGD', 'SGD', null, '0.7129', 1, 1);
