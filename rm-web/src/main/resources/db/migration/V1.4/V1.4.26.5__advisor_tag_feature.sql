ALTER TABLE `advisor_tag_map_display_option`
    DROP INDEX `uq_advisor_tag_map_display_option_user_id_group_id`,
    ADD UNIQUE INDEX `uq_advisor_tag_map_display_option_user_id_group_id`
        (`user_id` ASC, `group_id` ASC, `relation_type_name` ASC);
;

drop table if exists `advisor_tag_map_job_exp`;
CREATE TABLE if not exists `advisor_tag_map_job_exp`
(
    `id`                int(11)      NOT NULL AUTO_INCREMENT,
    `advisor_id`        int(11)      NOT NULL DEFAULT 0,
    `matched_terms_str` varchar(191) NOT NULL DEFAULT '',
    `tag_id`            int(11)      NOT NULL DEFAULT 0,
    `create_at`         timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at`         timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uq_advisor_tag_map_job_exp_tag_id_advisor_id` (`tag_id`, `advisor_id`),
    <PERSON><PERSON>Y `idx_advisor_tag_map_job_exp_advisor_id` (`advisor_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;
