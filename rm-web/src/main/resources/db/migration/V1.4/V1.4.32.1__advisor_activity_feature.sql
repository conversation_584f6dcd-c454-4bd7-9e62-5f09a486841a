-- https://www.notion.so/capvision/AM-Dashboard-Activity-status-should-sort-f72cc5ad9fab4a03892ae37c40b1eb43
-- advisor_activity_view hes performance issue
drop view if exists `advisor_activity_view`;

drop table if exists `advisor_activity`;
CREATE TABLE if not exists `advisor_activity`
(
    `id`                  int(11)   NOT NULL AUTO_INCREMENT,
    `advisor_id`          int(11)   NOT NULL DEFAULT 0,
    `screened_proj_count` int(11)   NOT NULL DEFAULT 0,
    `selected_proj_count` int(11)   NOT NULL DEFAULT 0,
    `sent_proj_count`     int(11)   NOT NULL DEFAULT 0,
    `create_at`           timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at`           timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uq_advisor_activity_advisor_id` (`advisor_id`),
    KEY `idx_advisor_activity_update_at` (`update_at`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

ALTER TABLE `task_event`
    ADD INDEX `idx_task_event_create_at` (`create_at` ASC);
;
