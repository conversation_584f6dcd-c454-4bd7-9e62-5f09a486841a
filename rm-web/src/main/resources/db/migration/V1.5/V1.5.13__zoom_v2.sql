ALTER TABLE `task_arrangement`
    ADD COLUMN `alternative_hosts` varchar(800) NOT NULL DEFAULT '' AFTER `mail_info_json`,
    ADD COLUMN `co_host_roles` varchar(100) NOT NULL DEFAULT '' AFTER `alternative_hosts`,
    ADD COLUMN `enable_waiting_room` tinyint(1) DEFAULT NULL AFTER `co_host_roles`,
    ADD COLUMN `send_ai_summary` tinyint(1) DEFAULT NULL AFTER `enable_waiting_room`;

ALTER TABLE `zoom_meeting`
    ADD COLUMN `alternative_hosts` varchar(800) NOT NULL DEFAULT '' AFTER `join_url`,
    ADD COLUMN `alternative_host_join_url` varchar(500) NOT NULL DEFAULT '' AFTER `alternative_hosts`,
    ADD COLUMN `enable_waiting_room` tinyint(1) DEFAULT NULL AFTER `alternative_host_join_url`,
    ADD COLUMN `send_ai_summary` tinyint(1) DEFAULT NULL AFTER `enable_waiting_room`;

ALTER TABLE `zoom_meeting_participant`
    ADD COLUMN `delete_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00' AFTER `update_at`;