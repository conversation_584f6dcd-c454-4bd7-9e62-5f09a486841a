DROP TABLE IF EXISTS `resend_event`;
CREATE TABLE `resend_event` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `email_record_id` int(11) NOT NULL DEFAULT 0,
    `email` varchar(255) NULL DEFAULT '',
    `type` varchar(50),
    `event_time` TIMESTAMP NOT NULL,
    `email_id` varchar(255) NOT NULL,
    `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;