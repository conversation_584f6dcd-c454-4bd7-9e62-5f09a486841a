INSERT INTO `role` (`name`) VALUES ("SetTarget"), ("UpdateCallTracker");
INSERT INTO `permission` (`name`, `value`) VALUES ("SetTarget", "SetTarget"),("UpdateCallTracker", "UpdateCallTracker");

INSERT INTO `role_permission` (`role_id`, `permission_id`)
VALUES
  ((SELECT `id` FROM `role` WHERE `name` = "SetTarget"),
   (SELECT `id` FROM `permission` WHERE `name` = "SetTarget")),
  ((SELECT `id` FROM `role` WHERE `name` = "UpdateCallTracker"),
   (SELECT `id` FROM `permission` WHERE `name` = "UpdateCallTracker")),
   (1,(SELECT `id` FROM `permission` WHERE `name` = "SetTarget")),
   (1,(SELECT `id` FROM `permission` WHERE `name` = "UpdateCallTracker"));