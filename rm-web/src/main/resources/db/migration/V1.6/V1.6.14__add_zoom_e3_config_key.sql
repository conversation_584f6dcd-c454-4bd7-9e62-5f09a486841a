INSERT INTO `app_config_entry` (`key`, `value`, `type_str`, `value_type_str`, `value_hint_str`, `multiple`, `description`, `create_by_id`, `update_by_id`)
VALUES
    ('USING_ZOOM_E3_FOR_ALL_FI_CLIENTS', 'false', 'SYSTEM', 'BOOLEAN', 'OTH<PERSON>', '0', 'if true, all FI clients will be using Zoom Engineer 3 Account', '1', '1');

INSERT INTO `app_config_entry` (`key`, `value`, `type_str`, `value_type_str`, `value_hint_str`, `multiple`, `description`, `create_by_id`, `update_by_id`)
VALUES
    ('USING_ZOOM_E3_FI_CLIENT_IDS', '', 'OTHER', 'NUMBER', 'OTHER', '1', 'client ids which will use zoom enginee3 account', '1', '1');