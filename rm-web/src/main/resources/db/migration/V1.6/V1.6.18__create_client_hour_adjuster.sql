DROP TABLE if exists `client_hour_adjuster`;
CREATE TABLE if not exists `client_hour_adjuster`(
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `type` varchar(40),
    `client_id` int(11) NOT NULL DEFAULT 0,
    `adjuster` decimal(10,3) NOT NULL DEFAULT 1,
    `create_by_id` int(11) NOT NULL DEFAULT 0,
    `update_by_id` int(11) NOT NULL DEFAULT 0,
    `create_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    `delete_at` timestamp NOT NULL DEFAULT '0000-00-00 00:00:00',
    PRIMARY KEY (`id`),
    UNIQUE KEY `client_id_type` (`client_id`, `type`, `delete_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;