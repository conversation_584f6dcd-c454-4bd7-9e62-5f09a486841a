DROP TABLE if exists `project_real_time_notify_record`;
CREATE TABLE if not exists `project_real_time_notify_record` (
    `id` int(11) NOT NULL AUTO_INCREMENT,
    `project_id` int(11) NULL DEFAULT NULL,
    `task_id` int(11) NULL DEFAULT NULL,
    `type` varchar(255) NOT NULL DEFAULT '',
    `message` text NULL DEFAULT NULL,
    `create_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
