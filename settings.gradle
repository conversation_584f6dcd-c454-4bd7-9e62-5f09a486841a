pluginManagement {
    repositories {
        gradlePluginPortal()
        mavenLocal()
        switch (System.getenv('NEXUS_ENV')) {
            case "us_local":
                maven {
                    url usLocalNexusUrl
                    allowInsecureProtocol = true
                }
                break
            case "us_vpn":
                maven {
                    url usVpnNexusUrl
                    allowInsecureProtocol = true
                }
                break
            case "us_aws":
                maven {
                    url usNexusUrl
                    allowInsecureProtocol = true
                }
                break
            default:
                maven {
                    url usLocalNexusUrl
                    allowInsecureProtocol = true
                }
        }
    }
}

rootProject.name = 'rm-server'

include(':rm-core')
include(':rm-web')
include(':rm-portal')
include(':client-portal')
include(':rm-bridge')
include(':rm-bridge-outsource')
include(':rm-cron')
// try to speed up build process
//include(':rm-ndb')

// dependency substitution
if(properties.get('includeBuildCapvisionCoreRepoPath')?.trim()) {
    println("sustitute module('com.cpvsn:capvision-core' with '${properties.get('includeBuildCapvisionCoreRepoPath')}'")
    includeBuild(properties.get('includeBuildCapvisionCoreRepoPath')) {
        dependencySubstitution {
            substitute module('com.cpvsn:capvision-core') using project(':capvision-core')
        }
    }
}
if(properties.get('includeBuildCapvisionServiceRepoPath')?.trim()) {
    println("sustitute module('com.cpvsn:capvision-service' with '${properties.get('includeBuildCapvisionServiceRepoPath')}'")
    includeBuild(properties.get('includeBuildCapvisionServiceRepoPath')) {
        dependencySubstitution {
            substitute module('com.cpvsn:capvision-service') using project(':capvision-service')
        }
    }
}

if (System.env.HOME == "/Users/<USER>") {
//    includeBuild('/Users/<USER>/Workspace/code/capvision/oauth2-client') {
//        dependencySubstitution {
//            substitute module('com.cpvsn:oauth2-client') using project(':')
//        }
//    }

//    includeBuild('/Users/<USER>/Workspace/code/capvision/auth-extension') {
//        dependencySubstitution {
//            substitute module('com.cpvsn:auth-extension') using project(':')
//        }
//    }
//    includeBuild('/Users/<USER>/Workspace/code/capvision/whalewisdom-sdk-kt') {
//        dependencySubstitution {
//            substitute module('com.cpvsn:whalewisdom-spring-boot-starter') using project(':whalewisdom-spring-boot-starter')
//        }
//    }
//    includeBuild('/Users/<USER>/Workspace/code/personal/poi-extension') {
//        dependencySubstitution {
//            substitute module('com.cpvsn:poi-extension') using project(':')
//        }
//    }
//    includeBuild('/Users/<USER>/Workspace/code/capvision/crud-kit') {
//        dependencySubstitution {
//            substitute module('com.cpvsn:crud-kit') using project(':crud-kit')
//        }
//    }
}

